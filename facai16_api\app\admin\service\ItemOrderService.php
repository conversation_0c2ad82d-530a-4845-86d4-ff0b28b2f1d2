<?php
namespace app\admin\service;

use app\common\repository\ItemOrderRepository;
use app\common\utils\Result;

/**
 * 项目订单表
 */
class ItemOrderService
{
    /**
     * 项目订单列表
     * @param $params
     * @return array
     */
    public function getItemOrderLists($params): array
    {
        $where          = [];

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }


        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        
        if (!empty($params['item_name']))
        {
            $where[] = ['item_name', '=', $params['item_name']];
        }

        if (isset($params['item_status']) && is_numeric($params['item_status']))
        {
            $where[] = ['item_status', '=', $params['item_status']];
        }

        if (isset($params['is_test']) && is_numeric($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }


        $ItemOrderRepo  = new ItemOrderRepository();
        $data           = $ItemOrderRepo->paginates($where);

        return Result::success($data);
    }

}
