<?php
namespace app\admin\controller;

use app\admin\service\WithdrawService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 提现
 */
class Withdraw
{

    /**
     * 提现记录
     * @return Json
     */
    public function getWithdrawLists(): Json
    {

        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order',
            'name',
            'is_test',
            'status'
        ]);

        $WithdrawService   = new WithdrawService();
        $data              = $WithdrawService->getWithdrawLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    public function export(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order',
            'name',
            'is_test',
            'status'
        ]);

        $WithdrawService   = new WithdrawService();
        $data              = $WithdrawService->export($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 支付信息
     * @return Json
     */
    public function getWithdrawInfo(): Json
    {
        $id              = Request::param('id',0);
        $WithdrawService = new WithdrawService();
        $data            = $WithdrawService->getWithdrawInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 用户提现
     * @return Json
     */
    public function userWithdraw(): Json
    {
        $ids             = Request::param('ids',[]);
        $status          = Request::param('status',0);
        $remark          = Request::param('remark','');

        $WithdrawService = new WithdrawService();
        $data            = $WithdrawService->userWithdraw($ids, $status, $remark);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}