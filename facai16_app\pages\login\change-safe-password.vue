<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="page-bg-lg">
				<image src="/static/login/safeimg.png" mode="aspectFit" class="block w-309 h-308 f-pa top-95 right-16">
				</image>
			</view>
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">
					<view class="h-320">
						<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
							<template #left>
								<view>
									<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
								</view>
							</template>
							<template #center>
								<view class="color-#fff">
									修改支付密码
								</view>
							</template>
						</uv-navbar>
					</view>

				</view>


				<view class="flex1 f-pr c2">
					<scroll-view scroll-y="true" class="scroll-view ">
						<view class="mx-32  bg-#fff r-20 px-30 lh-77">
							<view class=" py-15 ">
								<view class="flex">
									<view class="w-180 fc">
										手机号
									</view>
									<view class="flex1 fc">
										<input type="tel" placeholder="请输入" :disabled="true"
											class="h-77 c2 lh-32 w-full" v-model="phone" />
									</view>
								</view>
							</view>
							<uv-gap height="1" bgColor="#ECF0F4"></uv-gap>
							<view class="py-15 ">
								<view class="flex">
									<view class="w-180 fc">
										验证码
									</view>
									<view class="flex1 fc">
										<input type="number" placeholder="请输入验证码" class="h-77 c2 lh-32 w-200"
											v-model="smscode" maxlength="6" />
									</view>
									<view>
										<button v-if="disabled" :class="{'bg-op-30':disabled}" :changeText="changeText"
											@tap="getCode"
											class="px-0 w-168 h-77 r-90 bg-#407CEE   t-30 color-#fff  fcc">{{tips}}</button>
										<button v-else :class="{'bg-op-30':disabled}" :changeText="changeText"
											@tap="getCode"
											class="px-0 w-168 h-77 r-90 bg-#407CEE  t-30  color-#fff fcc">{{tips}}</button>
									</view>
								</view>
							</view>
							<uv-gap height="1" bgColor="#ECF0F4"></uv-gap>
							<view class=" py-15 ">
								<view class="flex">
									<view class="w-180 fc">
										登录密码
									</view>
									<view class="flex1 fc">
										<input type="number" v-model='password' password maxlength="6"
											placeholder="请输入6位数字密码" class="h-77 c2 lh-32 w-full" value="" />
									</view>
								</view>
							</view>
							<uv-gap height="1" bgColor="#ECF0F4"></uv-gap>
							<view class=" py-15 ">
								<view class="flex">
									<view class="w-180 fc">
										确认密码
									</view>
									<view class="flex1 fc">
										<input type="number" v-model='confirmPassword' password maxlength="6"
											placeholder="请输入6位数字密码" class="h-77 c2 lh-32 w-full" value="" />
									</view>
								</view>
							</view>
						</view>
						<view class="h-164">

						</view>

						<view class="mx-60">
							<view class="btn-full fcc" @click="submit">
								提交
							</view>
						</view>


						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>



				</view>


			</view>
		</view>
		<fixed-kefu></fixed-kefu>
		<uv-code :seconds="seconds" @end="end" @start="start" ref="code" @change="codeChange" startText="发送"
			change-text="xS"></uv-code>
	</view>
</template>


<script>
	import {
		updatePayPassword,
		resetSms
	} from '@/api/auth.js'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import {
		mapState
	} from 'vuex'
	export default {
		mixins: [needAuth],
		data() {
			return {
				password: '',
				confirmPassword: '',
				smscode: '',
				disabled: false,
				seconds: 60,
				tips: '',
			};
		},
		computed: {
			...mapState(['userInfo']),
			phone() {
				return this.userInfo.phone2 || ''
			}
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.code.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					resetSms({
						phone: this.userInfo.phone
					}).then((res) => {
						if (res.code == 0) {
							uni.hideLoading();
							// 这里此提示会被this.start()方法中的提示覆盖
							uni.$uv.toast('验证码已发送');
							// 通知验证码组件内部开始倒计时
							this.$refs.code.start();
						}
					}).finally(() => {
						uni.hideLoading()
					})
				} else {
					uni.$uv.toast('倒计时结束后再发送');
				}
			},
			end() {
				this.disabled = false
				uni.$uv.toast('倒计时结束');
			},
			start() {
				uni.$uv.toast('倒计时开始');
				this.disabled = true
			},
			async submit() {
				let err = ''
				if (!this.password || this.password.length < 6) {
					err = '请输入6位数字密码'
				} else if (!this.confirmPassword) {
					err = '请再次输入密码'
				} else if (this.confirmPassword != this.password) {
					err = '密码不一致'
				} else if (!this.smscode) {
					err = '请输入验证码'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await updatePayPassword({
							password: this.password,
							confirmPassword: this.confirmPassword,
							smscode: this.smscode
						})
						if (resData.code == 0) {
							uni.showToast({
								title: '支付密码已修改'
							})
							uni.navigateBack()
						}
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			}
		},
	}
</script>
<style lang="scss" scoped>
	.page-bg {
		background: #F6F9FC;
		background-size: 100% auto;
	}

	.page-bg-lg {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 441rpx;
		background: linear-gradient(180deg, #407CEE 0%, #F6F9FC 100%);
		opacity: 0.69;
	}
</style>