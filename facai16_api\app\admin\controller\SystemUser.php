<?php
namespace app\admin\controller;

use app\admin\service\SystemUserService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 系统用户
 */
class SystemUser
{

    /**
     * @return Json
     */
    public function getSystemUserLists(): <PERSON><PERSON>
    {
        $username           = Request::param('username','');

        $SystemLogService   = new SystemUserService();
        $data               = $SystemLogService->getSystemUserLists($username);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 用户信息
     * @return Json
     */
    public function getSystemUserInfo(): Json
    {
        $id                 = Request::param('id',0);
        $SystemLogService   = new SystemUserService();
        $data               = $SystemLogService->getSystemUserInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加用户
     * @return Json
     */
    public function addSystemUser(): Json
    {
        $param              = Request::only([
            'username',
            'password' => '123456',
        ]);

        $SystemLogService   = new SystemUserService();
        $data               = $SystemLogService->addSystemUser($param['username'], $param['password']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新用户
     * @return Json
     */
    public function updateSystemUser(): Json
    {
        $param              = Request::only([
            'username',
            'password' => '123456',
            'id' => 0
        ]);

        $SystemLogService   = new SystemUserService();
        $data               = $SystemLogService->updateSystemUser($param['id'], $param['username'], $param['password']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除用户
     * @return Json
     */
    public function deleteSystemUser(): Json
    {
        $id                = Request::param('id','');
      
        $SystemLogService   = new SystemUserService();
        $data               = $SystemLogService->deleteSystemUser($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



}