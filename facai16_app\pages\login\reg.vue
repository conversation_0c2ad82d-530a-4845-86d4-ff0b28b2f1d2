<template>
	<view>
		<view class="page-login">
			<view class="fcc-h pt-108">
				<image src="/static/login/logo.png" mode="aspectFit" class="w-140 h-100 block"></image>
				<view class="t-48 fm lh-62 color-#fff">
					Mistral AI
				</view>
			</view>
			<view class="h-70"></view>

			<view class="mx-34 r-20 border-1 border-solid border-#fff bg-#fff bg-op-10 px-36">
				<view class="fc-bet h-105">
					<view class="tit">
						注册账号
					</view>
					<!-- <view class="fc" @click="$refs.popSelect.open()">
						<image src="/static/login/118.png" mode="aspectFit" class="block mr-12 size-32"></image>
						<text class="t-30 lh-42 color-#fff">
							{{list[index]}}
						</text>
					</view> -->
				</view>


				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-tel.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1  mr-20">
						<input type="text" placeholder="请输入昵称" v-model="username"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff DIN">
					</view>
				</view>


				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-tel.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1  mr-20">
						<input type="number" maxlength="11" placeholder="请输入手机号" v-model="phone"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff DIN">
					</view>
				</view>

				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-pwd.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="text" password placeholder="请输入密码" v-model="password"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>
				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-pwd.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="text" password placeholder="请再次输入密码" v-model="password2"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>
				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-pwd.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="number" maxlength="6" password placeholder="请输入您的交易密码(6位数字)" v-model="withdraw"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>
				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-pwd.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="number" password maxlength="6" placeholder="请再次输入交易密码(6位数字)" v-model="withdraw2"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>
				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-yqm.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="text" v-model="invite" placeholder="请输入您的邀请码"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>
				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-safe.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 ">
						<input type="number" v-model="code" placeholder="请输入验证码"
							placeholder-style="color:#fff;font-size:31rpx" maxlength="6"
							class="t-36 w-full lh-116  color-#fff">
					</view>
					<view class="mr-14">
						<uv-button type="primary" :text="duration?`${duration}(s)`:'发送验证码'" :disabled="duration"
							shape="circle" @click="getVerifyCodeHandle"></uv-button>
					</view>
				</view>

				<view class="h-20"></view>


				<view class="btn-full fcc !r-20" @click="submit">
					立即注册
				</view>

				<view class="h-36"></view>

			</view>


			<view class="h-50"></view>
			<view class="">
				<view class="fcc">
					<view @click="agree=!agree">
						<image v-if="agree" src="/static/check1-on.png" mode="aspectFit" size-32 block></image>
						<image v-else src="/static/check1.png" mode="aspectFit" size-32 block></image>
					</view>
					<view class="t-22 color-#fff ml-8">
						<text @click="agree=!agree">我已阅读并同意</text>
						<navigator url="/pages/setting/article" class="inline-block">
							<text class="color">
								《服务协议》
							</text>
							<text class="color">《隐私协议》</text>
						</navigator>
					</view>
				</view>
				<view class="h-30"></view>
				<view class="btn-area"></view>
			</view>
		</view>


		<pop-select ref="popSelect" title="切换语言" :list="list" @select="select"></pop-select>
		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		noNeedAuth
	} from '@/utils/router-mixin.js'
	import {
		register,
		getRegVerifyCode
	} from '@/api/auth.js'
	export default {
		mixins: [noNeedAuth],
		data() {
			return {
				agree: false,
				index: 0,
				list: ['中文', 'English'],
				invite: '',
				username: "",
				password: "",
				password2: "",
				code: "",
				phone: "",
				withdraw: "",
				withdraw2: "",
				duration: 0,
				timer: null
			};
		},
		onShow() {
			if (uni.getStorageSync('inviteCode')) {
				this.invite = uni.getStorageSync('inviteCode')
				this.$forceUpdate()
			}
		},
		methods: {
			select(e) {
				this.index = e
			},
			async submit() {
				let err = ''
				if (!this.phone || !(/^\d{11}$/.test(this.phone))) {
					err = '请输入有效手机号'
				} else if (!this.username) {
					err = '请输入账户昵称'
				} else if (!this.password) {
					err = '请输入登录密码'
				} else if (!this.password2) {
					err = '请再次输入登录密码'
				} else if (this.password != this.password2) {
					err = '登录密码不一致'
				} else if (!this.withdraw) {
					err = '请输入交易密码'
				} else if (!this.withdraw2) {
					err = '请再次输入交易密码'
				} else if (this.withdraw != this.withdraw2) {
					err = '交易密码不一致'
				} else if (!this.code || !(/^\d{6}$/.test(this.code))) {
					err = '请输入验证码'
				} else if (!this.agree) {
					err = '请阅读并同意用户协议'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await register({
							phone: this.phone,
							code: this.code,
							invite: this.invite,
							password: this.password,
							withdraw: this.withdraw,
							username: this.username,
						})
						uni.setStorageSync("Accept-Token", resData.data.token);
						this.$store.dispatch('afterLogin');
						uni.navigateTo({
							url: '/pages/index/index',
						})
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			},
			async getVerifyCodeHandle() {
				let err = ''
				if (!this.phone || !(/^\d{11}$/.test(this.phone))) {
					err = '请输入有效手机号'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				}
				try {
					uni.showLoading({
						title: '发送中',
					});
					await getRegVerifyCode({
						phone: this.phone
					})
					this.duration = 60
					timer = setInterval(() => {
						if (!this.duration) {
							clearInterval(this.timer)
							timer = null
							return
						}
						this.duration--
					}, 1000)
				} catch (e) {} finally {
					uni.hideLoading()
				}
			}
		},
		onHide() {
			if (this.timer) {
				clearInterval(this.timer)
				this.duration = 0
				this.timer = null
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-login {
		background: url(/static/article/bg.jpg) no-repeat #000232;
		background-size: 100% auto;
	}

	.tit {
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 40rpx;
		color: #FFFFFF;
		line-height: 56rpx;
		text-shadow: 2rpx 2rpx 2rpx rgba(0, 0, 0, 0.8);
		text-align: right;
		font-style: normal;
	}

	.input-row {

		backdrop-filter: blur(29rpx);
	}
</style>