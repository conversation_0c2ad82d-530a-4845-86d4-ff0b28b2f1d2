<?php
namespace app\admin\controller;

use app\admin\service\PaymentService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 支付订单
 */
class Payment
{

    /**
     * 支付记录
     * @return Json
     */
    public function getPaymentLists(): Json
    {

        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order_no',
            'status',
            'is_test',
            'style',
            'class_id'
        ]);

        $PaymentService   = new PaymentService();
        $data             = $PaymentService->getPaymentLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    public function export(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order_no',
            'status',
            'is_test',
            'style'
        ]);
        $PaymentService   = new PaymentService();
        $data             = $PaymentService->export($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 支付记录信息
     * @return Json
     */
    public function getPaymentInfo(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentService();
        $data           = $PaymentService->getPaymentInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加支付记录
     * @return Json
     */
    public function userRecharge(): Json
    {
        $ids             = Request::param('ids',[]);
        $status          = Request::param('status',0);
        $remark          = Request::param('remark','');

        $PaymentService  = new PaymentService();
        $data            = $PaymentService->userRecharge($ids, $status, $remark);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}