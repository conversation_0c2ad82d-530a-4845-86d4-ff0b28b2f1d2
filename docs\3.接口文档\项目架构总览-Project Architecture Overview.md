# Facai16 项目架构总览

## 项目概述

Facai16是一个完整的金融投资平台，包含三个主要模块：
- **API后端** (facai16_api) - PHP ThinkPHP6框架
- **管理后台** (facai16_admin) - Vue3 + Element Plus
- **移动端APP** (facai16_app) - UniApp跨平台应用

## 技术栈总览

### 后端技术栈
- **框架**: ThinkPHP 6.1
- **数据库**: MySQL + Redis
- **队列**: Think Queue (Redis)
- **文件存储**: 阿里云OSS
- **短信服务**: 聚合数据、九鼎阁
- **身份验证**: 腾讯云、阿里云、聚合数据
- **支付**: 多种支付接口
- **其他**: JWT认证、二维码生成、Excel处理

### 前端管理后台技术栈
- **框架**: Vue 3.2
- **UI库**: Element Plus 2.9
- **路由**: Vue Router 4.0
- **状态管理**: Vuex 4.0
- **HTTP客户端**: Axios
- **富文本编辑器**: WangEditor
- **图标**: Element Plus Icons

### 移动端APP技术栈
- **框架**: UniApp (Vue3)
- **UI库**: UV-UI 1.1
- **状态管理**: Vuex
- **工具库**: Lodash、Moment.js、Crypto-js
- **样式**: UnoCSS
- **调试**: VConsole

## 核心业务功能

### 用户管理
- 用户注册/登录
- 实名认证
- 银行卡管理
- 用户等级体系
- 邀请推广体系

### 投资理财
- 项目投资
- 收益计算
- 投资记录
- 风险评估

### 资金管理
- 充值/提现
- 转账功能
- 资金记录
- 余额宝功能

### 团队管理
- 团队层级关系
- 团队收益
- 推广奖励
- 团队数据统计

### 系统管理
- 权限管理
- 配置管理
- 日志管理
- 数据统计
