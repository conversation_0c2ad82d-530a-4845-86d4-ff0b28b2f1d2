<?php

namespace app\common\strategy\sms;

/**
 * 短信
 * Class SmsContext
 * @package app\common\strategy\sms
 */
class SmsContext
{
    private  $strategy;

    // 构造函数通过传入策略实例来选择具体的短信发送策略
    public function __construct(SmsInterface $strategy)
    {
        $this->strategy = $strategy;
    }

    // 使用当前选择的策略来发送短信
    public function sendMessage($phone, $message)
    {
       return $this->strategy->send($phone, $message);
    }
}
