<?php

namespace app\common\command;

use app\admin\service\util\RelationTrait;
use app\common\utils\File;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Config;


/**
 * 测试
 * Class Backup
 * @package app\common\command
 */
class Test extends Command
{
    use RelationTrait;

    protected function configure()
    {
        // 指令配置
        $this->setName('Test')->setDescription('the Test command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        $settings = File::listFiles(config_path('more'));

        foreach ($settings as $val)
        {
            Config::load('more/'. $val['name'], $val['name']);
        }
    }

}

