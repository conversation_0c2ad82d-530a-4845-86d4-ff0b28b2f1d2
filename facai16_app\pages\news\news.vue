<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							新闻资讯
						</view>
					</template>

				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true"
					:refresher-triggered="refresherTriggered" @refresherrefresh='onRefresh' scroll-y="true"
					refresher-background='#ffffff00' style="height: calc(100vh - 50px);">
					<NewsItem v-for="(item,index) in list" :key="item.id" :item='item' />
					<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
					<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
						@loadmore="scrollHandle" />
					<view class="btn-area"></view>
				</scroll-view>
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		getArticle
	} from '@/api/home.js'
	import NewsItem from './news-item.vue'
	export default {
		components: {
			NewsItem
		},
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		onShow() {
			this.getRecord()
		},
		methods: {
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await getArticle({
						class_id: 2,
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>