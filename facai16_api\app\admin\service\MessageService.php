<?php
namespace app\admin\service;

use app\common\jobs\MessageJob;
use app\common\repository\MessageRepository;
use app\common\repository\UserRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 站内信
 */
class MessageService
{
    /**
     * 站内信列表
     * @param $params
     * @return array
     */
    public function getMessageLists($params): array
    {

        $where      = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (isset($params['is_read']) && is_numeric($params['is_read']))
        {
            $where[] = ['is_read', '=', $params['is_read']];
        }

        $MessageRepo = new MessageRepository();
        $data        = $MessageRepo->paginates($where,'', $params['limit']);

        return Result::success($data);
    }

    /**
     * 站内信信息
     * @param $id
     * @return array
     */
    public function getMessageInfo($id): array
    {
        $MessageRepo = new MessageRepository();
        $data        = $MessageRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加站内信
     * @param $params
     * @return array
     */
    public function addMessage($params): array
    {

        if ($params['users'] == 'all')
        {
            $UserRepo = new UserRepository();
            $users    = $UserRepo->selectByCondition([],'phone');
            $users    = array_column($users,'phone');
        }
        else
        {
            $users    = array_filter(explode(',', $params['users']));
        }

        foreach ($users as $user)
        {
            $UserRepos  = new UserRepository();
            $info       = $UserRepos->findByCondition(['phone' => $user]);

            if (empty($info))
            {
                continue;
            }

            $push   = [
                'uid'         => $info['id'],
                'username'    => $info['username'],
                'phone'       => $info['phone'],
                'is_test'     => $info['is_test'],
                'title'       => $params['title'],
                'content'     => $params['content'],
            ];

            queue(MessageJob::class, $push);
        }

        return Result::success();
    }

    /**
     * 更新站内信
     * @param $params
     * @return array
     */
    public function updateMessage($params): array
    {

        $time                   = Request::time();
        $params['update_time']  = $time;


        $MessageRepo = new MessageRepository();
        $res         = $MessageRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除站内信
     * @param $id
     * @return array
     */
    public function deleteMessage($id): array
    {
        $MessageRepo = new MessageRepository();
        $res         = $MessageRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

}
