<?php
namespace app\common\error;

/**
 * 后台错误信息常量
 * Class Common
 * @package app\common\error
 */
class Common
{

    /**
     * 保存失败
     */
    const SAVE_FAILED   = 10001;

    /**
     * 删除失败
     */
    const DELETE_FAILED = 10002;

    /**
     * 输入数据过多
     */
    const INPUT_DATA_TOO_MUCH = 10003;

    /**
     * 锁定状态不允许操作
     */
    const LOCK_NO_OPERATE = 10004;

    /**
     * 用户名错误
     */
    const USERNAME_ERROR = 10005;

    /**
     * 用户ID错误
     */
    const USER_ID_ERROR = 10006;

    /**
     * 已存在相同数据
     */
    const SAME_DATA = 10007;

    /**
     * 查无数据
     */
    const NO_DATA = 10008;

    /**
     * 没有需要处理的数据
     */
    const NO_DATA_TO_DEAL = 10009;

    /**
     * 部分更新失败
     */
    const PART_UPDATE_FAILED = 10010;

    /**
     * 参数不能为空
     */
    const PARAM_EMPTY = 10018;

    /**
     * 用户不存在
     */
    const USER_DOES_NOT = 10019;

    /**
     * 数据已存在
     */
    const USER_DATA_YES = 10020;

    /**
     * 禁用失败
     */
    const DISABLE_FAILED = 10021;

    /**
     * 你的操作过于频繁
     */
    const OPERATION_TOO_FREQUENTLY = 10022;

    /**
     * 接口地址错误
     */
    const INTERFACE_ADDRESS_ERROR = 10023;

    /**
     * 数据不存在
     */
    const USER_DATA_NO = 10024;

    /**
     * 数据已被审核
     */
    const DATA_BEEN_CHECK = 10025;

    /**
     * 非法请求
     */
    const ILLEGAL_REQUEST = 10026;

    /**
     * 操作失败
     */
    const OPERATION_FAILED = 10027;

    /**
     * 上传失败
     */
    const UPLOAD_FAILED    = 10028;
}