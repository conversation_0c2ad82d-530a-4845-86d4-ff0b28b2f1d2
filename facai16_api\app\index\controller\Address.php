<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\AddressService;
use think\facade\Request;
use think\response\Json;


/**
 * 用户地址
 */
class Address
{

    /**
     * 地址信息
     * @return Json
     */
    public function info(): <PERSON><PERSON>
    {
        $AddressService = new AddressService();
        $result         = $AddressService->info();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 用户地址列表
     * @return Json
     */
    public function lists(): <PERSON><PERSON>
    {
        $AddressService = new AddressService();
        $result         = $AddressService->lists();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 编辑地址
     * @return Json
     */
    public function edit(): Json
    {
        $params         = Request::only([
            'address_name'  => '',
            'address_phone' => '',
            'address_city'  => '',
            'address_place' => '',
            'id'            => 0
        ]);


        $AddressService = new AddressService();
        $result         = $AddressService->edit($params);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 添加
     * @return Json
     */
    public function add(): Json
    {
        $params         = Request::only([
            'address_name'  => '',
            'address_phone' => '',
            'address_city'  => '',
            'address_place' => '',
        ]);

        $AddressService = new AddressService();
        $result         = $AddressService->add($params);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 删除
     * @return Json
     */
    public function delete(): Json
    {
        $params         = Request::only(['id']);
        $AddressService = new AddressService();
        $result         = $AddressService->delete($params['id']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


}