<?php
return [

    // 默认缓存驱动
    'default' => env('strategy.pay', 'HengTongsPay'),

    // 服务策略
    'strategy' => [

        // 三方支付
        'ThreePartPayment' => [
            'channelCode'   => '8899',
            'userCode'      => '100015',
            'url'           => 'http://54.168.244.135:8090/zjdr/apis/pay/get',
            'sign'          => 'b072ede19587a12af79106409fefb7e6',

        ],

        //
        'HengTongsPay'     => [
            'url'          => 'http://pay.hengtongstopays.com/api/pay/unifiedOrder',
            'mch_id'       => 'M1739627805',
            'app_id'       => '67b09d1de4b0cd86c91a7cf9',
            'sign'         => 'LQ8BQ9ppAkt9YEkEdblJIXe2wQxKClODcsLjtsfbg2cdqiiHvvHhihbcVLlohu55ECGwNeM5xUAOdBYFOBBPXKF2chjMj4u8WNOsq3CJ7Dcg24kxl8o0DGGzz3r0yLy0',
            'channel'      => 'itpay',
        ],

        //海洋-支付宝
        'HaiYangPay'        => [
            //下单网关
            'url'       => 'https://api.dingshanzhibo.com/api',
            //商户秘钥
            'sign'      => 'c0f2d6dab92abcf4a21e639cbad526ce',
            //通道编码
            'channel'   => '135889',
            //商户号
            'mch_id'    => 'DLCQJ-1',
        ],

        //大力支付
        'DarLiePay'         => [
            //下单网关
            'url'       => 'https://pay.darlie-pay.com/api/pay',
            //商户号
            'mch_id'    => 'drenjie-zfb22',
            //商户秘钥
            'sign'      => '97iL2CaPO19laP',
            //通道编码
            'channel'   => '2',

        ],

        //币通支付
        'BingTongPay'  => [
            //商户号
            'mch_id'    => 'bitong04',
            //下单网关
            'url'       => 'https://a.f-dsn.com/api/personnelfiles/preorder/addorder',
            //通道编码
            'sign'      => 'T68288HT640F84Z22444840T68L8VR',
            //通道编码
            'channel'   => 'quickpay',
        ],
        //币通支付银联
        'BiTongPayYL'  => [
            //商户号
            'mch_id'    => '2260033127',

            'app_id'    => '4ef6700b55d640328686d6795704d4e5',
            //下单网关
            'url'       => 'https://xspay.xusunpay.com/api/payAPI/create',
            //通道编码
            'sign'      => '6RW0JYZHTOXHUGSQCGCSKTKVGKJY7ZOKP2UAL9VNARDTEJGVCEY0YYRSIAPEQKMHHBQTMEQTIKUAYQFFUDWTI6DVKWJ49AHJP703YXDF2CFXD3XTPWVSNOPWA9MIUWLZ',
            //通道编码
        ],

        //星空支付
        'XingKongPay'  => [
            //商户号
            'mch_id'    => '9440',

            //下单网关
            'url'       => 'http://api.huyazf.com/v1/pay',
            //通道编码
            'sign'      => 'puDKw38N4FjYMhocHsr2WbG7lX0gxaUd',

        ],


        //星联支付-支付宝
        'XingLianPayZfb'  => [
            //APPID
            'app_id'    => 'mi7brfatxajmu2wp8j',
            //通道编码
            'sign'      => '9643208010494cc082cb5df770e6dec2',
            //下单网关
            'url'       => 'http://company-api.xinglianzhifu.cc/order/pay.html',
        ],



        //星联支付-微信
        'XingLianPayVx'  => [
            //APPID
            'app_id'    => 'vdm6xpvlyu4vlcd3rt',
            //通道编码
            'sign'      => 'c7e692ab4d244ab4b3f82efee6c98994',
            //下单网关
            'url'       => 'http://company-api.xinglianzhifu.cc/order/pay.html',
        ],

        //星联支付-银联
        'XingLianPayYL'  => [
            //APPID
            'app_id'    => '7h0mzndoak11dhxsbb',
            //通道编码
            'sign'      => '81211a51e25f4271b0246b2005bf5287',
            //下单网关
            'url'       => 'http://company-api.xinglianzhifu.cc/order/pay.html',
        ],


        //星联支付-云闪付
        'XingLianPayYSF'  => [
            //APPID
            'app_id'    => '84ybkbs2x6cv1xh68t',
            //通道编码
            'sign'      => 'ca5cf7d7302a48c3a1607ac21685512b',
            //下单网关
            'url'       => 'http://company-api.xinglianzhifu.cc/order/pay.html',
        ],


        'DfPay'         => [
            //APPID
            'mch_id'    => '716',
            //通道编码
            'sign'      => 'a49acc86dae188f467',
            //下单网关
            'url'       => 'https://yyds.qiangivn.com/pay/',
            //通道编码
            'channel'   => 'alipay_jsapi',
        ],


        'BingTongPay2' => [
            //APPID
            'mcd_id'     => '10052',
            //通道编码
            'sign'       => 'd7a780fc56f74b6ef71b0729e4463a2b',
            //下单网关
            'url'        => 'http://jiuzhou.1688888.vip/api/newOrder',
            //通道编码
            'channel'   => '336',
        ],


        'LeLePay'   => [

            //APPID
            'mch_id'    => '100042',
            //通道编码
            'sign'      => '30bb24b7f085023bf3ab3a5433a88487',
            //下单网关
            'url'       => 'http://**************/zjdr/apis/pay/query',
            //通道编码
            'channel'   => '0000',

        ],

        'PIAOZONGZHIFUBAO'   => [

            //APPID
            'mch_id'    => '10056',
            //通道编码
            'sign'      => '657E1DD5063D54DA25AAA785AB939854',
            //下单网关
            'url'       => 'https://jiashun.flaresec.com/order/create/',
            //通道编码
            'channel'   => '7',

        ],


        'XingLianPay'  => [
            //APPID
            'app_id'    => 'vdm6xpvlyu4vlcd3rt',
            //通道编码
            'sign'      => 'c7e692ab4d244ab4b3f82efee6c98994',
            //下单网关
            'url'       => 'http://company-api.xinglianzhifu.cc/order/pay.html',
        ],


        'QiuTianPay'    => [
            //APPID
            'mch_id'    => '*********',
            //通道编码
            'sign'      => 'p26ocx32l3616i2q8w07qbf51ciyj2rx',
            //下单网关
            'url'       => 'https://zhuoyuemon.meisuobudamiya.net/Pay_Index.html',
        ],

        //IP白名单：*************
        'ThsFastPay'    => [
            'mchId' => 'M202504061718575902',
            'sign'  => 'QcKIJmvWDvSqsl13g3CtOwZt8iH9X9YR6rTzYshX0wI7rVPpH9f1Xo2UT4CKsc',
            'url'   => 'http://shanyinapi.thsfastplatformapi.com/pay/api/v1/unifiedOrder',
        ],

        'FeiCuiPay'     => [
            'mch_id'    => '*********',
            'key'       => '518bc22cf7dc9a0e662389d8652af2c1',
            'url'       => 'http://mi.feicuipay.com/optimus/collect/placeOrder',
        ],


        'Epay'          => [
            //支付接口地址
            'url'                   => 'http://************/',
            //商户ID
            'pid'                   => '1013',
            //平台公钥
            'platform_public_key'   => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnV+Ovzso13nYbQQhI+zKLHJth8ZucnNCJiY3MT2Pk5zGwN6l3n+TCLYogqlmUZmrryLGNIKk1XJmZDLUmo6twif13ngE7iUH6VnQmwpQxoIrw8dbGwpCstTkXipEqtPzqeaHIEks9ap95gPJgAAMJKUY6rFsVkC3aV9eKuP2fQE9JEpFbLDbfLiqmyV8lEdQu8THXEZWdw9fHs82WEdy6K4N+TSAwcU98IL92Xt9aaHRvbkGHzxStNjGeLpgeY46U7iszRGoRxwNqrYpNkMzzP3pouygBxiLQaxA1tL4U3QBC9Bk+4d0xVnCKcD1Ibs3V6B4Ej/keeuTGulwVwalLwIDAQAB',
            //商户私钥
            'merchant_private_key'  => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+cRG4BscBcPkeylptUjVrPzDVyQnuhNo2moJMIYf2J6XHyljXlQeIyoiIzT9xKjr11LjbMCVHVcWkApBNtChxvNJhV0ZAZ2oMWl3+wl8Zcg3Zc5FswXGVY3ISW3wvYl3nVtiogCQfIbxLfosEBTGsQvP6OJ0m2+ERhSqog3auAGx7PXSxLeAnQhDzLXlG6I/+vWghJ6iC4ZASOAkrPG/HdSrTILKRhzW5MkyvMl2BUentctSSy+is5gsI2+yIrePAhYdoha4HwOLU+/jf5w+u72dKp21pZUZr9w+nwwkmqdWyled/75VQWR5bUwBUtAzQM1b3nXcit+vcmlWpg8lvAgMBAAECggEBAJmTMQjIXl9pLVyJLlcOfomYEde1Ic85T4fNHdorCCuXWXc+VFq1kbfcLDK14b1uotFv1AreTpGyN4nFj306FYtzC+z3pm11d+2ySi6RrIMmmMiNC+86KFCiO3FWWVUU7EkMnfd0Lqz0VZZD2/NIBQC3lWxpj0Gw+1VXoEj4ixJRXObsZ6/VCYAYXbVpF3WXHVYivk3sUlidaqjs1nDxC7e8Ctx3mBtRJDT/HPsXtSEitmGRqVEWJ9cbVQFdjtO1mOsSlLa3DY/xkEWpES5kLRUnHuEhR65dW6LaJRzJQhh2xR0XGhnhEijBkT17/dwq+ZU411rEihkNq+SdzvmJFoECgYEA4yQ+7bIhS6/waJ08kZI6CpSVNal8V2FhjBbHDSw3vPzS8L4xqQtSk9wi+2nTasNvSsmf5tjAce80EC5ufTiGewPC7rdK8kYvgmG7VTW5q6l80cfYFyA8nSaAYouDjCck6YrTmzX2kv36qoxbWyE4h8LavfOMOWWQLDyqiLSCH88CgYEA1qMpvdtscaSF2zoOQUGn3u78anWuIATVxAgJtLHvX1PQZWI9efxGPzs8+eVQJOkm1fO+QqCAKJVb+JovWr/corwkfh9BPgXo6iWHz7y2kouaa4rPrpAMkQ0Di+9QcAmFxHIaIahsSyzAqFcdGfyY2wfGBzxfko65CCVtIjQBhGECgYB1rDFtsvrM2zkQ+WSHhdnL4W3rfwKLwkdm6+XzTRUTM2eUkPPpP5RoRYNZ/Fkq1lSxygKs9V29qM6wE13qQX5p/GjggtSw7utuAHOskUi/Re8EPyQT1v+P6oJYiy57TUwyMVmu7yXuwOU7tbseWveOXcQ05BehJJysTkGK0PkYwQKBgDB1LdtbSqRtjHruJIG/HPNL6VjvsJwx+0ECv444Dd7rwUTiZRbBSerEqQOSLgHnUYlEOlQ4gDapYdZCmSZX4qwq5eJDlCs4klkql/rilCWJtxdIg5LZmry0v3Q8V2C0BatQR24J5EkklWpsIHnZeXkZzC+eBcXEyyl3qafrLdyBAoGAaauauq0Y1OOefrODXttt9xPz+TI+aHVv/f+ACpQ0ecZiRsxQ0CXHyuN6Rw9YD8wTWgvYS7ay2qJlGLHjlppTj0izfR5zUfYOSeFE4vicf6ZoYh1SREkg2Sc8uL0fFmUDbBZY1/JNcni5AELUtUN5tMXgy7q4wdTE1gyaOdxWwGQ=',
        ],

        'EggPay'     => [
            //支付接口地址
            'url'                   => 'https://kxapi.eggmm.xyz/api/pay/create_order',
            'mch_id'                => '20000083',
            'sign'                  => 'KM6BYXWQFOCX561UAXQUSQ49JQFX5VHYIHGCUJSAUNWGULJMXZ3K0JQCQEHSKRBLQAINFMPNKKQWJF8S7RAJLAP5O2UMBA3T51MQ5266OPDXLYBGSCH3DANMI7NMPLV1',
        ],


        'SiHaiPay'  => [
            //支付接口地址
            'url'                   => 'https://sihaipaytue-api.meisuobudamiya.cc/api/order',
            'app_id'                => '79624103f4f2e8c411679494',
            'sign'                  => 'C046885E9F28899437950096Fa15Ef9f83061DE3',
        ],

        'YouYiPay'  => [
            'url'                   => 'http://*************:4567/api/Pay_Index.html',
            'app_id'                => '6188',
            'sign'                  => 'pniuubJoAvWrN94WuAnfjFZwKoCdY4SI',
        ],

        'MtPay' => [
            'url'    => 'http://pay.anxinpays.com/api/pay/unifiedOrder',
            'sign'   => 'pTZKPilTWrAaX2hIYrSBVM965BzyC5tKeGdQPU1GjS5OjymMCHNPyeZ1rRF5Mq0IbphK3nOkv7XRgBSQG3AUwYk5gHr6bvkrzSQG70h7yWrfJjZVyv6LJPpOfQuaaWT4',
            'mch_id' => 'M1746015973',
            'app_id' => '681216e5e4b0fd87d4a42e27'
        ],

        'ShibaPay'  => [
            'url'                   => 'https://gateway.shibapay666.com/api/pay/create_order',
            'mch_id'                => '20000309',
            'app_id'                => '79810bfa97ee4c1bab3b5324581bd3df',
            'sign'                  => '7PZCZ84C46DWZGXVAVIG0JZADKLJMJC9FBAGHIJ5P4W8NGEAN9XJ6XOUZUJT98TZ2A9YWQCJGPIUEZJTMHXNYBSS2A3LW4YGHMSMC4RKH7938QJXRVLJCNM2NYAZ2WY0',
        ],

        'BaJiPay'   => [
            'url'                 => 'http://**************:35000/Pay_Index.html',
            'mch_id'              => '250576881',
            'sign'                => 'qztirphsrcxv55srzry8x244imv7t4i0',
        ],

    ],

];

