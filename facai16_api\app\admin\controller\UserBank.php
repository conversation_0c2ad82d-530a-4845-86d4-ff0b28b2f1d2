<?php
namespace app\admin\controller;

use app\admin\service\UserBankService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 用户地址
 */
class UserBank
{

    /**
     * 用户地址
     * @return mixed
     */
    public function getUserBankLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'phone',
            'name',
        ]);

        $UserBankService        = new UserBankService();
        $data                   = $UserBankService->getUserBankLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 银行卡信息
     * @return Json
     */
    public function getUserBankInfo(): Json
    {
        $id                 = Request::param('id',0);
        $UserBankService    = new UserBankService();
        $data               = $UserBankService->getUserBankInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加银行卡
     * @return Json
     */
    public function addUserBank(): Json
    {
        $params              = Request::only([
            'phone'             => '',
            'type'              => '',
            'default'           => '',
            'name'              => '',
            'bank_name'         => '',
            'bank_branch'       => '',
            'bank_account'      => '',
            'bank_owner'        => '',
            'coin_name'         => '',
            'coin_blockchain'   => '',
            'coin_account'      => '',
            'alipay_account'    => '',
            'alipay_img'        => '',
            'wx_img'            => ''
        ]);




        $UserBankService    = new UserBankService();
        $data               = $UserBankService->addUserBank(
            $params['phone'],
            $params['type'],
            $params['default'],
            $params['bank_name'],
            $params['bank_branch'],
            $params['bank_account'],
            $params['coin_name'],
            $params['coin_blockchain'],
            $params['coin_account'],
            $params['alipay_account'],
            $params['alipay_img'],
            $params['wx_img']
        );

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新用户信息
     * @return Json
     */
    public function updateUserBank(): Json
    {
        $params              = Request::only([
            'id'                => '',
            'phone'          => '',
            'type'              => '',
            'default'           => '',
            'name'              => '',
            'bank_name'         => '',
            'bank_branch'       => '',
            'bank_account'      => '',
            'coin_name'         => '',
            'coin_blockchain'   => '',
            'coin_account'      => '',
            'alipay_account'    => '',
            'alipay_img'        => '',
            'wx_img'            => ''
        ]);

        $UserBankService    = new UserBankService();

        $data               = $UserBankService->updateUserBank(
            $params['id'],
            $params['phone'],
            $params['type'],
            $params['default'],
            $params['name'],
            $params['bank_name'],
            $params['bank_branch'],
            $params['bank_account'],
            $params['coin_name'],
            $params['coin_blockchain'],
            $params['coin_account'],
            $params['alipay_account'],
            $params['alipay_img'],
            $params['wx_img']
        );

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除
     * @return Json
     */
    public function deleteUserBank(): Json
    {
        $id                 = Request::param('id',0);
        $UserBankService    = new UserBankService();
        $data               = $UserBankService->deleteUserBank($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}