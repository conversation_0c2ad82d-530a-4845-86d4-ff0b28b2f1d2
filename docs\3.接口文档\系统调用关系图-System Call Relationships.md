# 系统调用关系图

## 整体架构调用关系

```
移动端APP (facai16_app)
    ↓ HTTP API调用
API后端 (facai16_api/app/index/)
    ↓ 数据库操作
MySQL + Redis

管理后台 (facai16_admin)
    ↓ HTTP API调用
API后端 (facai16_api/app/admin/)
    ↓ 数据库操作
MySQL + Redis
```

## 详细调用关系

### 1. 用户登录流程

#### 移动端APP登录
```
pages/login/login.vue
    ↓ 调用
api/auth.js → login()
    ↓ HTTP POST
facai16_api/app/index/controller/Login.php → login()
    ↓ 调用
facai16_api/app/index/service/LoginService.php → login()
    ↓ 调用
facai16_api/app/common/repository/UserRepository.php → findByCondition()
    ↓ 查询
MySQL users表
```

#### 管理后台登录
```
layout/login.vue
    ↓ 调用
utils/axios.js
    ↓ HTTP POST
facai16_api/app/admin/controller/Auth.php → login()
    ↓ 调用
facai16_api/app/admin/service/AuthService.php → login()
    ↓ 调用
facai16_api/app/common/repository/AdminRepository.php → findByCondition()
    ↓ 查询
MySQL admins表
```

### 2. 用户投资流程

```
pages/details/buy.vue (投资页面)
    ↓ 调用
api/job.js → buyProject()
    ↓ HTTP POST
facai16_api/app/index/controller/Item.php → pay()
    ↓ 调用
facai16_api/app/index/service/ItemService.php → pay()
    ↓ 调用多个Repository
├── UserRepository.php → 检查用户余额
├── ItemRepository.php → 检查项目状态
├── MoneyLogRepository.php → 记录资金变动
└── ItemRecordRepository.php → 记录投资记录
    ↓ 数据库事务
MySQL (users, items, money_logs, item_records表)
```

### 3. 用户管理流程

```
views/userManage/userList.vue (用户列表)
    ↓ 调用
utils/axios.js
    ↓ HTTP GET
facai16_api/app/admin/controller/User.php → getUserLists()
    ↓ 调用
facai16_api/app/admin/service/UserService.php → getUserLists()
    ↓ 调用
facai16_api/app/common/repository/UserRepository.php → getList()
    ↓ 查询
MySQL users表
```

### 4. 用户转移流程

```
views/userManage/components/userList/userTransfer.vue
    ↓ 提交表单
views/userManage/userList.vue → submitForm()
    ↓ HTTP POST
facai16_api/app/admin/controller/User.php → userRemove()
    ↓ 调用
facai16_api/app/admin/service/UserService.php → userRemove()
    ↓ 调用
facai16_api/app/admin/service/util/RelationTrait.php → relationshipTransfer()
    ↓ 复杂的关系更新
├── updateUserRelations() → 更新用户关系
├── updateAllSubordinates() → 更新下级关系
└── updateUserLevel() → 更新用户等级
    ↓ 数据库事务
MySQL users表 (多条记录更新)
```

### 5. 资金操作流程

#### 充值流程
```
pages/cash/recharge.vue
    ↓ 调用
api/user.js → postRecharge()
    ↓ HTTP POST
facai16_api/app/index/controller/Recharge.php → recharge()
    ↓ 调用
facai16_api/app/index/service/RechargeService.php → recharge()
    ↓ 调用
├── UserRepository.php → 更新用户余额
├── MoneyLogRepository.php → 记录资金流水
└── RechargeRepository.php → 记录充值订单
    ↓ 数据库事务
MySQL (users, money_logs, recharge_orders表)
```

#### 提现流程
```
pages/cash/withdrawal.vue
    ↓ 调用
api/user.js → withdrawal()
    ↓ HTTP POST
facai16_api/app/index/controller/Withdrawal.php → withdrawal()
    ↓ 调用
facai16_api/app/index/service/WithdrawalService.php → withdrawal()
    ↓ 调用
├── UserRepository.php → 检查余额
├── BankRepository.php → 验证银行卡
├── MoneyLogRepository.php → 记录资金流水
└── WithdrawalRepository.php → 创建提现申请
    ↓ 数据库事务
MySQL (users, banks, money_logs, withdrawals表)
```

### 6. 数据统计流程

```
pages/index/index.vue (首页统计)
    ↓ 并发调用多个API
├── api/user.js → getItemNotFinishedSum()
├── api/user.js → getIncomeTotalSum()
├── api/user.js → getTodayTeamTotalSum()
└── api/user.js → getTodayIncomeSum()
    ↓ HTTP GET
facai16_api/app/index/controller/Statistics.php
    ↓ 调用对应方法
├── itemNotFinishedSum()
├── incomeTotalSum()
├── todayTeamTotalSum()
└── todayIncomeSum()
    ↓ 复杂SQL查询
MySQL (多表联查统计)
```

## 中间件调用链

### 用户认证中间件
```
HTTP请求
    ↓ 经过
facai16_api/app/index/middleware/UserAuth.php
    ↓ 验证
JWT Token + Redis缓存
    ↓ 通过后
到达Controller
```

### 管理员认证中间件
```
HTTP请求
    ↓ 经过
facai16_api/app/admin/middleware/AdminAuth.php
    ↓ 验证
JWT Token + 权限检查
    ↓ 通过后
到达Controller
```

### CORS中间件
```
HTTP请求
    ↓ 经过
facai16_api/app/common/middleware/Cors.php
    ↓ 设置
跨域响应头
    ↓ 继续
下一个中间件
```

## 缓存调用关系

### Redis缓存使用
```
业务逻辑
    ↓ 检查缓存
Redis
    ↓ 缓存未命中
查询MySQL
    ↓ 结果写入
Redis缓存
    ↓ 返回
业务结果
```

### 常用缓存Key
- `user:info:{user_id}` - 用户信息缓存
- `config:{key}` - 系统配置缓存
- `item:list:{page}` - 项目列表缓存
- `lock:{operation}:{id}` - 分布式锁

## 队列任务调用

### 异步任务处理
```
业务操作
    ↓ 推送任务
Redis队列
    ↓ 消费任务
facai16_api/app/common/jobs/
    ↓ 执行
具体业务逻辑
    ↓ 结果处理
更新数据库/发送通知
```

### 常用队列任务
- 收益计算任务
- 短信发送任务
- 数据统计任务
- 日志清理任务
