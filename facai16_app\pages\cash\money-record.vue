<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							资金明细
						</view>
					</template>

				</uv-navbar>
				<view class="h-16"></view>
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30 overflow-y-hidden">
				<MoneyRecord ref="rechargeRecord" />
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import MoneyRecord from './components/money-record.vue'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		components: {
			MoneyRecord,
		},
		data() {
			return {
				tabIndex: -1
			};
		},
		watch: {
			tabIndex(newVal, oldVal) {
				this.$nextTick(() => {
					this.$refs.rechargeRecord.onRefresh(0)
				})
			}
		},
		methods: {

		},
		onReady() {
			this.$nextTick(() => {
				this.tabIndex = 0
			})
		}
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>