<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * HengTongsPay
 */
class HengTongsPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'HengTongsPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $money   = $data['money'] * 100;

        $params  = [
            'mchNo'        => $this->config['mch_id'],
            'appId'        => $this->config['app_id'],
            'mchOrderNo'   => $data['orderNo'],
            'amount'       => (float) $money,
            'wayCode'      => $data['channel'],
            'currency'     => 'cny',
            'clientIp'     => request()->ip(),
            'notifyUrl'    => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
            'returnUrl'    => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
            'expiredTime'  => 3600,
            'subject'      => $data['orderNo'],
            'body'         => $data['orderNo'],
            'reqTime'      => time() . '000',
            'version'      => 1.0,
            'signType'     => 'MD5'
        ];


        $params['sign']  = $this->signature($params);

        $result  = curl_post($this->config['url'], $params);

        $uri     = '';

        Log::channel('pay')->info(self::CHANNEL . '@@' . $result);

        $result  = json_decode($result,true);

        if ($result['code'] == 0)
        {
            $uri = $result['data']['payData'];
        }


        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);


        Log::channel('pay')->info(self::CHANNEL . '@@@' . json_encode($param));

        $sign  = $param['sign'];

        $param = Arrays::withOut($param,['sign']);

        if ($this->signature($param) != $sign)
        {
            Log::info(self::CHANNEL . '@@@' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['mchOrderNo'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '@@@' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        // 按照ASCII码对参数名进行排序
        ksort($params);

        // 拼接待签名字符串
        $stringToSign = '';

        foreach ($params as $key => $value)
        {
            $stringToSign .= $key . '=' . $value . '&';
        }

        $stringToSign .= 'key=' . $this->config['sign'];

        // 使用MD5算法生成签名
        return strtoupper(md5($stringToSign));
    }

}