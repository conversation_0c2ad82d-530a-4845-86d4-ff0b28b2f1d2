<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="手机号码" required >
            <el-input v-model="form.phone"  />
        </el-form-item>
        <!-- <el-form-item label="总充值" required >
            <el-input v-model="form.recharge_money" disabled />
        </el-form-item>
        <el-form-item label="总提款" required >
            <el-input v-model="form.withdraw_money" disabled />
        </el-form-item> -->
        <el-form-item label="钱包类型" required>
            <el-select v-model="form.type" placeholder="" clearable>
                <el-option v-for="item in userWalletTypeEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="是否默认" required>
            <el-radio-group v-model="form.default">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
            </el-radio-group>
        </el-form-item>
       
        <template v-if="form.type == 0">
            <el-form-item label="银行名称"  required>
                <el-input v-model="form.bank_name"  clearable />
            </el-form-item>
            <el-form-item label="银行支行"  required>
                <el-input v-model="form.bank_branch"  clearable />
            </el-form-item>
            <el-form-item label="银行账号"  required>
                <el-input v-model="form.bank_account"  clearable />
            </el-form-item>
        </template>
        <template v-if="form.type == 1">
            <el-form-item label="币种"  required>
                <el-input v-model="form.coin_name"  clearable />
            </el-form-item>
            <el-form-item label="区块链"  required>
                <el-input v-model="form.coin_blockchain"  clearable />
            </el-form-item>
            <el-form-item label="地址"  required>
                <el-input v-model="form.coin_account"  clearable />
            </el-form-item>
        </template>
        <template v-if="form.type == 2 || form.type == 3">
            <el-form-item label="账号"  required>
                <el-input v-model="form.alipay_account"  clearable />
            </el-form-item>
            <br>
           
        </template>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import {userWalletTypeEnums, getLabelByVal} from '@/config/enums'

const form = ref({
    // recharge_money: '',
    // withdraw_money: '',
    phone: '',
    default: '',
    type: '',
    bank_name: '',
    bank_branch: '',
    bank_account: '',
    coin_name: '',
    coin_blockchain: '',
    coin_account: '',
    alipay_account: '',
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>