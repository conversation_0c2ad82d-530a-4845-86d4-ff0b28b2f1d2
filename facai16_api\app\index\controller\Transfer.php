<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\CouponRepository;
use app\common\repository\TransferRepository;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\common\utils\Result;
use app\index\service\TransferService;
use app\index\service\YueBaoService;
use think\facade\Request;
use think\response\Json;

/**
 * 转账
 */
class Transfer
{

    /**
     * 转账
     * @return Json
     */
    public function operate(): Json
    {
        $params  = Request::only([
            'phone'   => 0,
            'money'   => 0,
        ]);

        $UserRepo     = new UserRepository();

        $id           = $UserRepo->userByHeader('id');

        $RedisLock    = new RedisLock();
        $status       = $RedisLock->lock('operate:' . $id);

        $TransferService  = new TransferService();


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在签约上一个订单');
            }

            $result  = $TransferService->operate($params);

            $RedisLock->unLock('operate:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('operate:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }
    }


    /**
     * 记录
     * @return Json
     */
    public function record(): Json
    {
        $TransferService  = new TransferService();
        $result           = $TransferService->record();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}