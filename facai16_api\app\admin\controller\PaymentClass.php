<?php
namespace app\admin\controller;

use app\admin\service\PaymentClassService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 支付类型
 */
class PaymentClass
{

    /**
     * 获取支付分类
     * @return mixed
     */
    public function getPaymentClassLists(): Json
    {
        $PaymentService = new PaymentClassService();
        $data           = $PaymentService->getPaymentClassLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取分类信息
     * @return Json
     */
    public function getPaymentClassInfo(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentClassService();
        $data           = $PaymentService->getPaymentClassInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加分类信息
     * @return Json
     */
    public function addPaymentClass(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentClassService();
        $data           = $PaymentService->addPaymentClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新支付类型
     * @return Json
     */
    public function updatePaymentClass(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentClassService();
        $data           = $PaymentService->updatePaymentClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除支付类型
     * @return Json
     */
    public function deletePaymentClass(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentClassService();
        $data           = $PaymentService->deletePaymentClass($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}