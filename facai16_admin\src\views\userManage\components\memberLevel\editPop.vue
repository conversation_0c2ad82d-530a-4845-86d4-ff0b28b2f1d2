<template>
    <el-form label-width="180px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="等级名称" required>
            <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="单笔投资" required>
            <el-input v-model="form.invest" />
        </el-form-item>
        <el-form-item label="累计投资" required>
            <el-input v-model="form.invest_total" />
        </el-form-item>
        <el-form-item label="单笔充值" required>
            <el-input v-model="form.recharge" />
        </el-form-item>
        <el-form-item label="累计充值" required>
            <el-input v-model="form.recharge_total" />
        </el-form-item>
        <el-form-item label="额外收益" required>
            <el-input v-model="form.extra" />
        </el-form-item>
        <el-form-item label="月奖励" required>
            <el-input v-model="form.bonus" />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import { withdrawTypeEnums, getLabelByVal } from '@/config/enums'

const form = ref({
    title: "",
    invest: "",
    invest_total: "",
    recharge: "",
    recharge_total: "",
    extra: "",
    bonus: ""
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(() => {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({ form })

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/deep/ .el-radio-group {
    width: 220px;
}

.form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
    width: 100%;
}

/deep/ .el-form-item {
    align-items: flex-start;
}
</style>