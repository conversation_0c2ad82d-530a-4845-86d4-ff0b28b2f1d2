<?php
namespace app\admin\controller;

use app\admin\service\FeedbackService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 反馈
 */
class Feedback
{

    /**
     * 反馈列表
     * @return mixed
     */
    public function getFeedbackLists(): <PERSON><PERSON>
    {
        $FeedbackService = new FeedbackService();
        $data            = $FeedbackService->getFeedbackLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 获取反馈信息
     * @return Json
     */
    public function getFeedbackInfo(): Json
    {
        $id              = Request::param('id',0);
        $FeedbackService = new FeedbackService();
        $data            = $FeedbackService->getFeedbackInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除反馈
     * @return Json
     */
    public function deleteFeedback(): Json
    {
        $id              = Request::param('id',0);
        $FeedbackService = new FeedbackService();
        $data            = $FeedbackService->deleteFeedback($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}