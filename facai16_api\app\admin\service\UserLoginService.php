<?php
namespace app\admin\service;

use app\common\repository\UserLoginRepository;
use app\common\utils\Result;

/**
 * 抽奖
 */
class UserLoginService
{
    public function getUserLoginLists(): array
    {
        $UserLoginRepo = new UserLoginRepository();
        $data        = $UserLoginRepo->paginates();

        return Result::success($data);
    }

    public function getUserLoginInfo($id): array
    {
        $UserLoginRepo = new UserLoginRepository();
        $data          = $UserLoginRepo->findById($id);

        return Result::success($data);
    }


    public function addUserLogin($params): array
    {
        $UserLoginRepo = new UserLoginRepository();
        $res        = $UserLoginRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }





}
