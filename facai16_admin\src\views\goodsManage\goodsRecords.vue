<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    exportLink="GoodLog/export"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="用户名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.order_no"
          placeholder="订单号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.goods_title"
          placeholder="商品名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchForm.status" placeholder="状态" clearable>
          <el-option
            v-for="item in sendStatusEnums"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="购买时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="购买时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editItem('add', {})"
        v-permission
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户" width="150" />
      <el-table-column prop="phone" label="电话号码" width="160" />
      <el-table-column prop="goods_title" label="商品名称" width="200" />
      <el-table-column prop="order_no" label="订单号" width="200" />
      <!-- <el-table-column prop="address_name" label="收货人" width="200" /> -->
      <!-- <el-table-column prop="address_city" label="省市区" width="200" /> -->
      <!-- <el-table-column prop="address_place" label="详细地址" width="200" /> -->
      <el-table-column prop="desc" label="描述" width="200" />
      <el-table-column prop="status" label="状态" width="200">
        <template #default="scope">
          <span :class="{ green: scope.row.status == 1 }">{{
            getLabelByVal(scope.row.status, sendStatusEnums)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="购买时间" width="200" />
      <el-table-column
        label="操作"
        fixed="right"
        :resizable="false"
        width="200px"
         v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editItem('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="新增/编辑" width="1100">
      <editPop
        :key="editRow"
        :formType="formType"
        ref="editFormRef"
        :item="editRow"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  booleanEnums,
  getLabelByVal,
  handlersBooleanEnums,
  sendStatusEnums,
} from "@/config/enums";
import EditPop from "./components/goodsRecords/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"))
const searchForm = ref({
  username: "",
  phone: "",
  order_no: "",
  goods_title: "",
  status: "",
  starttime: "",
  endtime: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);
const formType = ref("add");

onMounted(() => {
  getList();
});

const editItem = (type, row = {}) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url =
    formType.value == "add" ? "GoodLog/addGoodLog" : "GoodLog/updateGoodLog";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      username: data.username,
      phone: data.phone,
      deliver_order_no: data.deliver_order_no,
      deliver_title: data.deliver_title,
      address_name: data.address_name,
      address_phone: data.address_phone,
      address_city: data.address_city,
      address_place: data.address_place,
      status: data.status,
      deliver_title: data.deliver_title,
      deliver_order_no: data.deliver_order_no,
      deliver_time: data.deliver_time,
      goods_id: data.goods_id,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "GoodLog/deleteGoodLog?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/GoodLog/getGoodLogLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
