<template>
    <adminTable
      :hideSeachButton="true"
      :tableLoading="tableLoading"
      :totalList="totalList"
      v-model:page="page"
      v-model:limit="limit"
      v-model:searchForm="searchForm"
      :tableData="tableData"
      @search="getList"
    >
      <template v-slot:table>
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="uid" label="用户ID" width="130" />
        <el-table-column prop="username" label="用户名" width="130" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="is_test" label="账号类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.is_test, accountTypeEnums) }}
        </template>
      </el-table-column>
        <el-table-column prop="words" label="字" width="100" />
        <el-table-column prop="create_at" label="创建时间" width="130" />
        <el-table-column prop="update_at" label="更新时间" width="130" />
        
        <el-table-column
          label="操作"
          fixed="right"
          width="200"
          v-if="currentUser.role == 0"
        >
          <template #default="scope">
           
            <el-button
              type="danger"
              icon="Delete"
              v-permission
              @click="deleteAction(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </template>
      
    </adminTable>
  </template>
  
  <script setup>
  import { ref, getCurrentInstance, onMounted, reactive } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { accountTypeEnums, getLabelByVal } from "../../config/enums";
  
  const dialogFlag = ref(false);
  const editFormRef = ref(false);
  const editRow = ref({});
  const formType = ref("add");
  const searchForm = reactive({})
  
  onMounted(() => {
    getList();
  });
  
  const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
  const { proxy } = getCurrentInstance();
  
  const page = ref(1);
  const limit = ref(10);
  const totalList = ref(0);
  const tableLoading = ref(false);
  const tableData = ref([]);
  const getList = async () => {
    tableLoading.value = true;
    const res = await proxy.$http({
      type: "get",
      url: "/Words/getWordsLogLists",
      params: {
        page: page.value,
        limit: limit.value,
      },
    });
    tableLoading.value = false;
    if (res.code == 0) {
      tableData.value = res.data.data;
      totalList.value = res.data.total;
    }
  };
  
  const editItem = (type, row) => {
    formType.value = type;
    editRow.value = row;
    dialogFlag.value = true;
  };
  
  const deleteAction = (row) => {
    ElMessageBox.confirm("是否删除?", "Warning", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await proxy.$http({
          method: "get",
          url: "/Words/deleteWordsLogLists?id=" + row.id,
        });
        if (res.code == 0) {
          getList();
        }
        ElMessage({
          type: "success",
          message: res.msg,
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  };
  
  </script>
  
  <style lang="less" scoped></style>
  