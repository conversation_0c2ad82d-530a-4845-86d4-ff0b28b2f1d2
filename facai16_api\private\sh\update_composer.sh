#!/bin/bash

# 设置 composer.json 的路径和上次修改时间的文件
COMPOSER_FILE="/path/to/your/composer.json"
LAST_MODIFIED_FILE="/path/to/your/last_modified_time.txt"

# 获取当前 composer.json 的修改时间
CURRENT_MODIFIED_TIME=$(stat -c %Y "$COMPOSER_FILE")

# 如果 last_modified_time.txt 不存在，初始化它
if [ ! -f "$LAST_MODIFIED_FILE" ]; then
    echo "$CURRENT_MODIFIED_TIME" > "$LAST_MODIFIED_FILE"
fi

# 读取上次修改的时间
LAST_MODIFIED_TIME=$(cat "$LAST_MODIFIED_FILE")

# 如果文件被修改过
if [ "$CURRENT_MODIFIED_TIME" -ne "$LAST_MODIFIED_TIME" ]; then
    # 运行 composer update
    composer update
    echo "Composer updated due to changes in composer.json."

    # 更新 last_modified_time.txt
    echo "$CURRENT_MODIFIED_TIME" > "$LAST_MODIFIED_FILE"
else
    echo "No changes in composer.json."
fi

#chmod +x update_composer.sh
#* * * * * /path/to/your/update_composer.sh