<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\BankService;
use think\facade\Request;
use think\response\Json;


/**
 * 银行卡管理
 */
class Bank
{

    /**
     * 再审核中的银行
     * @return Json
     */
    public function bankInCheck(): Json
    {
        $BankService = new BankService();
        $result      = $BankService->bankInCheck();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 银行卡
     * @return Json
     */
    public function banks(): Json
    {
        $BankService = new BankService();
        $result      = $BankService->banks();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 银行卡管理
     * @return Json
     */
    public function lists(): Json
    {
        $params       = Request::only([
            'type' => 0,
        ]);

        $BankService = new BankService();
        $result      = $BankService->lists($params['type']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 绑定银行卡
     * @return Json
     */
    public function add(): Json
    {

        $params       = Request::only([
            'type' => 0,
            'default',
            'bank_name',
            'bank_account',
            'bank_branch',
            'coin_name',
            'coin_blockchain',
            'coin_account',
            'alipay_account',
            'alipay_img',
            'wx_img',
            'name'
        ]);

        $UserService = new BankService();
        $result      = $UserService->add($params);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 编辑银行卡
     * @return Json
     */
    public  function edit(): Json
    {
        $params       = Request::only([
            'type' => 0,
            'id' => 0,
            'bank_name',
            'bank_account',
            'bank_branch',
            'coin_name',
            'coin_blockchain',
            'coin_account',
        ]);

        $UserService = new BankService();
        $result      = $UserService->edit($params);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 删除账户
     * @return Json
     */
    public function delete(): Json
    {
        $params       = Request::only(['id' => 0]);
        $BankService  = new BankService();
        $result       = $BankService->delete($params['id']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


}