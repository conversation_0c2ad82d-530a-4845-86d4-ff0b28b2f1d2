<?php
namespace app\admin\controller;

use app\admin\service\GoodLogService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 商品购买记录
 */
class GoodLog
{

    /**
     * 商品购买记录
     * @return Json
     */
    public function getGoodLogLists(): Json
    {

        $params             = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order_no',
            'class_id'
        ]);


        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->getGoodLogLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 导出
     * @return Json
     */
    public function export(): Json
    {
        $params             = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order_no',
            'class_id'
        ]);


        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->export($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 商品购买记录信息
     * @return Json
     */
    public function getGoodLogInfo(): Json
    {
        $id                = Request::param('id',0);
        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->getGoodLogInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 商品购买记录信息
     * @return Json
     */
    public function deleteGoodLog(): Json
    {
        $id                = Request::param('id',0);
        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->deleteGoodLog($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    public function addGoodLog(): Json
    {
        $param             = Request::only([
            'phone',
            'goods_id',
            'status',
            'deliver_title' => '',
            'deliver_order_no'=> '',
            'deliver_time' => ''
        ]);

        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->addGoodLog($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * @return Json
     */
    public function updateGoodLog(): Json
    {
        $params    = Request::only([
            'status',
            'deliver_title',
            'deliver_order_no',
            'deliver_time',
            'address_name',
            'address_phone',
            'address_city',
            'address_place',
            'id'
        ]);

        $GoodLogService    = new GoodLogService();
        $data              = $GoodLogService->updateGoodLog($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



}