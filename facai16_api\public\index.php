<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// [ 应用入口文件 ]
namespace think;
use Dotenv\Dotenv;


require __DIR__ . '/../vendor/autoload.php';

$dotenv = Dotenv::createArrayBacked(__DIR__.'/../')->load();
// 执行HTTP应用并响应
$app    = new App();
//载入配置
$app->loadEnv($dotenv['APP_ENV'] ?? '');

$http     = $app->http;

$response = $http->name('index')->run();

$response->send();

$http->end($response);
