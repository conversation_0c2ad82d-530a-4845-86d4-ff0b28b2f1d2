校订时间：2022-03-23
配置
项目配置 必须 存储在 .env 文件和 config 目录中，然后使用 env() 和 config() 函数来读取；
应用配置 必须 存储在模块 config 目录中，如：app/index/config；
配置参数名 必须 使用小写定义配置参数的规范， 如：app_name；
配置文件 必须 以PHP数组格式存储；
.env 文件 必须 采用 .ini 格式存储。
必须 开启 自动时间戳 。
数据库
必须 遵循官方文档的数据表和字段采用小写加下划线方式命名规范；
数据表名 必须 为「单数」, 多个单词情况下使用「Snake Case」 如：topic, user_log;
数据库字段名 必须 为「Snake Case」，如：view_count, create_time ;
数据表主键 必须 为「id」;
数据表外键 必须 为「resource_id」，如：user_id , topic_id ;
绝不 允许数据表没有时间戳字段，每个表 必须 有创建时间（ create_time ） 和 更新时间 (update_time)，并且这两个字段 必须 是整型( int )；
和自动时间戳字段规范保持一致，其它时间类型 必须 存储成整型( int ) ，并且命名 应该 以 _time 为后辍；
不应该 所有数据库字段允许空值，应该 为索引、字符串或整数字段指定一个合理的默认值。（数据库允许空值(null)，往往是悲剧的开始）
应该 为数据表常用查询添加索引；

验证器
验证器 是 ThinkPHP 框架推荐进行数据验证的方式。

验证器类 必须 放在模块的 validate 目录里， 如：app/admin/validate;
验证器类名 必须 为「单数」驼峰法命名, 如：app\admin\validate\Topic ;
类文件名 必须 遵循驼峰法命名规范, 并且 必须 与类名保持大小写一致，如 app/admin/validate/Topic.php；
验证器类 应该 遵循 约定大于配置 方式进行命名， 数据模型 Topic 对应的验证器命名也 应该 是 Topic ；
当可以使用场景（ `since' ）实现对同一数据模型不用场景表单的验证时 应该 使用场景实现，不应该 再定义新的验证器；
数据模型
数据模型类 必须 放在模块的 model 目录里, 如：app/common/model；
数据模型类名 必须 为「单数」驼峰法命名, 如：app\common\model\Topic ；
类文件名 必须 遵循驼峰法命名规范, 并且 必须 与类名保持大小写一致，如 app/common/model/Topic.php；
数据模型变量 必须 为「resource_id」，如：$user_id, $topic_id ；
在数据模型保存用户表单提交数据前，应该 使用 验证器 来验证表单数据是否有效；
当数据模型的代码臃肿时，应该 利用 Trait 来精简逻辑代码量，提高可读性；
绝不 「过度设计（Over Designed）」，极大降低了编码愉悦感。
控制器
必须 优先使用 Restful 资源控制器 ；
控制器类 必须 放在模块的 controller 目录里，如：app/index/controller；
控制器类名 必须 为「单数」驼峰法命名, 绝不 以 Controller 为后辍, 如：app\index\controller\Topic ;
类文件名 必须 遵循驼峰法命名规范, 并且 必须 与类名保持大小写一致，如 app/index/controller/Topic.php；
不应该 为「方法」书写注释，这要求方法取名要足够合理，不需要过多注释；
应该 为一些复杂的逻辑代码块书写注释，主要介绍产品逻辑 - 为什么要这么做 ；
不应该 在控制器中书写「私有方法」，控制器里 应该 只存放「路由动作方法」；
绝不 遗留「死方法」，就是没有用到的方法，控制器里的所有方法，都应该被使用到，否则应该删除；
绝不 把业务逻辑代码写在控制器里，应该 写在对应数据模型类里；
绝不 在控制器里批量注释掉代码，无用的逻辑代码就必须清除掉。
路由
绝不 在路由配置文件里书写『闭包路由』或者其他业务逻辑代码；
路由器要保持干净整洁，绝不 放置除路由配置以外的其他程序逻辑；
必须 优先使用 Restful 路由，配合资源控制器使用;
应该 使用 name 方法为路由指定生成标识，路由标识必须 优先使用『资源前缀』作为命名规范，如 topic.index 的资源前缀是 topic. ;
当路由有标识时，获取 URL 时 必须 优先使用标识路由获取， 如话题详情页对应的资源路由是 topic/index 而它的标识是 topic.index , 那么我们应该优先使用 url('[topic.index]') 获取页面路由。
助手函数
ThinkPHP 提供了很多 助手函数，但有时候我们也需要创建自己的助手函数。我们在创建自己的助手函数时应该遵循：

公共助手函数 必须 创建在 app/common.php 里；
模块独有助手函数 必须 创建在模块的 common.php 里，如 app/index/common.php；
助手函数命名 必须 遵循 ThinkPHP 官方 命名规范, 使用小写字母和下划线（小写字母开头）的方式，例如 get_client_ip;

代码版本控制
应该 每完成一个功能开发提交一次代码；
绝不 让代码在自己的电脑上过夜；
绝不 把本地化配置文件版本到版本控制里，应该 上传一个示例配置文件，如不要把 .env 文件纳入版本控制；
绝不 把项目运行缓存目录和文件纳入版本控制，如 runtime 目录里；
绝不 把第三方扩展包纳入版本控制，如 vendor 目录里的文件和子目录；
绝不 把用户上传文件纳入版本控制，如用户上传的话题图片和头像图片。

API接口规范
必须 按照业务需求设计API；
绝不 传输多余并且业务不需要的字段；
必须 标注好每个字段的意义和对应的枚举;