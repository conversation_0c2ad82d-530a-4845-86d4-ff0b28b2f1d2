<?php
namespace app\common\repository;
use app\common\model\MoneyClass;
use app\common\model\MoneyLog;
use app\common\utils\Result;
use think\response\Json;


class MoneyLogRepository extends MoneyLog
{
    use BaseRepository;


    /**
     * 账变
     * @param int $uid
     * @param float $amount
     * @param int $cid
     * @param string $orderId
     * @param string $desc
     * @return array
     */
    public function fund(int $uid, float $amount, int $cid, string $orderId, string $desc = ''): array
    {
        $MoneyRepo = new MoneyRepository();
        $money     = $MoneyRepo->findByCondition(['uid' => $uid],'*', [],true,true);

        if(!$money)
        {
            return Result::fail('没有钱包信息');
        }

        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($uid,'id,username,phone,is_test');


        if(!$user)
        {
            return Result::fail('没有账号信息');
        }

        $MoneyClassRepo = new MoneyClassRepository();
        $class          = $MoneyClassRepo->findById($cid,'id,title,style');

        if(!$class)
        {
            return Result::fail('没有账变信息');
        }

        $update = [
            'update_time' => time(),
            'update_at'   => date('Y-m-d H:i:s', time())
        ];

        $before = $money['money'] + $money['frozen_money'];

        switch ($class['style'])
        {
            case MoneyClass::STYLE['FROZEN_MAX_DECREASE']:

                if ($money['frozen_money'] < $amount)
                {
                    $_before                = $money['money'] - ($amount - $money['frozen_money']);
                    $update['money']        = $_before;
                    $update['frozen_money'] = 0;

                    if (($_before - $amount) < 0)
                    {
                        return Result::fail('扣减失败');
                    }
                }
                else
                {
                    $update['frozen_money'] = $money['frozen_money'] - $amount;
                }

                $after = $before - $amount;

                break;
            case MoneyClass::STYLE['FROZEN_DECREASE']:

                $_before        = $money['frozen_money'];

                if (($_before - $amount) < 0)
                {
                    return Result::fail('可用扣减失败');
                }

                $_after                      = $_before - $amount;
                $update['frozen_money']      = $_after;
                $after                       = $before - $amount;
                break;

            case MoneyClass::STYLE['FROZEN_INCREASE']:
                $_before                 = $money['frozen_money'];
                $_after                  = $_before + $amount;
                $after                   = $before + $amount;
                $update['frozen_money']  = $_after;
                break;
            case MoneyClass::STYLE['DECREASE']:

                $_before        = $money['money'];

                if (($_before - $amount) < 0)
                {
                    return Result::fail('可用扣减失败');
                }

                $_after              = $_before - $amount;
                $update['money']     = $_after;
                $after               = $before - $amount;
                break;
            case MoneyClass::STYLE['INCREASE']:
            default:
                $_before             = $money['money'];
                $_after              = $_before + $amount;
                $after               = $before + $amount;
                $update['money']     = $_after;
                break;
        }


        $res = $MoneyRepo->updateByCondition(['uid' => $uid], $update);

        if (!$res)
        {
            return Result::fail('更新钱包失败');
        }

        $time   = time();

        $insert = [
            'uid'           => $user['id'],
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'is_test'       => $user['is_test'],
            'order_id'      => $orderId,
            'class_id'      => $class['id'],
            'class_name'    => $class['title'],
            'before'        => $before,
            'amount'        => $amount,
            'after'         => $after,
            'desc'          => $desc,
            'create_time'   => $time,
            'update_time'   => $time,
        ];


        $MoneyLogRepo = new MoneyLogRepository();
        $res          = $MoneyLogRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }


        return Result::success();
    }

    /**
     * 积分
     * @param int $uid
     * @param float $amount
     * @param int $cid
     * @param string $orderId
     * @param string $desc
     * @return array
     */
    public function point(int $uid, float $amount, int $cid, string $orderId, string $desc = ''):array
    {
        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($uid,'id,username,phone');

        if(!$user)
        {
            return Result::fail('没有账号信息');
        }

        $UserInfoRepo   = new UserInfoRepository();
        $userInfo       = $UserInfoRepo->findByCondition(['uid' => $uid],'*', [],true,true);

        if (!$userInfo)
        {
            return Result::fail('没有账号信息2');
        }

        $UserStateRepo  = new UserStateRepository();
        $isTest         = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $MoneyClassRepo = new MoneyClassRepository();
        $class          = $MoneyClassRepo->findById($cid,'id,title,style');

        if(!$class)
        {
            return Result::fail('没有账变信息');
        }


        $update = [
            'update_time' => time(),
            'update_at'   => date('Y-m-d H:i:s', time())
        ];

        $before        = $userInfo['user_points'];

        if ($class['style'] == MoneyClass::STYLE['INCREASE'])
        {
            $after = $before + $amount;
        }
        else
        {

            if (($before - $amount) < 0)
            {
                return Result::fail('扣减失败');
            }

            $after = $before - $amount;
        }

        $update['user_points'] = $after;

        $res = $UserInfoRepo->updateByCondition(['uid' => $uid], $update);

        if (!$res)
        {
            return Result::fail('更新钱包失败');
        }

        $time   = time();

        $insert = [
            'uid'           => $user['id'],
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'is_test'       => (int)$isTest,
            'order_id'      => $orderId,
            'class_id'      => $class['id'],
            'class_name'    => $class['title'],
            'before'        => $before,
            'amount'        => $amount,
            'after'         => $after,
            'desc'          => $desc,
            'create_time'   => $time,
            'update_time'   => $time,
        ];


        $MoneyLogRepo = new MoneyLogRepository();
        $res          = $MoneyLogRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }


        return Result::success();
    }

    /**
     * 优惠券
     * @param int $uid
     * @param float $amount
     * @param int $cid
     * @param string $orderId
     * @param string $desc
     * @return array
     */
    public function coupon(int $uid, float $amount, int $cid, string $orderId, string $desc = ''):array
    {

        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($uid,'id,username,phone');

        if(!$user)
        {
            return Result::fail('没有账号信息');
        }

        $UserInfoRepo   = new UserInfoRepository();
        $userInfo       = $UserInfoRepo->findByCondition(['uid' => $uid],'*', [],true,true);

        if (!$userInfo)
        {
            return Result::fail('没有账号信息');
        }

        $UserStateRepo  = new UserStateRepository();
        $isTest         = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $MoneyClassRepo = new MoneyClassRepository();
        $class          = $MoneyClassRepo->findById($cid,'id,title,style');

        if(!$class)
        {
            return Result::fail('没有账变信息');
        }


        $update = [
            'update_time' => time(),
            'update_at'   => date('Y-m-d H:i:s', time())
        ];

        $before        = $userInfo['user_coupon'];

        if ($class['style'] == MoneyClass::STYLE['INCREASE'])
        {
            $after = $before + $amount;
        }
        else
        {

            if (($before - $amount) < 0)
            {
                return Result::fail('扣减失败');
            }

            $after = $before - $amount;
        }

        $update['user_coupon'] = $after;

        $res = $UserInfoRepo->updateById($uid, $update);

        if (!$res)
        {
            return Result::fail('更新钱包失败');
        }

        $time   = time();

        $insert = [
            'uid'           => $user['id'],
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'is_test'       => (int)$isTest,
            'order_id'      => $orderId,
            'class_id'      => $class['id'],
            'class_name'    => $class['title'],
            'before'        => $before,
            'amount'        => $amount,
            'after'         => $after,
            'desc'          => $desc,
            'create_time'   => $time,
            'update_time'   => $time,
        ];


        $MoneyLogRepo = new MoneyLogRepository();
        $res          = $MoneyLogRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }

        return Result::success();
    }

    /**
     * 抽奖
     * @param int $uid
     * @param float $amount
     * @param int $cid
     * @param string $orderId
     * @param string $desc
     * @return array
     */
    public function raffle(int $uid, float $amount, int $cid, string $orderId, string $desc = ''):array
    {
        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($uid,'id,username,phone');

        if(!$user)
        {
            return Result::fail('没有账号信息');
        }

        $UserInfoRepo   = new UserInfoRepository();
        $userInfo       = $UserInfoRepo->findByCondition(['uid' => $uid],'*', [],true,true);

        if (!$userInfo)
        {
            return Result::fail('没有账号信息');
        }

        $UserStateRepo  = new UserStateRepository();
        $isTest         = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $MoneyClassRepo = new MoneyClassRepository();
        $class          = $MoneyClassRepo->findById($cid,'id,title,style');

        if(!$class)
        {
            return Result::fail('没有账变信息');
        }


        $update = [
            'update_time' => time(),
            'update_at'   => date('Y-m-d H:i:s', time())
        ];

        $before        = $userInfo['raffle_num'];

        if ($class['style'] == MoneyClass::STYLE['INCREASE'])
        {
            $after = $before + $amount;
        }
        else
        {

            if (($before - $amount) < 0)
            {
                return Result::fail('扣减失败');
            }

            $after = $before - $amount;
        }

        $update['raffle_num'] = $after;

        $res = $UserInfoRepo->updateByCondition(['uid' => $uid], $update);

        if (!$res)
        {
            return Result::fail('更新钱包失败');
        }

        $time   = time();

        $insert = [
            'uid'           => $user['id'],
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'is_test'       => (int)$isTest,
            'order_id'      => $orderId,
            'class_id'      => $class['id'],
            'class_name'    => $class['title'],
            'before'        => $before,
            'amount'        => $amount,
            'after'         => $after,
            'desc'          => $desc,
            'create_time'   => $time,
            'update_time'   => $time,
        ];


        $MoneyLogRepo = new MoneyLogRepository();
        $res          = $MoneyLogRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }

        return Result::success();
    }

    /**
     * 统计
     */
    public function statistics($star,$end,$type, $isTest = '', $members = []): float
    {

        $where      = [];
        $where[]    = ['create_time', '>=', $star];
        $where[]    = ['create_time', '<', $end];
        $where[]    = ['class_id', 'in', $type];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

     
        $members && $where[] = ['uid', 'in', $members];

        return $this->sumByCondition($where,'amount');
    }



    /**
     * 去重count
     */
    public function uniqCountBy($star, $end, $type, $field ='uid', $isTest = '', $members = []): int
    {
        $where      = [];
        $where[]    = ['create_time', '>=', $star];
        $where[]    = ['create_time', '<', $end];
        $where[]    = ['class_id', 'in', $type];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

        $members && $where[] = ['id', 'in', $members];

        return $this->distinct(true)->field($field)->where($where)->count();
    }



    /**
     * 去重count
     */
    public function countBy($star, $end, $type, $isTest = '', $members = []): int
    {
        $where      = [];
        $where[]    = ['create_time', '>=', $star];
        $where[]    = ['create_time', '<', $end];
        $where[]    = ['class_id', 'in', $type];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

        $members && $where[] = ['id', 'in', $members];

        return $this->field('id')->where($where)->count();
    }



}
