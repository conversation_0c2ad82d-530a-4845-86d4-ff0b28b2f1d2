<template>
  <div class="page">
    <el-form :inline="true" :model="props.searchForm" class="header-form">
      <div class="form-inline">
        <slot name="form-inline-items"></slot>
      </div>
      <el-form-item class="header-form-buttons">
        <el-button @click="resetSearchForm" v-if="!props.hideResetButton"
          >重置</el-button
        >
        <slot name="query-button-left"></slot>
        <el-button type="primary" v-if="!props.hideSeachButton" @click="onQuery"
          >查询</el-button
        >
        <el-button type="success" v-if="props.exportLink" @click="doExport"
          >导出</el-button
        >
        <slot name="query-button-right"></slot>
      </el-form-item>
    </el-form>
    <slot class="table-info"></slot>
    <el-table
      v-loading="tableLoading"
      row-key="id"
      @selection-change="tableSelectionChange"
      :data="props.tableData"
      class="table"
      border
      style="width: 100%"
    >
      <slot name="table"></slot>
    </el-table>
    <div class="pagination-block">
      <el-pagination
        v-model:current-page="props.page"
        v-model:page-size="props.limit"
        :page-sizes="[10, 20, 30, 40, 100]"
        :size="size"
        :disabled="disabled"
        :background="background"
        layout="total, sizes, prev, pager, next, jumper"
        :total="props.totalList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { getCurrentInstance, ref } from "vue";

const props = defineProps([
  "searchForm",
  "tableData",
  "hideSeachButton",
  "hideResetButton",
  "tableLoading",
  "totalList",
  "limit",
  "page",
  "exportLink",
]);

const emit = defineEmits(["search"]);
const { proxy } = getCurrentInstance();

const onQuery = () => {
  emit("search");
};

const { searchForm } = props;
const resetSearchForm = async () => {
  Object.keys(searchForm).map((item) => {
    searchForm[item] = null;
  });
  await emit("update:searchForm", searchForm);
  emit("search");
};

const size = "default";
const background = ref(false);
const disabled = ref(false);
const selectedItems = ref([]);

const tableSelectionChange = (val) => {
  let ids = [];
  val.map((item) => {
    ids.push(item.id);
  });
  emit("update:selectedIds", ids);
};

const handleSizeChange = async (val) => {
  await emit("update:limit", val);
  await emit("update:page", 1);
  emit("search");
};
const handleCurrentChange = async (val) => {
  await emit("update:page", val);
  emit("search");
};

const doExport = async () => {
  try {
    const res = await proxy.$http({
      method: "get",
      url: props.exportLink,
      params: props.searchForm,
    });
    if (res.code == 0) {
      window.open(res.data.url);
    } else {
      ElMessage({
        type: "error",
        message: error.msg,
      });
    }
  } catch (error) {
    ElMessage({
      type: "error",
      message: error.message,
    });
  }
};

defineExpose({ selectedItems });
</script>

<style lang="less" scoped>
.page {
  display: flex;
  flex-direction: column;
}

.header-form {
  display: flex;
  align-items: center;
  height: max-content;
  /deep/ .el-input {
    --el-input-width: 190px;
  }
  /deep/ .el-select {
    --el-select-width: 190px;
  }
  .form-inline {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .header-form-buttons {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    height: 100%;
    padding-top: 10px;
  }
}

.table {
  flex: 1;
}
::v-deep .el-scrollbar__view {
  width: 100%;
}

.header-form {
  padding-bottom: 10px;
  border-bottom: 1px solid #ececec;
}

.pagination-block {
  padding-top: 10px;
}
::v-deep .el-table__header {
  min-width: 100%;
  th {
    background-color: #efefefcc;
  }
}
::v-deep .el-table__body {
  min-width: 100%;
}
</style>
