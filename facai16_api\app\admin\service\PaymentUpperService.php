<?php
namespace app\admin\service;


use app\common\repository\PaymentChannelRepository;
use app\common\repository\PaymentUpperRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 支付上游
 */
class PaymentUpperService
{
    /**
     * 获取支付上游产品
     * @return array
     */
    public function getPaymentUpperLists(): array
    {
        $PaymentRepo = new PaymentUpperRepository();
        $data      = $PaymentRepo->paginates([],'*',10,['id'=> 'desc']);
        return Result::success($data);
    }

    /**
     * 获取支付上游产品信息
     * @param $id
     * @return array
     */
    public function getPaymentUpperInfo($id): array
    {
        $PaymentRepo = new PaymentUpperRepository();
        $data        = $PaymentRepo->findById($id);
        return Result::success($data);
    }

    /**
     * 添加支付上游产品信息
     * @param $params
     * @return array
     */
    public function addPaymentUpper($params): array
    {
        $PaymentRepo = new PaymentUpperRepository();
        $res         = $PaymentRepo->inserts($params);

        if (!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }

    /**
     * 更新支付上游产品
     * @param $params
     * @return array
     */
    public function updatePaymentUpper($params): array
    {
        $PaymentRepo = new PaymentUpperRepository();

        $res       = $PaymentRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail('修改失败');
        }

        $PaymentChannelRepo = new PaymentChannelRepository();
        $where      = [];
        $where[]    = ['upper_id','=',$params['id']];


        $update = [
            'status'        => $params['status'],
            'upper_status'  => $params['status'],
            'update_time'   => time()
        ];

        $res = $PaymentChannelRepo->updateByCondition($where, $update);

        if (!$res)
        {
            return Result::fail('修改失败');
        }

        return Result::success();
    }


    /**
     * 删除支付上游产品
     * @param $id
     * @return array
     */
    public function deletePaymentUpper($id): array
    {
        $PaymentRepo = new PaymentUpperRepository();
        $res         = $PaymentRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}