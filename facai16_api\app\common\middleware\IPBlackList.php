<?php


namespace app\common\middleware;

use app\common\define\Status;
use app\common\utils\IpAddress;
use Closure;
use app\Request;
use think\exception\HttpResponseException;
use think\facade\Config;
use think\Response;
use think\response\Html;

/**
 * 检测ip
 * Class IPBlackList
 * @package app\common\middleware
 */
class IPBlackList
{
    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {

        $ip       = IpAddress::realIP();

        $allowIps = Config::get('ip_blacklist');

        if(in_array($ip, $allowIps))
        {
            $data           = [];
            $data['code']   = Status::FAILED;
            $data['msg']    = 'ip address in black list [' . $ip . ']';
            $data['data']   = (object) null;
            $type           = array("Content-type", "application/json");
            $json           = json_encode($data,320);

            $response       = Html::create('','json',256)->content($json)->header($type);

            throw new HttpResponseException($response);

        }

        return $next($request);
    }

}
