// 实名状态
export const sfzStatusEnums = [
  {
    label: "通过",
    value: 0,
  },
  {
    label: "审核",
    value: 1,
  },
  {
    label: "拒绝",
    value: 2,
  }
];

export const userlistSfzStatusEnums = [
  {
    label: "正常",
    value: 0,
  },
  {
    label: "未实名",
    value: 1,
  },
  {
    label: "审核中",
    value: 2,
  }
];

export const stocksStatusEnums = [
  {
    label: "进行中",
    value: 0,
  },
  {
    label: "已结束",
    value: 1,
  },
]

export const yuebaoTypeEnums = [
  {
    label: "存",
    value: 1,
  },
  {
    label: "取",
    value: 2,
  },
  {
    label: "收益",
    value: 3,
  }
];

export const accountTypeEnums = [
  {
    label: "正常账号",
    value: 0,
  },
  {
    label: "测试账号",
    value: 1,
  },
]

export const accoutActiveEnums = [
  {
    label: "有效账号",
    value: 0,
  },
  {
    label: "无效账号",
    value: 1,
  },
]


export const booleanEnums = [
  {
    label: "是",
    value: 1,
  },
  {
    label: "否",
    value: 0,
  },
];

export const clockEnums = [
  {
    label: "否",
    value: 0,
  },
  {
    label: "个人",
    value: 1,
  },
  {
    label: "团队",
    value: 2,
  },
  {
    label: "团队解冻",
    value: 3,
  },
];

export const openEnums = [
  {
    label: "开启",
    value: 0,
  },
  {
    label: "关闭",
    value: 1,
  },
];

export const limitEnums = [
  {
    label: "正常",
    value: 0,
  },
  {
    label: "限制",
    value: 1,
  },
];

export const memberLevelList = [
  {
    label: "新星",
    value: 1,
  },
  {
    label: "一星",
    value: 2,
  },
  {
    label: "二星",
    value: 3,
  },
  {
    label: "三星",
    value: 4,
  },
  {
    label: "四星",
    value: 5,
  },
  {
    label: "五星",
    value: 6,
  },
  {
    label: "六星",
    value: 7,
  },
];

export const handleStatusEnums = [
  {
    label: "待审核",
    value: -1,
  },
  {
    label: "已通过",
    value: 1,
  },
  {
    label: "已拒绝",
    value: 2,
  },
];

export const userWalletTypeEnums = [
  {
    label: "银行卡",
    value: 0,
  },
  {
    label: "数字货币",
    value: 1,
  },
];

export const withdrawTypeEnums = [
  {
    label: "银行卡",
    value: 0,
  },
  {
    label: "数字货币",
    value: 1,
  },
  {
    label: "支付宝",
    value: 2,
  },
  {
    label: "微信",
    value: 3,
  },
  // {
  //   label: "支付宝转账",
  //   value: 4,
  // },
];

export const rechargeStatusEnums = [
  
    {
      label: "成功",
      value: 0,
    },
    {
      label: "审核",
      value: 1,
    },
    {
      label: "拒绝",
      value: 2,
    },
  
]

export const fundingTypesEnum = [
  {
    label: '作业存款',
    value: '1'
  },{
    label: '作业提款',
    value: '2'
  },{
    label: '签到',
    value: '3'
  },{
    label: '购买产品',
    value: '4'
  },{
    label: '产品收益',
    value: '5'
  },{
    label: '幸运抽奖',
    value: '6'
  },{
    label: '拼团',
    value: '7'
  },{
    label: '邀请首充返利',
    value: '8'
  },{
    label: '邀请盈利返利',
    value: '9'
  },{
    label: '团队长升级',
    value: '10'
  },{
    label: '余额宝本金',
    value: '11'
  },{
    label: '余额宝收益',
    value: '12'
  },{
    label: '福利发放',
    value: '13'
  },{
    label: '充值返利',
    value: '14'
  },{
    label: '转账',
    value: '15'
  },{
    label: '推广',
    value: '16'
  },{
    label: '积分',
    value: '17'
  }
]


export const rolesEnums = [
  
  {
    label: "管理员",
    value: 1,
  },
  {
    label: "代理",
    value: 2,
  },

]



export const sendStatusEnums = [
  
  // {
  //   label: "进行中",
  //   value: 0,
  // },
  {
    label: "完成出仓",
    value: 1,
  },
  {
    label: "准备出仓",
    value: 2,
  },
]

export const handlersBooleanEnums = [
  
  {
    label: "否",
    value: -1,
  },
  {
    label: "是",
    value: 1,
  },

]

export const saleStatusEnums = [
  
  {
    label: "开启",
    value: 0,
  },
  {
    label: "关闭",
    value: 1,
  },
  {
    label: "售馨",
    value: 2,
  },

]

export const clocksEnums = [
  
  {
    label: "未冻结",
    value: 0,
  },
  {
    label: "已冻结",
    value: 1,
  }
]

export const roleEnums = [
  
  {
    label: "超级管理",
    value: 0,
  },
  {
    label: "普通管理",
    value: 1,
  }
]

export const projectItemStatusEnums = [
  
  {
    label: "进行中",
    value: 0,
  },
  {
    label: "已结束",
    value: 1,
  },
  {
    label: "待结算",
    value: 2,
  }
]

export const userLevelTypeEnums = [
  {
    label: "自己",
    value: 0,
  },
  {
    label: "下级",
    value: 1,
  }
]

export const profitTypeEnums = [
  {
    label: "每期返息到期返本",
    value: 0,
  },
  {
    label: "一次性到期返本息",
    value: 1,
  },
  {
    label: "每期返息不返本",
    value: 2,
  },
  {
    label: "每期返息到期双倍本金",
    value: 3,
  }
]

export const lottoryTypeBEnums = [
  {
    label: "资金",
    value: 0,
  },
  {
    label: "集字",
    value: 1,
  },
  {
    label: "加息券",
    value: 2,
  }
]

export const profitTypeBEnums = [
  {
    label: "每期返息到期返本",
    value: 0,
  },
  {
    label: "到期返本返息",
    value: 1,
  }
]

export const cycleTimeEnums = [
  {
    label: "小时",
    value: 3600,
  },
  {
    label: "一天",
    value: 86400,
  },
  {
    label: "一周",
    value: 604800,
  },
  {
    label: "30天",
    value: 2592000,
  },
  {
    label: "一年",
    value: 31536000,
  }
]

export const payChannelStyleEnums = [
  {
    label: "框架",
    value: 0,
  },
  {
    label: "跳转",
    value: 1,
  },
  {
    label: "内置账号",
    value: 2
  },
  // {
  //   label: "支付宝转账",
  //   value: 3
  // }
]

export const couponStatusEnums = [
  {
    label: "未使用",
    value: 0,
  },
  {
    label: "已过期",
    value: 1,
  }
]

export const transferStatusEnums = [
  {
    label: "申请中",
    value: 0,
  },
  {
    label: "同意",
    value: 1,
  },
  {
    label: "拒绝",
    value: 2,
  }
]


export const getLabelByVal = (val, enums) => {
  return enums.filter((item) => item.value == val)[0]?.label;
};
