<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\MoneyClass;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\BCalculator;
use think\facade\Log;
use think\queue\Job;

/**
 * 投资返利
 * Class TeamRebateJob
 * @package app\job
 */
class TeamRebateJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job,  $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec()
    {
        $uid        = $this->info['uid'];
        $money      = $this->info['money'];

        $UserRepo   = new UserRepository();

        $userInfo   = $UserRepo->findById($uid);

        if (empty($userInfo))
        {
            return;
        }

        $SystemSetRepo = new SystemSetRepository();

        $invite        = [];
        $where         = [];
        $where[]       = ['key', '=','yaoqing_yingli_one'];

        $invite[1]    = $SystemSetRepo->valueByCondition($where, 'val');

        $where         = [];
        $where[]       = ['key', '=','yaoqing_yingli_two'];
        $invite[2]     = $SystemSetRepo->valueByCondition($where, 'val');


        $where         = [];
        $where[]       = ['key', '=','yaoqing_yingli_three'];
        $invite[3]     = $SystemSetRepo->valueByCondition($where, 'val');

        $txt = [
            1 => [
                'txt' => '一级会员分红收益: +',
                'do'  => MoneyClass::REBATE_V1,
            ],
            2 => [
                'txt' => '二级会员分红收益: +',
                'do'  => MoneyClass::REBATE_V2,
            ],
            3 =>  [
                'txt' => '三级会员分红收益: +',
                'do'  => MoneyClass::REBATE_V3,
            ],
        ];

        for ($i = 1; $i <= 3; $i++)
        {
            $pUser = $UserRepo->findByCondition(['id' => $userInfo['v' . $i . '_id']]);

            if (empty($pUser))
            {
                continue;
            }

            $amount       = BCalculator::calc($money)->mul($invite[$i])->div(100)->result();

            $MoneyLogRepo = new MoneyLogRepository();

            $phone        = substr($userInfo['phone'],0,3).'****'.substr($userInfo['phone'],strlen($userInfo['phone'])-4,4);

            $desc         = $phone . $txt[$i]['txt'] . $amount . "元" ;

            $class        = $txt[$i]['do'];

            $res          = $MoneyLogRepo->fund($pUser['id'], $amount, $class , $this->info['order_id'], $desc);


            if ($res['code'])
            {
                Log::channel('job')->error('邀请返佣失败' . json_encode($this->info));
                return;
            }

            $UserInfoRepo = new UserInfoRepository();

            $update  = [
                'team_invite'       => $amount,
                'team_invite_v'.$i  => $amount,
            ];

            $res = $UserInfoRepo->statistic($pUser['id'], $update);


            if ($res['code'])
            {
                Log::channel('job')->error('邀请返佣统计失败' . json_encode($this->info));
                return;
            }

        }


    }



    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('TeamRebateJob:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('TeamRebateJob:' . $this->info['uid']);
    }

}

