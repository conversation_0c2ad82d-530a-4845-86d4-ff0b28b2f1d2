<?php

namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\model\YueBao;
use app\common\repository\MoneyLogRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\YueBaoRepository;
use app\common\utils\Order;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;

/**
 * 余额宝
 */
class YueBaoService
{


    /**
     * 余额宝存入
     * @param array $params
     * @return array
     */
    public function deposit(array $params): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
        $UserInfoRepo       = new UserInfoRepository();


        Db::startTrans();


        try {

            $MoneyLogRepo = new MoneyLogRepository();

            $res          = $MoneyLogRepo->fund($user['id'], $params['money'],MoneyClass::YUEBAO_DEPOSIT, Order::uniqueNo(),'余额宝转入:'.$params['money'].'元');

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $update = [
                'yuebao_time'  => time() + 86400,
                'update_time'  => time(),
            ];

            $res = $UserInfoRepo->updateById($user['id'], $update);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('操作失败');
            }

            $res = $UserInfoRepo->statistic($user['id'],['yuebao_money' => $params['money']]);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('操作失败');
            }


            $info         = "余额宝存入:" . $params['money'];

            $insert      = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $user['is_test'],
                'type'          => YueBao::TYPE['deposit'],
                'money'         => $params['money'],
                'finish_money'  => $params['money'],
                'info'          => $info,
                'create_time'   => time(),
                'update_time'   => time(),
                'settle_time'   => time() + 86400,
            ];

            $YueBaoRepo = new YueBaoRepository();
            $res        = $YueBaoRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('操作失败');
            }

            Db::commit();

            return Result::success();

        }catch (\Exception $exception)
        {
            Record::exception('index',$exception,'YueBaoService');
            return Result::fail('操作失败');
        }

    }

    /**
     * 取出
     * @param array $params
     * @return array
     */
    public function withdraw(array $params): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
        $userInfo           = $UserRepo->userInfoByHeader();
        $YueBaoRepo         = new YueBaoRepository();
        $UserInfoRepo       = new UserInfoRepository();
        $MoneyLogRepo       = new MoneyLogRepository();

        if ($params['money'] > $userInfo['yuebao_money'])
        {
            return Result::fail('余额宝额度不足');
        }

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['type', '=', 1];
        $where[]    = ['finish_money', '>', 0];

        $lists      = $YueBaoRepo->selectByCondition($where,'*',['id' => 'desc']);

        $money      = $params['money'];
        $bills      = [];

        foreach ($lists as $list)
        {
            // 5000 - 1000 = 4000
            // 4000 - 1000 = 3000

            // 1000 - 5000 = -4000
            // 1000 - 1000
            $left    = $money - $list['finish_money'];

            if ($left > 0)
            {
                $bills[] = [
                    'id'            => $list['id'],
                    'finish_money'  => 0,
                ];
            }
            else
            {
                $bills[] = [
                    'id'            => $list['id'],
                    'finish_money'  => abs($left),
                ];
                break;
            }

            $money   = $left;
        }

        Db::startTrans();


        try {

            //更新账单
            foreach ($bills as $bill)
            {
                $update = [
                    'finish_money' => $bill['finish_money'],
                    'update_time'  => time(),
                ];

                $res = $YueBaoRepo->updateById($bill['id'], $update);

                if (!$res)
                {
                    Db::rollback();
                    return Result::fail('账单更新失败');
                }
            }

            $res          = $MoneyLogRepo->fund($user['id'], $params['money'],MoneyClass::YUEBAO_WITHDRAWAL, Order::uniqueNo(),'余额宝转出:'.$params['money'].'元');

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }


            $res = $UserInfoRepo->statistic($user['id'],['yuebao_money' => - $params['money']]);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('操作失败');
            }


            $info         = "余额宝取出:" . $params['money'];

            $insert      = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $user['is_test'],
                'type'          => YueBao::TYPE['withdrawals'],
                'money'         => $params['money'],
                'finish_money'  => 0,
                'info'          => $info,
                'create_time'   => time(),
                'update_time'   => time(),
            ];

            $res        = $YueBaoRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('操作失败');
            }

            Db::commit();

            return Result::success();

        }catch (\Exception $exception)
        {
            Record::exception('index',$exception,'YueBaoService');
            return Result::fail('操作失败');
        }

    }

    /**
     * 余额宝记录
     * @param $type
     * @return array
     */
    public function record($type): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $YueBaoRepo = new YueBaoRepository();

        $where   = [];
        $where[] = ['uid', '=', $user['id']];

        if (isset($type) && is_numeric($type))
        {
            $where[] = ['type', '=', $type];
        }

        $data    = $YueBaoRepo->paginates($where);


        return Result::success($data);
    }
}