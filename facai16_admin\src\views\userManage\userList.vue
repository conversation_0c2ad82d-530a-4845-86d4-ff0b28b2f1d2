<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="searchList"
  >

    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.sfz_name"
          placeholder="请输入用户身份证名字"
          prop="sfz_name"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          prop="username"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          prop="phone"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.level"
          placeholder="会员等级"
          clearable
        >
          <el-option
            v-for="item in memberLevels"
            :label="item.title"
            :value="item.id"
            :key="item.title"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.level_team"
          placeholder="团队长等级"
          clearable
        >
          <el-option
            v-for="item in teamLevels"
            :label="item.title"
            :value="item.id"
            :key="item.title"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_test"
          placeholder="账号类型"
          clearable
        >
          <el-option
            v-for="item in accountTypeEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.online_time"
          placeholder="三天未上线"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_signin"
          placeholder="是否签到"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sort_point"
          placeholder="积分排序"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sort_yuebao"
          placeholder="余额包排序"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sort_money"
          placeholder="可提余额排序"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sort_frozen_money"
          placeholder="可用余额排序"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sort_coupon_num"
          placeholder="加息券排序"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.sfz_status"
          placeholder="实名状态"
          clearable
        >
          <el-option
            v-for="item in userlistSfzStatusEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_online"
          placeholder="是否在线用户"
          clearable
        >
          <el-option
            v-for="item in booleanEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.invite"
          placeholder="请输入邀请码"
          prop="invite"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item>
        <el-select
          v-model="searchForm.level"
          prop="invite"
          placeholder="用户关系"
          clearable
        >
          <el-option label="下级" value="1" />
          <el-option label="下下级" value="2" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="注册开始时间"
            style="width: 100%"
            prop="starttime"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            prop="endtime"
            placeholder="注册结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        v-permission
        icon="CirclePlus"
        @click="editItems('add', {})"
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="100">
        <template #default="scope">
          <span :style="{color: scope.row.is_test == 1 ? 'red': ''}">{{ scope.row.username }}</span>
          <el-tag v-if="scope.row.online" type="success">在线</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="nickname" label="昵称" width="100" />
      <el-table-column prop="phone" label="手机号码" width="100" />
      <el-table-column prop="is_test" label="账号类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.is_test, accountTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="invite" label="邀请码" width="70" />
      <el-table-column prop="level_name" label="用户等级" width="70">
        <template #default="scope">
          {{ scope.row.level_name }}
        </template>
      </el-table-column>
      <el-table-column label="持有项目人数" prop="not_finish" width="180" />
      <el-table-column label="未完结项目" prop="item_not_finish" width="180" />
      <el-table-column prop="addtime" label="注册时间" width="220">
        <template #default="scope">
          注册时间：{{ scope.row.create_at }}
          <br />
          注册IP：{{ scope.row.register_ip }}
          <br />
          登录时间：{{ scope.row.login_time }}
          <br />
          登录地址：{{ scope.row.ip_address }}
        </template>
      </el-table-column>
      <el-table-column label="会员统计" width="180">
        <template #default="scope"> 余额：{{ scope.row.money }} </template>
      </el-table-column>
      <el-table-column label="充值金额" prop="infos.recharge_money" width="180" />
      <el-table-column label="提款金额" prop="infos.withdraw_money" width="180" />
      <el-table-column label="投资金额" prop="infos.invest_money" width="180" />
      <el-table-column label="收入" prop="infos.income_money" width="180" />

      <el-table-column prop="address" label="推荐人" width="180">
        <template #default="scope">
          上级:{{ scope.row.v1_id }}/{{ scope.row.v1_name }} <br />
          上上级:{{ scope.row.v2_id }}/{{ scope.row.v2_name }} <br />
        </template>
      </el-table-column>
      <el-table-column prop="address" label="下线统计" width="140">
        <template #default="scope">
          <p class="link" @click="viewTeams('levelOne', scope.row, 1)">
            一级会员（{{ scope.row.team1 }}）
          </p>
          <br />
          <p class="link" @click="viewTeams('levelTwo', scope.row, 2)">
            二级会员（{{ scope.row.team2 }}）
          </p>
          <p class="link" @click="viewTeams('levelTwo', scope.row, 3)">
            三级会员（{{ scope.row.team3 }}）
          </p>
          <p class="link" @click="viewTeams('levelTwo', scope.row, 4)">
            四级会员（{{ scope.row.team4 }}）
          </p>
          <p class="link" @click="viewTeams('levelTwo', scope.row, 5)">
            五级会员（{{ scope.row.team5 }}）
          </p>
        </template>
      </el-table-column>
      <el-table-column v-if="currentUser.role == 0" fixed="right" prop="address" width="250" label="操作">
        <template #default="scope">
          <div class="table-handle-td">
            <el-button
              type="primary"
              icon="Edit"
              v-permission
              @click="editItems('state', scope.row)"
              >状态</el-button
            >
            <el-button
              type="warning"
              icon="Wallet"
              v-permission
              @click="editItems('money', scope.row)"
              >金额</el-button
            >
            <el-button
              type="info"
              icon="User"
              v-permission
              @click="editItems('info', scope.row)"
              >信息</el-button
            >
            <el-button
              color="#626aef"
              @click="editItems('count', scope.row)"
              icon="Pointer"
              v-permission
              :dark="isDark"
              >统计</el-button
            >
            <el-button
              type="danger"
              @click="editItems('transfer', scope.row)"
              icon="Sort"
              v-permission
              :dark="isDark"
              >转移</el-button
            >

            <el-button
              color="#626aef"
              @click="toList('/chargeList', scope.row)"
              v-permission
              :dark="isDark"
              >普通充值</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/UsdtChargeList', scope.row)"
              v-permission
              :dark="isDark"
              >usdt充值</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/withdrawList', scope.row)"
              v-permission
              :dark="isDark"
              >提现记录</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/transferLogLists', scope.row)"
              v-permission
              :dark="isDark"
              >转账记录</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/discountList', scope.row)"
              v-permission
              :dark="isDark"
              >卡券记录</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/lottoryDetails', scope.row)"
              v-permission
              :dark="isDark"
              >抽奖记录</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/buyRecordList', scope.row)"
              v-permission
              :dark="isDark"
              >投资记录</el-button
            >
            <el-button
              color="#626aef"
              @click="toList('/yuebaoLogLists', scope.row)"
              v-permission
              :dark="isDark"
              >余额宝记录</el-button
            >
            <!-- <el-icon @click="deleteAction(scope.row)">
              <Delete />
            </el-icon> -->
          </div>
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="操作" :width="['levelOne', 'levelTwo'].includes(dialogType) ? '90%' : 1050">
      <editUserForm
        :key="currentRow"
        v-if="dialogType === 'add' || dialogType === 'edit'"
        ref="editUserFormRef"
        :type="dialogType"
        :item="currentRow"
      />
      <userMoneyForm
        :key="currentRow"
        v-if="dialogType === 'money'"
        ref="userMoneyFormRef"
        :item="currentRow"
      />
      <userStateForm
        :key="currentRow"
        v-if="dialogType === 'state'"
        ref="userStateFormRef"
        :item="currentRow"
      />
      <userInfoForm
        :key="currentRow"
        v-if="dialogType === 'info'"
        ref="userInfoFormRef"
        :item="currentRow"
      />
      <userCountForm
        :key="currentRow"
        v-if="dialogType === 'count'"
        ref="userCountFormRef"
        :item="currentRow"
      />
      <userTransfer
        :key="currentRow"
        v-if="dialogType === 'transfer'"
        ref="transferFormRef"
        :item="currentRow"
      />
      <userLevelOne
        :key="currentRow"
        v-if="dialogType === 'levelOne'"
        ref="levelOneRef"
        :item="currentRow"
      />
      <userLevelTwo
        :key="currentRow"
        v-if="dialogType === 'levelTwo'"
        ref="levelTwoRef"
        :level='teamLevel'
        :item="currentRow"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import editUserForm from "./components/userList/editUserForm.vue";
import userMoneyForm from "./components/userList/userMoneyForm.vue";
import userStateForm from "./components/userList/userStateForm.vue";
import userInfoForm from "./components/userList/userInfoForm.vue";
import userCountForm from "./components/userList/userCountForm.vue";
import userTransfer from "./components/userList/userTransfer.vue";
import userLevelOne from "./components/userList/userLevelOne.vue";
import userLevelTwo from "./components/userList/userLevelTwo.vue";
import { sfzStatusEnums, accountTypeEnums, userlistSfzStatusEnums, booleanEnums, getLabelByVal } from "@/config/enums";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from 'vue-router'

const searchForm = ref({
  sfz_name: "",
  username: "",
  phone: "",
  level: "",
  level_team: "",
  is_test: "",
  online_time: "",
  is_signin: "",
  sort_point: "",
  sort_yuebao: "",
  sort_money: "",
  sort_frozen_money: "",
  sfz_status: "",
  is_online: "",
  invite: "",
  starttime: "",
  endtime: "",
});
const tableData = ref([]);
const dialogFlag = ref(false);
const dialogType = ref("add");
const currentRow = ref({});

const editUserFormRef = ref(null);
const userMoneyFormRef = ref(null);
const userStateFormRef = ref(null);
const userCountFormRef = ref(null);
const userInfoFormRef = ref(null);
const transferFormRef = ref(null);
const levelOneRef = ref(null);
const levelTwoRef = ref(null);

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"))
const { proxy } = getCurrentInstance();

onMounted(() => {
  getList();
  getMemberLevels()
  getTeamLevels()
});

const router = useRouter()

const toList = (str, row) => {
  router.push(`${str}?phone=${row.phone}`)
}

const memberLevels = ref([])
const getMemberLevels = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/Level/getLevelLists",
  });
  memberLevels.value =  res.data.data
}

const teamLevel = ref(1)
const viewTeams = (str, row, level) => {
  teamLevel.value = level
  editItems(str, row)
}

const teamLevels = ref([])
const getTeamLevels = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/Team/getTeamLists",
  });
  teamLevels.value =  res.data.data
}

const searchList = () => {
  getList();
};

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    method: "get",
    url: "/user/getUserLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};

const editItems = (type, row = {}) => {
  currentRow.value = row;
  dialogType.value = type;

  dialogFlag.value = true;
};

const submitForm = async () => {
  if (dialogType.value == "money") {
    userMoneyFormRef.value.formRef.validate(async (valid) => {
      if (valid) {
        const data = userMoneyFormRef.value.form;
        const res = await proxy.$http({
          method: "post",
          url: "MoneyLog/addMoney",
          data: {
            uid: data.id,
            class_id: data.class_id,
            money: data.money3,
          },
        });
        dialogFlag.value = false;
        if (res.code == 0) {
          ElMessage({
            type: "success",
            message: res.msg,
          });
        } else {
          ElMessage({
            type: "error",
            message: res.msg,
          });
        }
        getList();
      }
    });
  } else if (dialogType.value == "add" || dialogType.value == "edit") {
    // 新增用户
    const url = dialogType.value == "add" ? "User/addUser" : "";
    const data = editUserFormRef.value.form;
    if (dialogType.value == "add") {
      data.id = null;
    }
    const res = await proxy.$http({
      method: "post",
      url: url,
      data: {
        username: data.username,
        phone: data.phone,
        invite: data.invite,
        password: data.password,
      },
    });

    if (res.code == 0) {
      dialogFlag.value = false;
      getList();
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      dialogFlag.value = false;
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
  } else if (dialogType.value == "state") {
    // 用户状态
    const data = userStateFormRef.value.form;
    const res = await proxy.$http({
      method: "post",
      url: "user/updateUserState",
      data: {
        uid: data.id,
        ban_buy: data.ban_buy,
        ban_sigin: data.ban_sigin,
        ban_raffle: data.ban_raffle,
        ban_login: data.ban_login,
        ban_invite: data.ban_invite,
        ban_recharge: data.ban_recharge,
        ban_withdraw: data.ban_withdraw,
        ban_exchange: data.ban_exchange,
        sfz_status: data.sfz_status,
        kick_out: data.kick_out,
        is_test: data.is_test,
        is_valid_user: data.is_valid_user,
      },
    });
    dialogFlag.value = false;
    if (res.code == 0) {
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
    getList();
  } else if (dialogType.value == "info") {
    // 用户信息
    const data = userInfoFormRef.value.form;
    const res = await proxy.$http({
      method: "post",
      url: "user/updateUser",
      data: {
        id: data.id,
        username: data.username,
        nickname: data.nickname,
        password: data.password,
        pin: data.pin,
        phone: data.phone,
        email: data.email,
        sfz_name: data.sfz_name,
        sfz_number: data.sfz_number,
        avatar: data.avatar,
        level: data.level,
      },
    });
    dialogFlag.value = false;
    if (res.code == 0) {
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
    getList();
  } else if (dialogType.value == "count") {
    // 用户统计
    const data = userCountFormRef.value.form;
    const res = await proxy.$http({
      method: "post",
      url: "user/updateUserInfo",
      data: {
        id: data.id,
        uid: data.id,
        username: data.username,
        shares_money: data.shares_money,
        yuebao_money: data.yuebao_money,
        yuebao_earn: data.yuebao_earn,
        yuebao_time: data.yuebao_time,
        yuebao_num: data.yuebao_num,
        recharge_num: data.recharge_num,
        withdraw_money: data.withdraw_money,
        withdraw_num: data.withdraw_num,
        signin_money: data.signin_money,
        signin_num: data.signin_num,
        raffle_money: data.raffle_money,
        raffle_num: data.raffle_num,
        team_invite: data.team_invite,
        team_item: data.team_item,
        team_item_v1: data.team_item_v1,
        team_item_v2: data.team_item_v2,
        team_item_v3: data.team_item_v3,
        team_invite_v1: data.team_invite_v1,
        team_invite_v2: data.team_invite_v2,
        team_invite_v3: data.team_invite_v3,
        team_invite_num: data.team_invite_num,
        team_invite_user: data.team_invite_user,
        team_recharge: data.team_recharge,
        team_recharge_v1: data.team_recharge_v1,
        team_recharge_v2: data.team_recharge_v2,
        team_recharge_v3: data.team_recharge_v3,
        income_money: data.income_money,
        invest_money: data.invest_money,
        invest_not_finish: data.invest_not_finish,
        invest_num: data.invest_num,
        user_points: data.user_points,
        user_coupon: data.user_coupon,
        bonus_num: data.bonus_num,
        bonus_money: data.bonus_money,
      },
    });
    dialogFlag.value = false;
    if (res.code == 0) {
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
    getList();
  } else if (dialogType.value == "transfer") {
    const data = transferFormRef.value.form;
    const res = await proxy.$http({
      method: "post",
      url: "user/userRemove",
      data: {
        from_user: data.from_user,
        to_user: data.to_user,
        // lv: data.lv,
      },
    });

    if (res.code == 0) {
      dialogFlag.value = false;
      getList();
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      dialogFlag.value = false;
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ElMessage({
        type: "success",
        message: "删除成功",
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};
</script>

<style lang="less" scoped>
.table-handle-td {
  display: flex;
  cursor: pointer;
  flex-wrap: wrap;
  button {
    margin-bottom: 10px;
    margin-left: 0;
    margin-right: 10px;
  }
}
</style>
