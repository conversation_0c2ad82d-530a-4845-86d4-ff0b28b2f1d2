<?php
namespace app\admin\service;

use app\common\repository\SystemUserRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 系统日志
 */
class SystemUserService
{

    /**
     * 用户记录
     * @param $username
     * @return array
     */
    public function getSystemUserLists($username): array
    {
        $where = [];

        if ($username)
        {
            $where[] = ['username', '=', $username];
        }

        $SystemUserRepo = new SystemUserRepository();
        $data           = $SystemUserRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 用户信息
     * @param $id
     * @return array
     */
    public function getSystemUserInfo($id): array
    {
        $SystemUserRepo = new SystemUserRepository();
        $data        = $SystemUserRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加用户
     * @param $username
     * @param $password
     * @return array
     */
    public function addSystemUser($username, $password): array
    {

        $insert  = [
            'username'      => $username,
            'password'      => $password,
            'create_time'   => time(),
            'update_time'   => time(),
        ];

        $SystemUserRepo = new SystemUserRepository();
        $res            = $SystemUserRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新用户
     * @param $id
     * @param $username
     * @param $password
     * @return array
     */
    public function updateSystemUser($id,$username,$password): array
    {

        $update  = [
            'username'      => $username,
            'password'      => $password,
            'update_time'   => time(),
        ];

        $SystemUserRepo = new SystemUserRepository();
        $res            = $SystemUserRepo->updateById($id, $update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除用户
     * @param $id
     * @return array
     */
    public function deleteSystemUser($id): array
    {
        $SystemUserRepo = new SystemUserRepository();
        $res            = $SystemUserRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    
}
