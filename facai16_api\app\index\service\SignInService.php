<?php

namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\model\SignIn;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SignInGiftLogRepository;
use app\common\repository\SignInGiftRepository;
use app\common\repository\SignInRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\TeamRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class SignInService
{
    public function grandPrize()
    {

    }

    /**
     *
     * @return Json
     */
    public function signIn(): array
    {
        $today              = date('Y-m-d 00:00:00', time());
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $SignInRepo         = new SignInRepository();


        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['create_time', '>=', $today];
        $where[]    = ['is_team', '=', SignIn::TEAM];

        $team       = $SignInRepo->countByCondition($where);


        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['create_time', '>=', $today];
        $where[]    = ['is_team', '=', SignIn::MEMBER];

        $member     = $SignInRepo->countByCondition($where);

        $data = [
            'team'   =>  $team,
            'member' =>  $member,
        ];

        return  Result::success($data);
    }

    /**
     * 签到记录
     * @return array
     */
    public function signInLog(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $SignInRepo         = new SignInRepository();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];

        $data       = $SignInRepo->paginates($where);

        return  Result::success($data);
    }

    /**
     * 礼品
     * @return array
     */
    public function gifts(): array
    {
        $SignInGiftRepo = new SignInGiftRepository();
        $data           = $SignInGiftRepo->selectByCondition();

        foreach ($data as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return  Result::success($data);
    }

    /**
     * 签到领取
     * @return array
     */
    public function sign(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $SignInRepo         = new SignInRepository();

        $MoneyLogRepo       = new MoneyLogRepository();

        $UserStateRepo      = new UserStateRepository();
        $isTest             = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $UserInfoRepo       = new UserInfoRepository();


        $where              = [];
        $where[]            = ['uid', '=', $user['id']];
        $where[]            = ['is_team', '=', SignIn::MEMBER];
        $last               = $SignInRepo->findByCondition($where,'*',['id'=>'desc']);

        $SystemSetRepo      = new SystemSetRepository();

        $signInMoney        = $SystemSetRepo->valueByCondition(['key' => 'signin_money'],'val');


        if (!empty($last)  && $last['create_time'] > strtotime(date('Y-m-d 00:00:00')))
        {
            return Result::fail('今日已经出勤过');
        }


        Db::startTrans();


        try {

            $insert = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'amount'        => $signInMoney,
                'is_test'       => $isTest,
                'is_team'       => SignIn::MEMBER,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $id = $SignInRepo->insertsGetId($insert);

            if (!$id)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $res = $MoneyLogRepo->fund($user['id'],$signInMoney,MoneyClass::SIGNIN, $id,"每日签到:获取" . $signInMoney. '元');

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $res = $UserInfoRepo->statistic($user['id'],['signin_num' => 1, 'signin_money' => $signInMoney]);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            // 提交事务
            Db::commit();


            //拼团成功
            return Result::success();

        } catch (\Exception $exception) {
            // 回滚事务
            Db::rollback();

            Record::exception('service', $exception, 'GoodsService->pay');

            //购买失败
            return Result::fail('签到失败');
        }
    }



    /**
     * 团长签到领取
     * @return array
     */
    public function signTeam(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $SignInRepo         = new SignInRepository();

        $MoneyLogRepo       = new MoneyLogRepository();


        $UserInfoRepo       = new UserInfoRepository();

        $where              = [];
        $where[]            = ['uid', '=', $user['id']];
        $where[]            = ['is_team', '=', SignIn::TEAM];
        $last               = $SignInRepo->findByCondition($where,'*',['id'=>'desc']);


        $TeamRepo           = new TeamRepository();
        $team               = $TeamRepo->findById($user['level_team']);

        $signInMoney        = $team['signin'] ?? 0;

        if ($signInMoney == 0 || $user['level_team'] == 0)
        {
            return Result::fail('团队等级未达标');
        }


        if (!empty($last)  && $last['create_time'] > strtotime(date('Y-m-d 00:00:00')))
        {
            return Result::fail('今日已经出勤过');
        }


        Db::startTrans();


        try {

            $insert = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'amount'        => $signInMoney,
                'is_test'       => $user['is_test'],
                'is_team'       => SignIn::TEAM,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $id = $SignInRepo->insertsGetId($insert);

            if (!$id)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $res = $MoneyLogRepo->fund($user['id'],$signInMoney,MoneyClass::SIGNIN, $id,"每日签到:获取" . $signInMoney. '元');

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $res = $UserInfoRepo->statistic($user['id'],['signin_num' => 1, 'signin_money' => $signInMoney]);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            // 提交事务
            Db::commit();


            //拼团成功
            return Result::success();

        } catch (\Exception $exception) {
            // 回滚事务
            Db::rollback();

            Record::exception('service', $exception, 'GoodsService->pay');

            //购买失败
            return Result::fail('签到失败');
        }
    }

    /**
     * 领取记录
     * @return array
     */
    public function record(): array
    {
        $UserRepo           = new UserRepository();
        $uid                = $UserRepo->userByHeader('id');

        $SignInGiftLogRepo  = new SignInGiftLogRepository();
        $data               = $SignInGiftLogRepo->paginates(['uid' => $uid]);


        foreach ($data['data'] as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return  Result::success($data);
    }

    /**
     * 礼物领取记录
     * @return array
     */
    public function giftLog(): array
    {
        $UserRepo           = new UserRepository();
        $uid                = $UserRepo->userByHeader('id');

        $SignInGiftLogRepo  = new SignInGiftLogRepository();
        $data               = $SignInGiftLogRepo->selectByCondition(['uid' => $uid]);

        return  Result::success($data);
    }

    /**
     * 签到礼品领取
     * @return void
     */
    public function gift($id): array
    {
        $SignInGiftRepo     = new SignInGiftRepository();
        $SignInGiftLogRepo  = new SignInGiftLogRepository();
        $UserRepo           = new UserRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $user               = $UserRepo->userByHeader();
        $userInfo           = $UserRepo->userInfoByHeader();
        $info               = $SignInGiftRepo->findById($id);

        if (empty($info))
        {
            return Result::fail('没有礼品信息');
        }

        $where   = [];
        $where[] = ['gift_id', '=', $id];
        $where[] = ['uid', '=', $user['id']];


        if ($SignInGiftLogRepo->findByCondition($where))
        {
            return Result::fail('无法重复领取');
        }

        if ($userInfo['signin_num'] < $info['days'])
        {
            return Result::fail('未达到领取条件');
        }


        Db::startTrans();

        try {

            $update = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $user['is_test'],
                'gift_id'       => $info['id'],
                'title'         => $info['title'],
                'img'           => $info['img'],
                'amount'        => $info['amount'],
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];



            $res = $SignInGiftLogRepo->inserts($update);


            if (!$res)
            {
                Db::rollback();
                return Result::fail('兑换失败');
            }

            $desc               = "礼品领取获得：" . $info['amount'];

            $res = $MoneyLogRepo->fund($user['id'], $info['amount'],MoneyClass::SIGNIN_GIFT, $info['id'], $desc);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['img']);
            }

            Db::commit();

            return Result::success();

        }catch (\Exception $exception)
        {
            Db::rollback();

            Record::exception('service', $exception, 'GoodsService->gift');

            //购买失败
            return Result::fail('兑换失败');
        }


    }

}