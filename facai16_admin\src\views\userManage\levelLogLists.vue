<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
  <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_test"
          placeholder="账号类型"
          clearable
        >
          <el-option
            v-for="item in accountTypeEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.lv_id"
          placeholder="等级"
          clearable
        >
          <el-option
            v-for="item in levelList"
            :label="item.title"
            :value="item.id"
            :key="item.title"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="用户ID" width="130" />
      <el-table-column prop="username" label="用户名" width="160" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="is_test" label="账号类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.is_test, accountTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="lv_id" label="等级ID" width="160" />
      <el-table-column prop="lv_name" label="等级名称" width="160" />
      <el-table-column label="类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.type, userLevelTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="desc" label="等级描述" width="160" />
      <el-table-column prop="admin_id" label="管理员id" width="160" />
      <el-table-column prop="admin_name" label="管理员名称" width="160" />
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { userLevelTypeEnums, accountTypeEnums, getLabelByVal } from "../../config/enums";

const searchForm = ref({
  username: "",
  is_test: "",
  lv_id: "",
  starttime: "",
  endtime: "",
});

onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);
const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Level/getLevelLogLists",
    params: {
      ...searchForm.value,
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};


const levelList = ref([])

const getLevelList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Level/getLevelLists"
  });
  tableLoading.value = false;
  if (res.code == 0) {
    levelList.value = res.data.data;
  }
};
getLevelList()
</script>

<style lang="less" scoped></style>
