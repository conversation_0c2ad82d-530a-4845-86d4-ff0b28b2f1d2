<?php
namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\TransferRepository;
use app\common\repository\UserRepository;
use app\common\utils\Order;
use app\common\utils\Result;
use app\index\controller\Transfer;
use think\facade\Db;

/**
 * 转账
 */
class TransferService
{

    /**
     * 转账
     * @param array $params
     * @return array
     */
    public function operate(array $params) :array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $where    = [];
        $where[]  = ['phone','=',$params['phone']];
        $toUser   = $UserRepo->findByCondition($where);

        if(!$toUser)
        {
            return Result::fail('转账用户不存在');
        }

        if($toUser['id'] == $user['id'])
        {
            return Result::fail('不能给自己');
        }


        if ($user['level'] == 0)
        {
            return Result::fail('vip会员才可以转账');
        }

        $SystemSetRepo    = new SystemSetRepository();
        $minTransfer      = $SystemSetRepo->valueByCondition(['key'=> 'min_transfer'],'val');


        if($params['money'] < $minTransfer)
        {
            return Result::fail('最小转账' . $minTransfer);
        }


        $where   = [];
        $where[] = ['uid','=',$user['id']];
        $where[] = ['status','=', 0];

        $TransferRepo = new TransferRepository();
        $_count       = $TransferRepo->countByCondition($where);

        if($_count > 0)
        {
            return Result::fail('有转账订单在审核中');
        }

        try {

            Db::startTrans();

            if ($user['is_test'] == 1)
            {
                $insert = [
                    'uid'          => $user['id'],
                    'username'     => $user['username'],
                    'phone'        => $user['phone'],
                    'is_test'      => $user['is_test'],
                    'to_uid'       => $toUser['id'],
                    'to_username'  => $toUser['username'],
                    'to_phone'     => $toUser['phone'],
                    'money'        => $params['money'],
                    'status'       => 0,
                    'create_time'  => time(),
                    'update_time'  => time()
                ];

                $TransferRepo = new TransferRepository();
                $id           = $TransferRepo->insertsGetId($insert);

                if (!$id)
                {
                    Db::rollback();
                    return Result::fail('添加失败');
                }

                // 提交事务
                Db::commit();

                return Result::success();
            }
            else
            {
                $insert = [
                    'uid'          => $user['id'],
                    'username'     => $user['username'],
                    'phone'        => $user['phone'],
                    'is_test'      => $user['is_test'],
                    'to_uid'       => $toUser['id'],
                    'to_username'  => $toUser['username'],
                    'to_phone'     => $toUser['phone'],
                    'money'        => $params['money'],
                    'status'       => 1,
                    'create_time'  => time(),
                    'update_time'  => time()
                ];

                $TransferRepo = new TransferRepository();
                $id           = $TransferRepo->insertsGetId($insert);

                if (!$id)
                {
                    Db::rollback();
                    return Result::fail('添加失败');
                }

                $MoneyLogRepo   = new MoneyLogRepository();

                $phone = substr($toUser['phone'],0,3).'****'.substr($toUser['phone'],strlen($toUser['phone'])-4,4);
                $txt   = '用户转账(出):' . $params['money'] . '到' . $phone . '元';
                $res   =  $MoneyLogRepo->fund($user['id'],$params['money'],MoneyClass::TRANSFER_OUT, $id, $txt);

                if ($res['code'])
                {
                    Db::rollback();
                    return Result::fail($res['msg']);
                }

                $txt   = '用户转账(进):' . $params['money'] . '收' . $phone. '元';
                $res   =  $MoneyLogRepo->fund($toUser['id'],$params['money'],MoneyClass::TRANSFER_IN, $id,$txt);

                if ($res['code'])
                {
                    Db::rollback();
                    return Result::fail($res['msg']);
                }

                // 提交事务
                Db::commit();
            }

            return Result::success();

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return Result::fail('发送失败');
        }

    }

    /**
     * 记录
     * @return array
     */
    public function record(): array
    {
        $UserRepo             = new UserRepository();
        $user                 = $UserRepo->userByHeader();

        $TransferRepo           = new TransferRepository();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $data       = $TransferRepo->paginates($where);

        return  Result::success($data);
    }
}