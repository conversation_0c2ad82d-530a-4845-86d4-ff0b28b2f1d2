<template>
	<view>
		
		
		
		<view class="box fc" :style="{width:boxWidh,transform: `translateX(${moveX}px)`}">
			<view class="w-510 h-106 fcc" v-for="(item,index) in list" :key="index">
				<view class="sign-item">
					<view class="fc pl-107">
						<view>
							<view class="t-24 lh-33 c2">
								获得 {{item.score}} 积分
							</view>
							<view class="fc t-22 lh-30 c9">
								<text class="mr-15 w-100 ddd">{{item.name}}</text><text>{{item.date}}签到成功</text>
							</view>
						</view>
					</view>
					<view class="size-96 f-pa left-6 top-[-6rpx]">
						<uv-avatar :src="item.img" size="96rpx"></uv-avatar>
					</view>
				</view>
			</view>
			<view class="w-510 h-106 fcc" v-for="(item,index) in list" :key="index">
				<view class="sign-item">
					<view class="fc pl-107">
						<view>
							<view class="t-24 lh-33 c2">
								获得 {{item.score}} 积分
							</view>
							<view class="fc t-22 lh-30 c9">
								<text class="mr-15 w-100 ddd">{{item.name}}</text><text>{{item.date}}签到成功</text>
							</view>
						</view>
					</view>
					<view class="size-96 f-pa left-6 top-[-6rpx]">
						<uv-avatar :src="item.img" size="96rpx"></uv-avatar>
					</view>
				</view>
			</view>
		</view>
		
		
		
	</view>
</template>

<script>
	export default {
		name:"sign-item-slide",
		props: {
			list:{
				type:Array,
				default:function(){
					return []
				}
			},
			x:{
				type:Number,
				default:0
			}
		},
		data() {
			return {
				// boxWidh:0,
				moveX:0,
				
				timer:null
			};
		},
		computed: {
			boxWidh() {
				var a  =  uni.upx2px(510)
				return (this.list.length + 2) * 2 * a  + 'px'
			}
		},
		mounted() {
			this.moveX = this.x
			if(this.list && this.list.length > 0){
				var that = this
				this.timer = setInterval(()=>{
					that.moveX -- 
					var a  =  uni.upx2px(510) * this.list.length
					if(that.moveX + a < 0){
						that.moveX = 0
					}
				},20)
			}
		}
	}
</script>

<style lang="scss">

</style>