<?php

namespace app\admin\middleware;

use app\common\repository\SystemUserRepository;
use Closure;
use think\exception\HttpResponseException;
use think\facade\Log;
use think\helper\Str;
use think\Request;
use think\Response;
use think\response\Html;
use <PERSON><PERSON>cha<PERSON>\IpLocationZh\Ip;


/**
 * 登入中间件
 * Class OperationRecordMiddleware
 * @package app\admin\middleware
 */
class OperationRecordMiddleware
{

    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next): Response
    {

        $reqUri    = \think\facade\Request::param();


        if (\think\facade\Request::isPost())
        {
            $admin                  = (new SystemUserRepository())->userByHeader();
            $ip                     = \think\facade\Request::ip();

            $reqUri['_url']         = \think\facade\Request::url();
            $reqUri['_id']          = $admin['id'] ?? '***';
            $reqUri['_username']    = $admin['username'] ?? '***';
            $reqUri['_ip']          = $ip;
            $reqUri['_ip_address']  = join(',',array_filter(Ip::find($ip)));
            Log::channel('operation')->notice(json_encode($reqUri,320));
        }



        return $next($request);
    }

}