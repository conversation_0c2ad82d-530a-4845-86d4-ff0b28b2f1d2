<template>
    <el-form
      label-width="80px"
      :inline="true"
      :model="form"
      class="demo-form-inline"
    >
      <el-form-item label="支付名称" required>
        <el-input v-model="form.title" />
      </el-form-item>
      <el-form-item label="支付编码" required>
        <el-input v-model="form.code" />
      </el-form-item>
      <el-form-item label="支付账号" required>
        <el-select v-model="form.accounts" placeholder="支付账号" clearable>
          <el-option
            v-for="item in accountList"
            :label="item.title"
            :key="item.id"
            :value="item.id + ''"
          />
        </el-select>
      </el-form-item>


      <el-form-item
        label="开启状态"
        prop="status"
      >
        <el-select v-model="form.status" placeholder="开启状态" clearable>
          <el-option
            v-for="item in openEnums"
            :label="item.label"
            :key="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="支付方式"
        prop="style"
      >
        <el-select v-model="form.style" placeholder="支付方式" clearable>
          <el-option
            v-for="item in payChannelStyleEnums"
            :label="item.label"
            :key="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="支付上游"
        prop="upper_id"
      >
        <el-select v-model="form.upper_id" placeholder="支付上游" clearable>
          <el-option
            v-for="item in uppersEnum"
            :label="item.title"
            :key="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="充值类型"
        prop="class_id"
      >
        <el-select v-model="form.class_id" placeholder="充值类型" clearable>
          <el-option
            v-for="item in typesEnum"
            :label="item.title"
            :key="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最大充值金额" required>
        <el-input v-model="form.max" />
      </el-form-item>
      <el-form-item label="最小充值金额" required>
        <el-input v-model="form.min" />
      </el-form-item>
    </el-form>
  </template>
  
  <script setup>
  import { getCurrentInstance, nextTick, onMounted, ref } from "vue";
  import { openEnums, payChannelStyleEnums, getLabelByVal } from "@/config/enums";
  
  const form = ref({
    code: "",
    status: "",
    upper_id: "",
    class_id: "",
    min: "",
    max: "",
    style: 1,
    accounts: '',
    title: '',
    style: '',
  });
  const props = defineProps(["item"]);
  
  onMounted(() => {
    nextTick(() => {
      form.value = Object.assign(form, props.item);
    });
    getTYpesEnum()
    getUppersEnum()
    getAccountList()
  });
  
  const typesEnum = ref([])

const getTYpesEnum = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: '/PaymentClass/getPaymentClassLists'
    })
    if (res.code == 0) {
        typesEnum.value = res.data.data
    }
}

const accountList = ref([])
const getAccountList = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/PaymentAccount/getPaymentAccountLists",
  });
  if (res.code == 0) {
    accountList.value = res.data.data;
  }
};

const { proxy } = getCurrentInstance()
const uppersEnum = ref([])

const getUppersEnum = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: '/PaymentUpper/getPaymentUpperLists'
    })
    if (res.code == 0) {
      uppersEnum.value = res.data.data
    }
}
  
  defineExpose({ form });
  </script>
  
  <style lang="less" scoped>
  .demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
  }
  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }
  
  .demo-form-inline .el-select {
    --el-select-width: 220px;
  }
  /deep/ .el-radio-group {
    width: 220px;
  }
  .form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
  }
  </style>
  