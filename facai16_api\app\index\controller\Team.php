<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\TeamService;
use think\facade\Request;
use think\response\Json;

/**
 * 团队
 */
class Team
{
    /**
     * 列表
     * @return Json
     */
    public function lists(): <PERSON><PERSON>
    {
        $params       = Request::only(['lv' => 1, ]);
        $TeamService  = new TeamService();
        $result       = $TeamService->lists($params['lv']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 下级列表
     * @return Json
     */
    public function blow(): <PERSON><PERSON>
    {
        $params       = Request::only(['lv' => 1, 'uid' => 0]);
        $TeamService  = new TeamService();
        $result       = $TeamService->blow($params['lv'], $params['uid']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 会员
     * @return Json
     */
    public function info(): Json
    {
        $TeamService  = new TeamService();
        $result       = $TeamService->info();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


}