// 页面路径：store/index.js
import * as userApi from '/api/user.js'
import {
	removeToken,
	removeUserId,
	removeUserInfo,
	setUserInfo
} from '../utils/request/auth.js'
import {
	createStore
} from 'vuex'
import env from '../utils/request/env.js';

const store = createStore({
	state: { //存放状态
		userInfo: {}, //用户信息
		minioUrl: '',
		configList: [],
		moneyDetail: {},
		bankList: [],
		itemNotFinishedSum: 0,
		incomeTotalSum: 0,
		todayTeamTotalSum: 0,
		todayIncomeSum: 0,
		addressInfo: null,
		teamInfo: {},
		prizes: [],
		lottoryDetailsInfo: null,
		lottoyDetailsType: null,
		inviteCode: '',
		customUrl: '',
	},
	getters: {
		getConfig: (state) => {
			//返回一个函数用于接收
			return (key) => {
				let one = state.configList.find(item => item.configKey == key);
				if (one) {
					return one.configValue;
				}
			}
		}
	},
	mutations: {
		setUserInfo(state, userInfo) {
			// 变更状态
			state.userInfo = userInfo;
		},
		setConfigList(state, configList) {
			state.configList = configList;
		},
		setMinioUrl(state, minioUrl) {
			state.minioUrl = minioUrl;
		},
		setUserMoneyDetail(state, userMoneyDetail) {
			state.moneyDetail = userMoneyDetail;
		},
		setBankList(state, list) {
			state.bankList = list;
		},
		setItemNotFinishedSum(state, itemNotFinishedSum) {
			state.itemNotFinishedSum = itemNotFinishedSum;
		},
		setIncomeTotalSum(state, incomeTotalSum) {
			state.incomeTotalSum = incomeTotalSum;
		},
		setTodayTeamTotalSum(state, todayTeamTotalSum) {
			state.todayTeamTotalSum = todayTeamTotalSum;
		},
		setTodayIncomeSum(state, todayIncomeSum) {
			state.todayIncomeSum = todayIncomeSum;
		},
		setAddressInfo(state, addressInfo) {
			state.addressInfo = addressInfo;
		},
		setTeamInfo(state, teamInfo) {
			state.teamInfo = teamInfo;
		},
		setPrizes(state, prizes) {
			state.prizes = prizes;
		},
		setLottoryDetailsInfo(state, lottoryDetailsInfo) {
			state.lottoryDetailsInfo = lottoryDetailsInfo;
		},
		setLottoyDetailsType(state, lottoyDetailsType) {
			state.lottoyDetailsType = lottoyDetailsType;
		},
		setInviteCode(state, inviteCode) {
			state.inviteCode = inviteCode
		},
		setCustomUrl(state, customUrl) {
			state.customUrl = customUrl
		}
	},
	actions: {
		getCustomUrl({
			commit
		}) {
			userApi.getSetting({
				key: 'kefu'
			}).then(res => {
				if (res.code == 0 && !(res.data instanceof Array)) {
					commit('setCustomUrl', res.data.val);
				}
			})

		},
		getLottoyDetailsType({
			commit
		}) {
			userApi.getArticleInfo({
				code: 'lottory_details_info'
			}).then(res => {
				if (res.code == 0 && !(res.data instanceof Array)) {
					commit('setLottoryDetailsInfo', res.data);
				}
			})

		},
		getLottoryDetailsInfo({
			commit
		}) {
			userApi.getSetting({
				key: 'lottoy_details_type'
			}).then(res => {
				if (res.code == 0 && !(res.data instanceof Array)) {
					commit('setLottoyDetailsType', res.data);
				}
			})
		},
		getPrizes({
			commit
		}) {
			userApi.getPrizes().then(res => {
				if (res.code == 0) {
					commit('setPrizes', res.data);
				}
			})
		},
		getTeamInfo({
			commit
		}) {
			userApi.getTeamInfo().then(res => {
				if (res.code == 0 && !(res.data instanceof Array)) {
					commit('setTeamInfo', res.data);
				}
			})
		},
		getAddressInfo({
			commit
		}) {
			userApi.getAddressInfo().then(res => {
				if (res.code == 0 && !(res.data instanceof Array)) {
					commit('setAddressInfo', res.data);
				}
			})
		},
		getTodayTeamTotalSum({
			commit
		}) {
			userApi.getTodayTeamTotalSum().then(res => {
				if (res.code == 0) {
					commit('setTodayTeamTotalSum', res.data.amount);
				}
			})
		},
		getTodayIncomeSum({
			commit
		}) {
			userApi.getTodayIncomeSum().then(res => {
				if (res.code == 0) {
					commit('setTodayIncomeSum', res.data.amount);
				}
			})
		},
		getItemNotFinishedSum({
			commit
		}) {
			userApi.getItemNotFinishedSum().then(res => {
				if (res.code == 0) {
					commit('setItemNotFinishedSum', res.data.amount);
				}
			})
		},
		getIncomeTotalSum({
			commit
		}) {
			userApi.getIncomeTotalSum().then(res => {
				if (res.code == 0) {
					commit('setIncomeTotalSum', res.data.amount);
				}
			})
		},
		getBankList({
			commit
		}) {
			userApi.getBankList().then(res => {
				if (res.code == 0) {
					commit('setBankList', res.data);
				}
			})
		},
		getUserInfo({
			commit
		}) {
			userApi.getUserInfo().then(res => {
				if (res.code == 0) {
					commit('setUserInfo', res.data);
					setUserInfo(res.data);
				}
			})
		},
		getUserMoneyDetail({
			commit
		}) {
			userApi.getUserMoneyDetail().then(res => {
				if (res.code == 0) {
					commit('setUserMoneyDetail', res.data);
				}
			})
		},
		logout({
			commit
		}) {
			commit('setUserInfo', {});
			removeToken();
			removeUserId();
			removeUserInfo();
			uni.reLaunch({
				url: '/pages/login/login'
			})
		},
		getConfigList({
			commit
		}) {
			userApi.getConfigList().then(res => {
				if (res.code == 200) {
					commit('setConfigList', res.data);
					let one = res.data.find(item => item.configKey == "minio.url");
					if (one) {
						commit('setMinioUrl', one.configValue);
					}
					// setConfigList(res.data);
				}
			})
		},
		afterLogin() {
			this.dispatch('getTodayTeamTotalSum')
			this.dispatch('getTodayIncomeSum')
			this.dispatch('getItemNotFinishedSum')
			this.dispatch('getIncomeTotalSum')
			this.dispatch('getBankList')
			this.dispatch('getUserInfo')
			this.dispatch('getUserMoneyDetail')
			this.dispatch('getAddressInfo')
			this.dispatch('getTeamInfo')
			this.dispatch('getPrizes')
			this.dispatch('getLottoyDetailsType')
			this.dispatch('getLottoryDetailsInfo')
			this.dispatch('getCustomUrl')
		}
	}
})

export default store