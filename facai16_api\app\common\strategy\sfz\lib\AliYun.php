<?php

namespace app\common\strategy\sfz\lib;

use app\common\strategy\sfz\SfzInterface;
use app\common\utils\Result;
use think\facade\Config;

/**
 * 阿里云
 */
class <PERSON><PERSON><PERSON> implements SfzInterface
{
    protected $config;

    public function __construct()
    {
        $this->config    = Config::get('serve_sfz.strategy.AliYun');
    }

    /**
     * 国际腾讯云实名验证  https://cloud.tencent.com/document/product/1007/33188
     * @param array $data
     * @return array
     */
    public function send(array $data): array
    {
        $host       = $this->config['url'];
        $method     = "GET";
        $appcode    = $this->config['appcode'];
        $headers    = [];


        array_push($headers, "Authorization:APPCODE " . $appcode);

        $BankCard = $data['bankcard'] ?? '';
        $IdCard   = $data['idcard'] ?? '';
        $Name     = $data['name'] ?? '';
        $Mobile   = $data['mobile'] ?? '';
        //&mobile={$Mobile}
        $querys   = "bankcard={$BankCard}&idcard={$IdCard}&name=".urlencode($Name);

        $url      = $host . "?" . $querys;

        $curl     = curl_init();

        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);

        if (1 == strpos("$".$host, "https://"))
        {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }

        $output = curl_exec($curl);

        curl_close($curl);

        $json   = json_decode($output, true);

        $params = $json['data'];

        if (empty($params))
        {
            return Result::fail('认证失败');
        }

        if ($params['result'])
        {
            return Result::fail($params['desc']);
        }

        return Result::success();
    }


}
