<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">


					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								我的团队
							</view>
						</template>
					</uv-navbar>

				</view>

				<view class="flex1 f-pr color-#fff">
					<scroll-view scroll-y="true" class="scroll-view" @scrolltolower="scrollHandle"
						:refresher-enabled="true" :refresher-triggered="refresherTriggered"
						@refresherrefresh='onRefresh' refresher-background='#ffffff00'>

						<view class="mx-32 mb-16">

							<view class="fc">
								<view class="f-pr mr-30">
									<view class="size-132  r-69">
										<image src="/static/my/tx.png" mode="aspectFill" class="size-132 r-100 block">
										</image>
									</view>

								</view>
								<view class="flex1">
									<view class="flex items-end mb-13">
										<view class="t-40 lh-56 color-#fff fw-600 mr-16">
											{{userInfo.phone2}}
										</view>

									</view>
									<view class="fc">

										<view
											class="f-pr pr-16 py-6 pl-36 t-20 color-#FFDEA7 lh-33 border-1 border-solid border-color-#B6A58D r-90">
											{{userInfo.level_name}}
											<image src="/static/my/xz1.png" mode="aspectFit"
												class="block f-pa left-[-20rpx] top-[-8rpx] w-54 h-60"></image>
										</view>
									</view>
								</view>
								<view>
									<view class="t-22 lh-30 mb-9">
										直推人数
									</view>
									<view class="t-32 lh-37">
										{{teamInfo.v1}}
									</view>
								</view>
								<view class="ml-24">
									<view class="t-22 lh-30 mb-9">
										团队总人数
									</view>
									<view class="t-32 lh-37">
										{{teamInfo.home}}
									</view>
								</view>
							</view>

						</view>

						<view class="f-pr">
							<image src="/static/team/kbg.png" mode="aspectFit" class="block w-750 h-351"></image>
							<view class="f-pa left-32 right-32 top-32">
								<view class="flex pt-40 pl-40">
									<view class="flex1">
										<view class="t-22 lh-30 mb-15">
											<text>
												投资人员
											</text>
										</view>
										<view class="DIN t-32 lh-37">
											{{teamInfo.works}}
										</view>
										<view class="h-30"></view>
										<view class="t-22 lh-41 mb-15">
											<text>
												绩效汇总
											</text>
										</view>
										<view class="DIN t-32 lh-37">
											{{moneyDetail.team_invite}}
										</view>
									</view>

									<view class="flex1">
										<view class="t-22 lh-30 mb-15">
											<text>
												待投资人员
											</text>
										</view>
										<view class="DIN t-32 lh-37">
											{{teamInfo.noWorks}}
										</view>
										<view class="h-30"></view>
										<view class="t-22 lh-41 mb-15 fc">
											<text>
												直聘收益
											</text>
											<!-- <view class="f-pr ml-12">
												<image src="/static/team/labelxs.png" mode="aspectFit"
													class="block w-77 h-41"></image>
												<view class="f-pa inset fcc t-22 lh-30 color-#8B5D13">
													明细
												</view>
											</view> -->
										</view>
										<view class="DIN t-32 lh-37">
											{{moneyDetail.team_invite_v1}}
										</view>
									</view>

									<view class="flex1">
										<view class="t-22 lh-30 mb-15">
											<text>
												今日新增
											</text>
										</view>
										<view class="DIN t-32 lh-37">
											{{teamInfo.today}}
										</view>
										<view class="h-30"></view>
										<view class="t-22 lh-41 mb-15">
											<text>
												间聘收益
											</text>
										</view>
										<view class="DIN t-32 lh-37">
											{{moneyDetail.team_invite_v2}}
										</view>
									</view>

								</view>
							</view>
						</view>
						<TeamItem v-for="item in list" :key="item.id" :item='item' :isV='true'
							@click="toTeamChild(item)" />
						<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
						<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
							@loadmore="scrollHandle" />
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>



				</view>


			</view>
		</view>



	</view>
</template>

<script>
	import * as UserApi from '@/api/user.js'
	import TeamItem from './components/team-item.vue'
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		components: {
			TeamItem
		},
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		computed: {
			...mapState(['userInfo', 'moneyDetail', 'incomeTotalSum', 'teamInfo', ]),
		},
		onShow() {
			this.onRefresh()
		},
		methods: {
			toTeamChild() {
				uni.navigateTo({
					url: '/pages/team/child'
				})
			},
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getTeamList()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getTeamList(1)
			},
			async getTeamList(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await UserApi.getTeamList({
						lv: 1,
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>