<?php

namespace app\admin\service;

use app\common\repository\QuestionRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

class QuestionService
{

    public function getQuestionLists(): array
    {
        $QuestionRepo = new QuestionRepository();
        $data         = $QuestionRepo->paginates();

        return Result::success($data);
    }


    public function addQuestion($params): array
    {
        $QuestionRepo = new QuestionRepository();
        $res          = $QuestionRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    public function updateQuestion($params): array
    {
        $params['update_time'] = Request::time();

        $QuestionRepo = new QuestionRepository();
        $res          = $QuestionRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    public function deleteQuestion($id): array
    {
        $QuestionRepo = new QuestionRepository();

        $res         = $QuestionRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}