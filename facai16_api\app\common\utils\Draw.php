<?php
namespace app\common\utils;


class Draw
{
    /**
     * 生成随机小数
     * @param int $min
     * @param int $max
     * @return string
     */
    public static function randomFloat(int $min = 0, int $max = 1): string
    {
        $num = $min + mt_rand() / mt_getrandmax() * ($max - $min);
        //控制小数后几位
        return sprintf("%.1f", $num);
    }

    /**
     * 抽奖
     * @param $proArr
     * @return int|string
     */
    public static function rand($proArr)
    {
        $result = '';

        //概率数组的总概率精度
        $proSum = array_sum($proArr);

        //概率数组循环
        foreach ($proArr as $key => $proCur)
        {
            $randNum = self::randomFloat(0, $proSum);

            if ($randNum <= $proCur)
            {
                $result = $key;
                break;
            } else
            {
                $proSum -= $proCur;
            }
        }
        unset ($proArr);
        return $result;
    }
}