<?php
namespace app\admin\controller;

use app\admin\service\UserService;
use app\admin\service\YueBaoService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 余额宝
 */
class YueBao
{

    /**
     * 余额宝记录
     * @return Json
     */
    public function yueBaoLog(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
        ]);

        $UserService    = new YueBaoService();
        $data           = $UserService->yueBaoLog($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}