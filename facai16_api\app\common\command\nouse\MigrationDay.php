<?php
declare (strict_types = 1);

namespace app\common\command\nouse;


use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Config;
use think\facade\Db;
use think\facade\Log;

/**
 * 大表数据迁移
 * Class Migration
 * @package app\common\command
 */
class MigrationDay extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('MigrationDay')->setDescription('the MigrationDay command');
    }


    /**
     * 注单表等大表数据迁移
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        // 获取需清空的表
        $cleanTableArr = Config::load('more/migration')['migration'];

        // 没有清空的表则停止运行
        if(empty($cleanTableArr))
        {
            Log::channel('command')->info('no_table_to_clean_up');
            return;
        }

        $backupDb  = Db::connect('migration');

        $currentDb = Db::connect('mysql');

        $dateBase  = config('database.connections.mysql.database');

        foreach ($cleanTableArr as $val)
        {
            $table    = $val['table'];
            $number   = $val['number'];
            $timeType = $val['time_type'];
            $field    = $val['field'];
            $type     = $val['type'];

            if (!in_array($type,['int','date']))
            {
                continue;
            }

            // 数据大于三个月则开始创建归档数据表
            $strToTime       = strtotime("-{$number} {$timeType} -1 day");

            echo '开始时间是：' . date('Y-m-01 00:00:00', $strToTime). PHP_EOL;

            echo '程序时间是：' . date('Y-m-d H:i:s', $strToTime). PHP_EOL;

            Log::channel('command')->info('开始时间是：' . date('Y-m-01 00:00:00', $strToTime));

            Log::channel('command')->info('程序时间是：' . date('Y-m-d H:i:s', $strToTime));

            if ($type == 'int')
            {
                $lastTime        = strtotime(date('Y-m-d 00:00:00', $strToTime + 86400));
                $firstDayOfMonth = strtotime(date('Y-m-01 00:00:00', $strToTime));
                echo '结束时间是：' . date('Y-m-d H:i:s', $lastTime) . PHP_EOL;
                Log::channel('command')->info('结束时间是：' . date('Y-m-d H:i:s', $lastTime) . PHP_EOL);

            }
            else
            {
                $lastTime        = date('Y-m-d 00:00:00', $strToTime + 86400);
                $firstDayOfMonth = date('Y-m-01 00:00:00', $strToTime);
                echo '结束时间是：' . $lastTime . PHP_EOL;
                Log::channel('command')->info('结束时间是：' . $lastTime . PHP_EOL);
            }


            $month          = date('Ym', $strToTime);

            // 检查存档表是否存在
            $archiveTable   = $table . '_' . $month;
            $sql            = "show tables like '" . $archiveTable . "'";

            $exists         = $backupDb->query($sql);

            // 没有则创建
            if(!$exists)
            {
                // 先在备库创建存档表
                $sql         = "show create table {$table}";
                $tableSqlArr = $currentDb->query($sql);
                $tableSql    = $tableSqlArr[0]['Create Table'];
                $tableSql    = str_replace('ENGINE=InnoDB', 'ENGINE=MyISAM', $tableSql);
                $sql         = preg_replace('/AUTO_INCREMENT=(\d+)/i', '', $tableSql);
                $sql         = str_replace("CREATE TABLE `" . $table . "`", "CREATE TABLE IF NOT EXISTS `" . $archiveTable . "`", $sql);

                unset($tableSql);

                try
                {
                    $backupDb->execute($sql);
                }
                catch (\Exception $e)
                {
                    Log::error("failed_to_create_backup_table: table: {$archiveTable}, sql: {$sql}, error: {$e->getMessage()}");
                    continue;
                }
            }

            // 时间不能等于0
            if($number == 0)
            {
                continue;
            }


            // 获取满足条件的列表
            $limit = 1000;

            if ($type == 'int')
            {
                $sql   = "SELECT count(*) AS count FROM {$dateBase}.{$table} WHERE {$field} >= {$firstDayOfMonth} AND {$field} < {$lastTime} LIMIT {$limit}";
            }
            else
            {
                $sql   = "SELECT count(*) AS count FROM {$dateBase}.{$table} WHERE {$field} >= '{$firstDayOfMonth}' AND {$field} < '{$lastTime}' LIMIT {$limit}";
            }

            $list  = $currentDb->query($sql);

            // 总数量
            $total = array_pop($list);
            $total = $total['count'] ?? 0;

            // 列表为空则跳过
            if(empty($total))
            {
                Log::info("no_data_to_clean_up");
                continue;
            }

            // 总页数
            $totalPage = ceil($total / $limit);

            // 往存档表写入数据
            for ($i = 1; $i <= $totalPage; $i++)
            {
                // 偏移量
                $offset = ($i - 1) * $limit;

                if ($type == 'int')
                {
                    $sql    = "INSERT ignore INTO {$archiveTable} (SELECT * FROM {$dateBase}.{$table} WHERE {$field} >= {$firstDayOfMonth} AND {$field} < {$lastTime}  LIMIT {$offset}, {$limit})";
                }
                else
                {
                    $sql    = "INSERT ignore INTO {$archiveTable} (SELECT * FROM {$dateBase}.{$table} WHERE {$field} >= '{$firstDayOfMonth}' AND {$field} < '{$lastTime}'  LIMIT {$offset}, {$limit})";
                }
              
                $res    = $backupDb->execute($sql);

                // 添加失败
                if(empty($res))
                {
                    Log::error("source_database_table_data_deletion_failed: {$archiveTable}, sql: {$sql}");
                }

            }

            // 来源表清除数据
            if ($type == 'int')
            {
                $sql = "DELETE FROM {$dateBase}.{$table} WHERE {$field} >= {$firstDayOfMonth} AND {$field} < {$lastTime} ";
            }
            else
            {
                $sql = "DELETE FROM {$dateBase}.{$table} WHERE {$field} >= '{$firstDayOfMonth}' AND {$field} < '{$lastTime}' ";
            }


            $res = $currentDb->execute($sql);

            // 删除失败
            if(empty($res))
            {
                Log::error("source_database_table_data_deletion_failed: {$table}, sql:{$sql}");
            }

        }

    }



}
