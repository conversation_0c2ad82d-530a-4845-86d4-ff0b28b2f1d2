#!/bin/bash

# 设置 crontab.txt 的路径
CRONTAB_FILE="/path/to/your/crontab.txt"
LAST_MODIFIED_FILE="/path/to/your/last_modified_time.txt"

# 获取当前 crontab.txt 的修改时间
CURRENT_MODIFIED_TIME=$(stat -c %Y "$CRONTAB_FILE")

# 如果 last_modified_time.txt 不存在，初始化它
if [ ! -f "$LAST_MODIFIED_FILE" ]; then
    echo "$CURRENT_MODIFIED_TIME" > "$LAST_MODIFIED_FILE"
fi

# 读取上次修改的时间
LAST_MODIFIED_TIME=$(cat "$LAST_MODIFIED_FILE")

# 如果文件被修改过
if [ "$CURRENT_MODIFIED_TIME" -ne "$LAST_MODIFIED_TIME" ]; then
    # 更新 crontab
    crontab "$CRONTAB_FILE"
    echo "Crontab updated from crontab.txt."

    # 更新 last_modified_time.txt
    echo "$CURRENT_MODIFIED_TIME" > "$LAST_MODIFIED_FILE"
else
    echo "No changes in crontab.txt."
fi

#chmod +x update_crontab.sh
#* * * * * /path/to/your/update_crontab.sh