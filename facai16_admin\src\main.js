import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import adminTable from "./components/adminTable.vue";
import axios from "@/utils/axios";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import "@wangeditor/editor/dist/css/style.css"; // 引入 css

import permission from "./directive/permission";

const app = createApp(App);

// app.directive('v-permission', permission)
app.directive("permission", (el) => {
  const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
  if (currentUser.role == 1) {
    el.style.display = "none";
  } else {
    el.style.display = "inline-block";
  }
});

if (process.env.NODE_ENV == "mock") {
  //导入mock
  require("./mock/userManage");
}

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.component("Editor", Editor);
app.component("Toolbar", Toolbar);

app.config.globalProperties.BASE_API_URL = process.env.VUE_APP_BASE_API_URL;



console.log(process.env)
console.log(import.meta.env)
app.config.globalProperties.$http = axios;
app.config.globalProperties.IMG_BASE_URL = process.env.VUE_APP_IMG_BASE_URL;

app.component("adminTable", adminTable);
app.use(ElementPlus);
app.use(store);
app.use(router);
app.mount("#app");
