<template>
	<view>

		<view class="page-sign">
			<view class="h-370">
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">

						</view>
					</template>
					<template #right>
						<view class="color-#407CEE w-132 h-46 bg-#fff t-26 fcc rounded-l-30px mr-[-13px]"
							@click="openUrl('/pages/sign/rule')">
							签到规则
						</view>
					</template>
				</uv-navbar>
			</view>
			<view class=" f-pr">
				<!-- 	<view class="rili-box min-h-816 mx-32">
					<uni-calendar :selected="selected" v-if="ifShow"></uni-calendar>
				</view> -->

				<div class="h-24"></div>

				<div class="sign--box-wrap">

					<view class="sign--box fcc-h">
						<view class="fcc t color-#fff pt-32 mb-30">
							<!-- 签到已获得 <text class="color-#F2B441 t-38 fw-600 px-5">{{moneyDetail.signin_money}}</text>元， -->
							已签到<text class="color-#F2B441 t-38 fw-600 px-5">{{moneyDetail.signin_num}}</text>天
						</view>

						<view class="btn-sign fcc " v-if="!signOk" @click="toSign">
							立即签到
						</view>
						<view class="btn-sign fcc disabled" v-else>
							今日已签到
						</view>

					</view>
				</div>
				<view class="h-18"></view>
				<div class="h-40"></div>
				<view class="btn-area"></view>
			</view>
		</view>

		<uv-overlay :show="show" @click="show = false">
			<view class="scroll-view inset fcc-h">
				<view class="pop-sign c2 f-pr" @click.stop="">
					<image src="/static/sign/pop.png" mode="aspectFit" w-654 h-557></image>
					<view class="f-pa left-0 right-0 top-390 fcc-h">
						<view class="fc ">
							<text class="t-33 lh-37">
								签到成功
							</text>

							<!-- <text class="fw-700 t-40 lh-48 DIN px-10 color-#FF5C35">
								2
							</text>
							<text class="t-33 lh-37">
								元
							</text> -->
						</view>
					</view>
				</view>
				<view class="mt-56" @click="show = false">
					<image src="/static/pop-x2.png" mode="aspectFit" size-56></image>
				</view>
			</view>
		</uv-overlay>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import {
		mapState
	} from 'vuex'
	import * as UserApi from '@/api/user.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				show: false,
				ifShow: true,
				//签到数据。 info 可以自定义签到值
				selected: [],
				signInfo: null
			};
		},
		computed: {
			...mapState(['moneyDetail', ]),
			signOk() {
				var a = false
				var nowdate = uni.$uv.timeFrom(new Date(), 'yyyy-mm-dd');
				return this.selected.some(item => item.date === nowdate);
			}
		},
		methods: {
			toSign() {
				UserApi.signin().then(res => {
					if (res.code == 0) {
						this.signInfo = res.data
						this.show = true
						this.$store.dispatch('getUserMoneyDetail')
					}
				})
			},
			getSelected() {
				UserApi.signRecord(res => {
					if (res.code == 0) {
						this.selected = res.data
					}
				})
			}
		},
		onShow() {
			this.getSelected()
		}
	}
</script>

<style lang="scss" scoped>
	.page-sign {
		background: url(/static/sign/bg.jpg) no-repeat #00001E;
		background-size: 100% auto;
		min-height: 100vh;
	}

	.page-sign-inner {
		width: 605rpx;
		height: 537rpx;
		// background: url(/static/sign/bg-au.png) no-repeat;
		background-size: contain;
		position: absolute;
		right: 0rpx;
		top: 0rpx;
	}


	// .page-{
	// 	min-height: 100vh;
	// 	background: url(/static/qiandao/qdbg1.png) 0 0 no-repeat #1DA979;
	// 	background-size: 100% auto;
	// 	&.signOk{
	// 		background-image: url(/static/qiandao/qdbg2.jpg);
	// 	}
	// }
	.btn-sign-rule {
		width: 132rpx;
		height: 45rpx;
		background: #FFFFFF;
		border-radius: 24rpx 0rpx 0rpx 24rpx;
	}

	.sign-img {
		position: absolute;
		right: 0;
		top: 220rpx;

		image {
			display: block;
			width: 261rpx;
			height: 179rpx;
		}
	}

	.rili-box {

		margin-left: 32rpx;
		margin-right: 32rpx;
		border-top: 4rpx solid #407CEE;
		border-radius: 16rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		background: #407CEE;
		padding-bottom: 30rpx;
		min-height: 600rpx;
	}

	.sign--box-wrap {
		margin-left: 32rpx;
		margin-right: 32rpx;
		border-top: 4rpx solid #407CEE;
		border-radius: 16rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		background: #407CEE;
		padding-bottom: 30rpx;
	}

	.sign--box {
		background: #407CEE;

		.btn-sign {
			width: 510rpx;
			height: 98rpx;
			background: linear-gradient(106deg, #FFEBAC 0%, #FFA70F 100%);
			border-radius: 49rpx;
			font-size: 32rpx;
			color: #222;

			&.disabled {
				opacity: 0.5;
			}
		}
	}
</style>