<template>
	<view class="f-pr mb-24 r-16 overflow p-24 bg-#fff" @click="toDetail">
		<view class="flex">
			<view class="mr-24">
				<uv-image :src="item.img" width="6.03125rem" height="6.03125rem" class="size-193 r-16 block">
					<template v-slot:error>
						<view style="font-size: 24rpx;">加载失败</view>
					</template>
				</uv-image>
			</view>
			<view class="flex1 flex flex-col justify-between">
				<view>
					<view class="color-#070E38 t-28 lh40 mb-4">
						{{item.title}}
					</view>
					<view class="t-24 lh-33 color">
						按天分红 到期返本
					</view>
				</view>
				<view class="w-300 flex">
					<view class="t-22 lh-30 c9">
						投资额度(元)
					</view>
					<view class="ml-9 t-32 c6 lh-30 DIN">
						<text class="">{{item.invest}}</text>
					</view>
				</view>
				<view class="w-300 flex">
					<view class="t-22 lh-30 c9">
						投资周期(天)
					</view>
					<view class="ml-9 t-32 c6 lh-30 DIN">
						<text class="">{{item.profit_cycle}}</text>
					</view>
				</view>
				<!-- <view>
					<view class="fc t-22 c9 lh-30">
						<text>
							项目进度
						</text>
						<text class="c2 fw-700 ml-10">
							40%
						</text>
					</view>
					<view class="mt-14 r-20 h-12 f-pr overflow">
						<uv-line-progress :percentage="40" height="12rpx" :showText="false" activeColor="#407CEE"
							inactiveColor="#D9E5FC"></uv-line-progress>
					</view>
				</view> -->
			</view>
		</view>

		<view class="fc-bet mb-20 mt-25">
			<view class="w-180">
				<view class="">
					<view class="t-22 lh-30 c9">
						兑换券
					</view>
					<view class="mt-9 t-32 c6 lh-45 DIN">
						<text class="">{{item.gift_points}}</text>
					</view>
				</view>
			</view>
			<view class="w-1 h-26 bg-#999999 rotate-30 "></view>
			<view class="w-240 fcc">
				<view>
					<view class="t-22 lh-30 c9">
						抽奖券
					</view>
					<view class="mt-9 t-32 c6 lh-45 DIN">
						<text class="">{{item.gift_raffle}}</text>
					</view>
				</view>
			</view>

			<view class="w-1 h-26 bg-#999999 rotate-30 " style="margin-right: auto;"></view>
			<view class=" fcc">
				<view>
					<view class="t-22 lh-30 c9">
						执行卡
					</view>
					<view class="mt-9  t-32 c6 lh-45 DIN">
						<text class="">{{item.gift_coupon}}</text>
					</view>
				</view>
			</view>
		</view>


		<view class="btn-full fcc !r-20">
			立即购买
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item'],
		methods: {
			toDetail() {
				uni.navigateTo({
					url: `/pages/details/details?id=${this.item.id}`
				})
			}
		}
	}
</script>

<style>
</style>