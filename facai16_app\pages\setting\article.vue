<template>
	<view>
		<view class="page-article scroll-view flex-page">
			<view>
				<uv-navbar title="隐私政策" @leftClick="back" fixed placeholder :titleStyle="{color:'#fff'}" bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48></image>
						</view>
					</template>
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="p-32">
						<box-box :blue="true">
							<view class=" p-30 t-30 lh-54 color-#fff r-16">
								收集和分析来自整个生产价值链的大量数据，并利用这些数据实时和预测地优化流程的能力，是智能制造的关键驱动力之一，它为工业企业(从制造商到能源生产商提供了变革潜力。
								在今天的数据世界中，分析和工业物联网对实时过程的重要性意味着，100%的可用性在边缘，是决策和最佳探作的必须。这是需要从一开始就解决的问题，而不是事后才考虑的问题。系统将需要是自主的，能够在没有人类干预的情况下识别和解决问题。此外，所有的“东西都需要相互连接和操作，这将推动对标准的需求。
								对于大多数工业企业来说，通往IIoT的道路将是一个进化之旅。在你开始挖掘下一代大数据驱动的智能自动化的潜力之前，你需要对其建立的基础进行现代化改造。而这意味着要好好地、认真地审视你现有的运营技术。
								现代化您的基础设施将在可靠性和可管理性方面带来巨大的好处，并创建一个坚实的未来就绪的平台，在此基础上建立您组织的IIOT战略。
								
								现代化您的基础设施将在可靠性和可管理性方面带来巨大的好处，并创建一个坚实的未来就绪的平台，在此基础上建立您组织的IIOT战略。
							</view>
						</box-box>
					</view> 
					<view class="btn-area"></view>
				</scroll-view>
			</view>
		</view>
		
		
		
		
		<fixed-kefu></fixed-kefu>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.page-article{
		background: url(/static/article/bg.jpg) 0 0 no-repeat;
		background-size: 100% auto;
		min-height: 100vh;
	}
</style>
