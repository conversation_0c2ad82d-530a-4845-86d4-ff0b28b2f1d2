<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">
					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								银行卡
							</view>
						</template>
					</uv-navbar>
				</view>
				<view class="flex1 f-pr color-#fff">
					<scroll-view scroll-y="true" class="scroll-view ">
						<view class="mx-40 py-20 t-28 lh-48 mb-0" v-for="(item,index) in bankList" :key="index">
							<view class="fc-bet mb-20">
								<view>
									银行卡{{index+1}}
								</view>
								<view class="fc" @click="bankIndex = index">
									<image src="/static/check1-on.png" mode="aspectFill" class="size-40 block"
										v-if="item.default"></image>
									<image src="/static/check1.png" mode="aspectFill" class="size-40 block" v-else>
									</image>
									<text class="ml-10">
										默认
									</text>
								</view>
							</view>
							<uni-swipe-action>
								<uni-swipe-action-item>
									<BankItem :item="item" />
									<template v-slot:right>
										<view @click="click(item.id)" style="height: 100%;" class="fcc px-35 bg-#f00">
											<uv-icon name="trash-fill" size="20" color="#fff"></uv-icon>
										</view>
									</template>
								</uni-swipe-action-item>
							</uni-swipe-action>
						</view>
						<no-data v-if="bankList.length==0"></no-data>
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>
				</view>

				<view class="p-10 px-40 bg-#fff">
					<navigator url="/pages/my/add-bank">
						<view class="btn-full fcc">
							添加银行卡
						</view>
					</navigator>
					<view class="btn-area"></view>
				</view>

			</view>
		</view>



	</view>
</template>

<script>
	import BankItem from './bank-item.vue'
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import * as UserApi from '@/api/user.js'
	export default {
		mixins: [needAuth],
		components: {
			BankItem
		},
		data() {
			return {

			};
		},
		computed: {
			...mapState(['bankList']),
		},
		methods: {
			click(id) {
				uni.showModal({
					title: '删除银行卡',
					content: '确定要删除该银行卡吗？',
					cancelColor: "#999",
					confirmColor: "#222",
					success: res => {
						if (res.confirm) {
							uni.showLoading()
							UserApi.deleteBank({
								id
							}).then(res => {
								if (res.code == 0) {
									uni.showToast({
										title: '删除成功'
									})
									this.$store.dispatch('getBankList')
								}
							}).finally(() => {
								uni.hideLoading()
							})
						}

					},
					fail: res => {
						uni.showToast({
							title: '取消'
						})
					},
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>