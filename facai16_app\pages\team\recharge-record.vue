<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">


					<uv-navbar  bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								当月充值明细
							</view>
						</template>
					</uv-navbar>
					
				</view>
				

				<view class="flex1 f-pr color-#fff" >
					<scroll-view scroll-y="true" class="scroll-view ">
						
					
						
						<view class="m-32 !mt-0 f-pr h-266 overflow r-20" v-for="(item,index) in 10">
							<image src="/static/team/boxbg.png" mode="widthFix" class="block w-686 h-341"></image>
							<view class="f-pa inset px-32 pt-32">
								<view class="fc pl-10 mb-32">
									<view class="mr-25 r-100 border-1 border-solid border-color-#407CEE size-66">
										<image src="/static/my/tx.png" mode="aspectFit" class="size-full block r-100"></image>
									</view>
									<view class="c2 t-32 lh-45 fw-600">
										Summer 13576768909
									</view>
									<view class="f-pr ml-12">
										<image src="/static/team/label.png" mode="aspectFit" class="block w-117 h-41"></image>
										<view class="f-pa inset fcc t-22 lh-30 color-#8B5D13">
											普通会员
										</view>
									</view>
								</view>
								<uv-gap height="1" bgColor="#E0E0E0" marginBottom="31rpx"></uv-gap>
								
								<view class="fc-bet">
									<view>
										<view class="t-28 lh-40 c2 mb-7">
											充值标题
										</view>
										<view class=" t-24 lh-33 color-#9EA59A mb-24">
											充值时间 2024.12.23 14:33:44
										</view>
									</view>
									<view class="color DIN t-40 lh-47">
										+300
									</view>
								</view>
								
								
							</view>
						</view>
						
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>
					
					
					
				</view>
				
				
			</view>
		</view>



	</view>
</template>

<script>
	export default {

		data() {
			return {
				
				
				
			};
		},
		mounted() {

		},

		methods: {
			
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}



</style>