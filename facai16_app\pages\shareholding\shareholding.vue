<template>
	<view>
		<view class="page-bg flex-page scroll-view">
			<view class="px-32 py-20">
				<top-status-bar></top-status-bar>
				<view class="fc-bet ">
					<view class="flex1 h-90 fc">
						<image src="/static/index/logo.png" mode="aspectFit" class="w-100 h-72 block mr-20"></image>
						<text class="fm color-#407CEE t-48 lh-63">
							Mistral AI
						</text>
					</view>
				</view>
			</view>
			<view>
				<view
					class="mx-32 r-100 border-1 border-solid border-#407CEE bg-#000 bg-op-50 p-8 t-28 lh-40 color flex">
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==0}"
						@click="tabIndex = 0">
						普通专区
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==1}"
						@click="tabIndex = 1">
						VIP专区
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==2}"
						@click="tabIndex = 2">
						福利专区
					</view>
				</view>
				<view class="h-16"></view>
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30 overflow-y-hidden">
				<ProjectList type="1" ref="project1" v-if="tabIndex==0" />
				<ProjectList type="2" ref="project2" v-if="tabIndex==1" />
				<ProjectList type="3" ref="project3" v-if="tabIndex==2" />
			</view>
			<view>
				<foot-nav-bar :fixed="true" index="2"></foot-nav-bar>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import ProjectList from "./project-list.vue"
	export default {
		components: {
			ProjectList
		},
		data() {
			return {
				tabIndex: -1
			};
		},
		watch: {
			tabIndex(newVal) {
				if (newVal > -1) {
					this.$nextTick(() => {
						this.$refs[`project${this.tabIndex+1}`].getRecord()
					})
				}
			}
		},
		onReady() {
			this.$nextTick(() => {
				this.tabIndex = 0
			})
		},
		methods: {

		},

	};
</script>
<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #000511;
		background-size: 100% auto;
	}
</style>