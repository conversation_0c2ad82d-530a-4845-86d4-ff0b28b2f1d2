<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="用户名" required >
            <el-input v-model="form.username" clearable  />
        </el-form-item>
        
        <!-- <el-form-item label="类型" required>
            <el-select v-model="form.role_id" placeholder="" clearable>
                <el-option v-for="item in rolesEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="邀请码" required >
            <el-input v-model="form.invite_code" clearable />
        </el-form-item> -->
        <el-form-item label="密码" required >
            <el-input v-model="form.password" clearable />
        </el-form-item>
        <!-- <el-form-item label="联系方式" required >
            <el-input v-model="form.email" clearable />
        </el-form-item>
        <el-form-item label="备注" required >
            <el-input v-model="form.remarks" clearable />
        </el-form-item> -->
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import {rolesEnums} from '@/config/enums'

const form = ref({
    username: '',
    role: '',
    role_id: '',
    invite_code: '',
    password: '',
    email: '',
    remarks: ''
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>