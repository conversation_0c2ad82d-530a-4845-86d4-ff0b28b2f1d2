<template>
	<view>
		<view class="page-login">
			<view class="fcc-h pt-108">
				<image src="/static/login/logo.png" mode="aspectFit" class="w-140 h-100 block"></image>
				<view class="t-48 fm lh-62 color-#fff">
					Mistral AI
				</view>
			</view>
			<view class="h-250"></view>

			<view class="mx-34 r-20 border-1 border-solid border-#fff bg-#fff bg-op-10 px-36">
				<view class="fc-bet h-105">
					<view class="tit">
						忘记密码
					</view>
				</view>


				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-tel.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1  mr-20">
						<input type="number" v-model="phone" placeholder="请输入手机号" maxlength="11"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff DIN">
					</view>
				</view>

				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-safe.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 ">
						<input type="number" v-model="code" placeholder="请输入验证码"
							placeholder-style="color:#fff;font-size:31rpx" maxlength="6"
							class="t-36 w-full lh-116  color-#fff">
					</view>
					<view class="mr-14">
						<uv-button type="primary" :text="duration?`${duration}(s)`:'发送验证码'" :disabled="duration"
							shape="circle" @click="getVerifyCodeHandle"></uv-button>
					</view>
				</view>

				<view class="h-20"></view>

				<view class="btn-full fcc !r-20" @click="nextStep">
					提交
				</view>

				<view class="h-36 text-center p-12 text-white">
					<navigator url="/pages/login/login">返回登录</navigator>
				</view>

			</view>


			<view class="h-50"></view>
			<view class="">
				<view class="fcc">
					<view @click="agree=!agree">
						<image v-if="agree" src="/static/check1-on.png" mode="aspectFit" size-32 block></image>
						<image v-else src="/static/check1.png" mode="aspectFit" size-32 block></image>
					</view>
					<view class="t-22 color-#fff ml-8">
						<text @click="agree=!agree">我已阅读并同意</text>
						<navigator url="/pages/setting/article" class="inline-block">
							<text class="color">
								《服务协议》
							</text>
							<text class="color">《隐私协议》</text>
						</navigator>
					</view>
				</view>
				<view class="h-30"></view>
				<view class="btn-area"></view>
			</view>
		</view>


		<pop-select ref="popSelect" title="切换语言" :list="list" @select="select"></pop-select>
		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		loginGoNext,
		getVerifyCode
	} from '@/api/auth.js'
	export default {
		data() {
			return {
				agree: false,
				index: 0,
				list: ['中文', 'English'],
				phone: '',
				code: '',
				duration: 0,
				timer: null
			};
		},
		methods: {
			select(e) {
				this.index = e
			},
			async nextStep() {
				let err = ''
				if (!this.phone || !(/^\d{11}$/.test(this.phone))) {
					err = '请输入有效手机号'
				} else if (!this.code || !(/^\d{6}$/.test(this.code))) {
					err = '请输入6位数字验证码'
				} else if (!this.agree) {
					err = '请阅读并同意用户协议'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await loginGoNext({
							phone: this.phone,
							code: this.code
						})
						await localStorage.setItem("user_phone", rthis.phone);
						uni.navigateTo({
							url: '/pages/login/change-password',
						})
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			},
			async getVerifyCodeHandle() {
				let err = ''
				if (!this.phone || !(/^\d{11}$/.test(this.phone))) {
					err = '请输入有效手机号'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				}
				try {
					uni.showLoading({
						title: '发送中',
					});
					await getVerifyCode({
						phone: this.phone
					})
					this.duration = 60
					timer = setInterval(() => {
						if (!this.duration) {
							clearInterval(this.timer)
							timer = null
							return
						}
						this.duration--
					}, 1000)
				} catch (e) {} finally {
					uni.hideLoading()
				}
			}
		},
		onHide() {
			if (this.timer) {
				clearInterval(this.timer)
				this.duration = 0
				this.timer = null
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-login {
		background: url(/static/article/bg.jpg) no-repeat #000232;
		background-size: 100% auto;
	}

	.tit {
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 40rpx;
		color: #FFFFFF;
		line-height: 56rpx;
		text-shadow: 2rpx 2rpx 2rpx rgba(0, 0, 0, 0.8);
		text-align: right;
		font-style: normal;
	}

	.input-row {

		backdrop-filter: blur(29rpx);
	}
</style>