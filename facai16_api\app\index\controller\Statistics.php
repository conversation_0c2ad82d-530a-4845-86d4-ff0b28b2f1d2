<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\StatisticsService;
use think\response\Json;

/**
 * 统计
 */
class Statistics
{

    /**
     * 在签约中
     * @return Json
     */
    public function itemNotFinishedSum():<PERSON>son
    {

        $StatisticsService  = new StatisticsService();
        $data               = $StatisticsService->itemNotFinishedSum();
        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 今天收益
     * @return Json
     */
    public function todayIncomeSum():Json
    {
        $StatisticsService  = new StatisticsService();
        $data               = $StatisticsService->todayIncomeSum();
        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 今天收益
     * @return Json
     */
    public function incomeTotalSum():Json
    {
        $StatisticsService  = new StatisticsService();
        $data               = $StatisticsService->incomeTotalSum();
        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 今天收益
     * @return Json
     */
    public function todayTeamTotalSum():Json
    {
        $StatisticsService  = new StatisticsService();
        $data               = $StatisticsService->todayTeamTotalSum();
        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

}