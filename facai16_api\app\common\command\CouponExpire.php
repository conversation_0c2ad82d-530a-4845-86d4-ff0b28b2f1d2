<?php

namespace app\common\command;

use app\common\repository\CouponRepository;
use think\console\Command;
use think\console\Input;
use think\console\Output;


/**
 * 优惠券已经过期
 * Class CouponExpire
 * @package app\common\command
 */
class CouponExpire extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('Test')->setDescription('the Test command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {

        $CouponRepo = new CouponRepository();
        $where      = [];
        $where[]    = ["status",'=',0];
        $where[]    = ["create_time",'<',time()];

        $update = [
            'status'      => 0,
            'update_time' => time(),
        ];

        $CouponRepo->updateByCondition($where, $update);
    }

}

