<?php
namespace app\admin\controller;

use app\admin\service\UserAddressService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 用户地址
 */
class UserAddress
{

    /**
     * 用户地址
     * @return mixed
     */
    public function getUserAddressLists(): J<PERSON>
    {

        $params         = Request::only([
            'starttime',
            'endtime',
            'phone',
        ]);


        $UserAddressService     = new UserAddressService();
        $data                   = $UserAddressService->getUserAddressLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 用户地址信息
     * @return Json
     */
    public function getUserAddressInfo(): Json
    {
        $id                 = Request::param('id',0);
        $UserAddressService = new UserAddressService();
        $data               = $UserAddressService->getUserAddressInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加地址
     * @return Json
     */
    public function addUserAddress(): <PERSON><PERSON>
    {
        $param              = Request::only([
            'phone'         => '',
            'address_name'  => '',
            'address_phone' => '',
            'address_city'  => '',
            'address_place' => '',
            'default'       => ''
        ]);

        $UserAddressService = new UserAddressService();
        $data               = $UserAddressService->addUserAddress($param['phone'],$param['address_name'],$param['address_phone'],$param['address_city'], $param['address_place'], $param['default']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新用户地址
     * @return Json
     */
    public function updateUserAddress(): Json
    {
        $param              = Request::only([
            'id'            => '',
            'phone'         => '',
            'address_name'  => '',
            'address_phone' => '',
            'address_city'  => '',
            'address_place' => '',
            'default'       => ''
        ]);

        $UserAddressService = new UserAddressService();
        $data               = $UserAddressService->updateUserAddress($param['id'], $param['phone'], $param['address_name'], $param['address_phone'],$param['address_city'], $param['address_place'], $param['default']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除记录
     * @return Json
     */
    public function deleteUserAddress(): Json
    {
        $id                 = Request::param('id',0);
        $UserAddressService = new UserAddressService();
        $data               = $UserAddressService->deleteUserAddress($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}