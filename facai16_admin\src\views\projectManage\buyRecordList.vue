<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="用户名"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-input v-model="searchForm.phone" placeholder="手机号" clearable />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.order_no"
          placeholder="订单号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.item_name"
          placeholder="项目名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.item_status"
          placeholder="处理状态"
          clearable
        >
          <el-option
            v-for="item in projectItemStatusEnums"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_test"
          placeholder="账号类型"
          clearable
        >
          <el-option
            v-for="item in accountTypeEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="购买时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="购买时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- <template v-slot:query-button-right>
      <el-button type="primary" icon="CirclePlus" @click="editItem('add', {})"
        >添加</el-button
      >
    </template> -->
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户" width="150" />
      <el-table-column prop="phone" label="电话号码" width="160" />
      <el-table-column prop="is_test" label="账号类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.is_test, accountTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="item_name" label="项目名称" width="200" />
      <el-table-column prop="order_no" label="订单号" width="200" />
      <el-table-column prop="amount" label="购买金额" width="200" />
      <el-table-column prop="cycle_start" label="已开始周期(天)" width="200" />
      <el-table-column prop="cycle_end" label="全部周期(天)" width="200" />
      <el-table-column prop="item_status" label="状态" width="200">
        <template #default="scope">
          {{ getLabelByVal(scope.row.item_status, projectItemStatusEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="is_coupon" label="是否优惠券购买" width="180">
        <template #default="scope">
          <span v-if="scope.row.is_coupon == 0">正常订单</span>
          <span v-if="scope.row.is_coupon == 1">优惠券购买</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="购买时间" width="130" />
      <el-table-column prop="end_time" label="结束时间" width="130" />
      <!-- <el-table-column label="操作" width="150" >
                <template #default="scope">
                    <el-button type="primary" icon="Edit" @click="editItem('edit', scope.row)" >编辑</el-button>
                    <el-button type="danger" icon="Delete" @click="deleteAction(scope.row)" >删除</el-button>
                </template>
            </el-table-column> -->
    </template>
    <!-- <el-dialog v-model="dialogFlag" title="新增/编辑" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog> -->
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  booleanEnums,
  getLabelByVal,
  projectItemStatusEnums,
  sendStatusEnums,
  accountTypeEnums,
} from "@/config/enums";
// import EditPop from "./components/buyRecordList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from 'vue-router'

const route = useRoute()

const searchForm = ref({
  username: "",
  phone: "",
  order_no: "",
  is_test: "",
  item_name: "",
  item_status: "",
  starttime: "",
  endtime: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);
const formType = ref("add");

onMounted(() => {
   if (route.query && route.query.phone) {
    searchForm.value.phone = route.query.phone
  }
  getList();
});

const editItem = (type, row = {}) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = () => {
  
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ElMessage({
        type: "success",
        message: "删除成功",
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/ItemOrder/getItemOrderLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
