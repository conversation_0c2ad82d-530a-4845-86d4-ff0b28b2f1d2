<?php

namespace app\admin\controller;

use app\admin\service\SystemSetService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 系统设计列表
 */
class SystemSet
{

    /**
     * 系统设置
     * @return Json
     */
    public function getSystemSetLists(): Json
    {
        $params = Request::only([
            'limit' => 100,
            'desc'  => ''
        ]);

        $SystemSetService   = new SystemSetService();
        $data               = $SystemSetService->getSystemSetLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新系统设置
     * @return Json
     */
    public function updateSystemSet(): Json
    {
        $params = Request::only([
            'key'   => '',
            'val'   => '',
            'desc'  => '',
        ]);

        $SystemSetService   = new SystemSetService();
        $data               = $SystemSetService->updateSystemSet($params['key'], $params['val'], $params['desc']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 更新系统设置
     * @return Json
     */
    public function addSystemSet(): Json
    {
        $params = Request::only([
            'key'    => '',
            'val'    => '',
            'desc'   => ''
        ]);

        $SystemSetService   = new SystemSetService();
        $data               = $SystemSetService->addSystemSet($params['key'], $params['val'], $params['desc']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除系统设置
     * @return Json
     */
    public function deleteSystemSet(): Json
    {
        $params = Request::only([
            'id'   => '',
        ]);

        $SystemSetService   = new SystemSetService();
        $data               = $SystemSetService->deleteSystemSet($params['id']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}