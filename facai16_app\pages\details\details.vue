<template>
	<view>
		<view class="bg-#fff">
			<view class="fixed-view">
				<top-status-bar></top-status-bar>
				<view class="fc-bet px-24 py-35">
					<view class="fcc w-88 h-88 r-100 bg-#000 bg-op-70" @click="back">
						<image src="/static/details/back.png" mode="aspectFit" size-48></image>
					</view>
				</view>
			</view>
			<uv-image :src="detail.img" width="100%">
				<template v-slot:error>
					<view style="font-size: 24rpx;">加载失败</view>
				</template>
			</uv-image>
			<view class="px-32 pt-42 pb-32 c2 t-36 lh-50">
				{{detail.title}}
			</view>
			<view class="mx-32 bg-#EBF1FD r-16 color mb-56">
				<view class="h-7 bg-#407CEE r-12"> </view>
				<view class="fc-bet pt-20 pb-27">
					<view class="flex1 fcc-h">
						<view class="color t-22 lh-47">
							<text class="t-40 DIN">{{detail.invest}}</text>元
						</view>
						<view class="t-22 lh-30">
							项目本金
						</view>
					</view>
					<view class="w-1 h-36 bg-#D2D7E1 rotate-30"></view>

					<view class="w-1 h-36 bg-#D2D7E1 rotate-30"></view>
					<view class="flex1 fcc-h">
						<view class="color t-22 lh-47">
							按天分红 到期返本
						</view>
						<view class="t-22 lh-30">
							结算方式
						</view>
					</view>
					<view class="w-1 h-36 bg-#D2D7E1 rotate-30"></view>
					<view class="flex1 fcc-h">
						<view class="color t-22 lh-47">
							<text class="t-40 DIN">{{detail.profit_cycle}}</text>天
						</view>
						<view class="t-22 lh-30">
							项目周期
						</view>
					</view>

				</view>
			</view>
			<view class="fc-bet mb-28 mx-32">
				<image src="/static/sign/tit-arr.png" class="block w-256 h-23 rotate-0"></image>
				<view class="t-28 lh-40 color">
					商品详情
				</view>
				<image src="/static/sign/tit-arr.png" class="block w-256 h-23 rotate-180"></image>
			</view>

			<view class="mx-32 mb-32 border-1 border-#A0BEF7 border-solid r-12 t-28 c2 lh-40 f-pr overflow px-24">
				<view v-html="ruleText"></view>
			</view>

			<view class="fc-bet mb-28 mx-32">
				<image src="/static/sign/tit-arr.png" class="block w-256 h-23 rotate-0"></image>
				<view class="t-28 lh-40 color">
					结算详情
				</view>
				<image src="/static/sign/tit-arr.png" class="block w-256 h-23 rotate-180"></image>
			</view>

			<view class="mx-32 mb-32 border-1 border-#A0BEF7 border-solid r-12 t-28 c2 lh-40 f-pr overflow">
				<view class="flex border-0 border-b border-#A0BEF7 border-solid">
					<view class="w-160 bg-#EBF1FD fc">
						<view class="pl-25 t-28 color lh-40 py-20">
							职位
						</view>
					</view>
					<view class="flex1 fc">
						<view class="px-25 py-19 flex1">
							周期
						</view>
					</view>
					<view class="flex1 bg-#EBF1FD fc">
						<view class="pl-25 t-28 color lh-40 py-20">
							日薪资
						</view>
					</view>
					<view class="flex1 fc">
						<view class="px-25 py-19 flex1">
							结算
						</view>
					</view>
				</view>
				<view class="flex border-0 border-b border-#A0BEF7 border-solid" v-for="(item,index) in levelInfo"
					:key="item.id">
					<view class="w-160 bg-#EBF1FD fc">
						<view class="pl-25 t-28 color lh-40 py-20">
							{{item.title}}
						</view>
					</view>
					<view class="flex1 fc">
						<view class="px-25 py-19 flex1">
							{{detail.profit_cycle}}天
						</view>
					</view>
					<view class="flex1 bg-#EBF1FD fc">
						<view class="pl-25 t-28 color lh-40 py-20">
							{{item.invest_rate*detail.invest/100}}元
						</view>
					</view>
					<view class="flex1 fc">
						<view class="px-25 py-19 flex1">
							{{item.invest_rate*detail.invest*detail.profit_cycle/100}}元
						</view>
					</view>
				</view>
			</view>
			<view class="h-30"></view>

			<fixed-footer-view height="136rpx">
				<view class="bg-#fff color-#fff">
					<view class=" px-30 py-10">

						<view class="btn-full fcc" @click="buyProject">
							立即购买
						</view>

					</view>
					<view class="btn-area"></view>
				</view>
			</fixed-footer-view>
		</view>

		<fixed-kefu></fixed-kefu>

	</view>
</template>

<script>
	import * as JobApi from '@/api/job.js'
	import {
		htmlDecodeByRegExp
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				// 轮播
				current: 0,
				levelInfo: [],
				detail: {}
			};
		},
		computed: {
			ruleText() {
				if (!this.detail.content) {
					return ''
				} else {
					return htmlDecodeByRegExp(this.detail.content).replace(
						"<img",
						'<img  mode="widthFix"'
					)
				}
			},
		},
		mounted() {
			if (this.$route.query && this.$route.query.id) {
				this.getDetail(this.$route.query.id)
				this.getLevelInfo()
			} else {
				uni.navigateBack()
			}
		},
		methods: {
			getLevelInfo() {
				JobApi.getLevelInfo().then((res) => {
					if (res.code == 0) {
						this.levelInfo = res.data
					}
				})
			},
			getDetail(id) {
				JobApi.getProjectInfo({
					id: id
				}).then((res) => {
					if (res.code == 0) {
						this.detail = res.data
					}
				})
			},
			buyProject() {
				uni.showLoading()
				JobApi.buyProject({
					type: 0,
					id: this.detail.id
				}).then((res) => {
					if (res.code == 0) {
						uni.showToast({
							title: '购买成功'
						})
						this.$store.dispatch('getUserInfo')
						this.$store.dispatch('getUserMoneyDetail')
					}
				}).finally(() => {
					uni.hideLoading()
				})
			},
			back() {
				uni.navigateBack()
			},

		},
	}
</script>

<style lang="scss" scoped>
	@mixin flex($direction: row) {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: $direction;
	}

	.indicator {
		@include flex(row);
		justify-content: center;

		&__dot {
			height: 6px;
			width: 6px;
			border-radius: 100px;
			background-color: rgba(255, 255, 255, 0.35);
			margin: 0 5px;
			transition: background-color 0.3s;

			&--active {
				background-color: #ffffff;
			}
		}
	}

	.indicator-num {
		padding: 2px 0;
		background-color: #110C09;
		border-radius: 100px;
		width: 35px;
		@include flex;
		justify-content: center;

		&__text {
			color: #fff;
			font-size: 12px;
		}
	}
</style>