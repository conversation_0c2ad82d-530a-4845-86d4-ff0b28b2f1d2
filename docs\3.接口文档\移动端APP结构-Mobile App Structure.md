# 移动端APP (facai16_app) 详细结构

## 目录结构

```
facai16_app/
├── pages/                 # 页面目录
│   ├── index/            # 首页
│   ├── login/            # 登录注册
│   ├── my/               # 个人中心
│   ├── cash/             # 资金管理
│   ├── invest/           # 投资理财
│   ├── team/             # 团队管理
│   ├── project/          # 项目列表
│   ├── details/          # 项目详情
│   ├── shareholding/     # 股权投资
│   ├── luck-draw/        # 抽奖活动
│   ├── sign/             # 签到
│   ├── invite/           # 邀请推广
│   ├── setting/          # 设置页面
│   ├── help/             # 帮助中心
│   ├── news/             # 新闻资讯
│   ├── notice/           # 公告通知
│   ├── address/          # 地址管理
│   └── pay/              # 支付页面
├── api/                  # API接口
├── components/           # 公共组件
├── store/                # 状态管理
├── utils/                # 工具函数
├── static/               # 静态资源
├── common/               # 公共文件
└── uni_modules/          # uni-app插件
```

## 核心页面功能

### 首页模块 (pages/index/)

#### index.vue - 首页
**功能**:
- 用户信息展示
- 快捷功能入口
- 公告轮播
- 数据统计展示
- 项目推荐

#### custom.vue - 自定义页面
**功能**:
- 个性化内容展示
- 动态配置页面

### 登录注册模块 (pages/login/)

#### login.vue - 登录页面
**功能**:
- 手机号登录
- 密码登录
- 验证码登录
- 记住密码

#### reg.vue - 注册页面
**功能**:
- 手机号注册
- 验证码验证
- 邀请码填写
- 密码设置

#### forgot.vue - 忘记密码
**功能**:
- 密码重置
- 验证码验证
- 新密码设置

#### change-password.vue - 修改密码
**功能**:
- 登录密码修改
- 旧密码验证

#### change-safe-password.vue - 修改支付密码
**功能**:
- 支付密码修改
- 安全验证

### 个人中心模块 (pages/my/)

#### my.vue - 个人中心
**功能**:
- 用户信息展示
- 功能菜单
- 资产概览
- 快捷操作

#### bank.vue - 银行卡管理
**功能**:
- 银行卡列表
- 添加银行卡
- 银行卡验证

#### add-bank.vue - 添加银行卡
**功能**:
- 银行卡信息填写
- 实名验证
- 银行卡绑定

#### usdt.vue - USDT管理
**功能**:
- USDT地址管理
- 地址添加
- 地址验证

### 资金管理模块 (pages/cash/)

#### recharge.vue - 充值页面
**功能**:
- 充值金额输入
- 支付方式选择
- 充值记录查看

#### withdrawal.vue - 提现页面
**功能**:
- 提现金额输入
- 银行卡选择
- 提现申请

#### transfer.vue - 转账页面
**功能**:
- 转账对象选择
- 转账金额输入
- 转账确认

#### record.vue - 资金记录
**功能**:
- 收支明细
- 记录筛选
- 数据统计

#### yuebao.vue - 余额宝
**功能**:
- 余额宝投资
- 收益查看
- 资金转入转出

### 投资理财模块 (pages/invest/)

#### record.vue - 投资记录
**功能**:
- 投资项目列表
- 收益统计
- 投资详情

#### invest-record-list.vue - 投资记录列表
**功能**:
- 记录分页展示
- 状态筛选
- 详情查看

#### invest-record-item.vue - 投资记录详情
**功能**:
- 投资详细信息
- 收益计算
- 操作记录

### 团队管理模块 (pages/team/)

#### team.vue - 团队首页
**功能**:
- 团队数据统计
- 下级用户列表
- 团队收益

#### child.vue - 下级详情
**功能**:
- 下级用户信息
- 投资数据
- 收益贡献

#### recharge-record.vue - 充值记录
**功能**:
- 团队充值统计
- 充值明细
- 数据分析

### 项目模块 (pages/project/)

#### project.vue - 项目列表
**功能**:
- 项目展示
- 项目筛选
- 项目搜索

### 项目详情模块 (pages/details/)

#### details.vue - 项目详情
**功能**:
- 项目信息展示
- 收益计算
- 风险提示

#### buy.vue - 项目购买
**功能**:
- 投资金额输入
- 支付方式选择
- 投资确认

## API接口模块 (api/)

### auth.js - 认证接口
- `login()` - 用户登录
- `register()` - 用户注册
- `getVerifyCode()` - 获取验证码

### user.js - 用户接口
- `getUserInfo()` - 获取用户信息
- `getUserMoneyDetail()` - 获取资金详情
- `realNameAuth()` - 实名认证
- `addBank()` - 添加银行卡

### job.js - 项目接口
- `getProjects()` - 获取项目列表
- `buyProject()` - 购买项目
- `getProjectInfo()` - 获取项目详情
- `getRecord()` - 获取投资记录

### home.js - 首页接口
- `noticeData()` - 获取公告
- `getArticle()` - 获取文章

### product.js - 商品接口
- `getProductPage()` - 获取商品列表
- `buyProduct()` - 购买商品
- `getPointsProducts()` - 获取积分商品

### order.js - 订单接口
- `getMyProductList()` - 获取我的商品
- `getMyProductById()` - 获取商品详情

## 状态管理 (store/index.js)

### State状态
- `userInfo` - 用户信息
- `configList` - 配置列表
- `moneyDetail` - 资金详情
- `bankList` - 银行卡列表
- `teamInfo` - 团队信息

### Actions操作
- `login()` - 登录操作
- `logout()` - 登出操作
- `getUserInfo()` - 获取用户信息
- `getConfig()` - 获取配置信息

## 工具函数 (utils/)

### request/ - 网络请求
- `request.js` - 请求封装
- `auth.js` - 认证工具
- `env.js` - 环境配置

## 应用配置

### manifest.json - 应用配置
- 应用信息
- 权限配置
- 平台特定配置

### pages.json - 页面配置
- 页面路由
- 导航栏配置
- tabBar配置
