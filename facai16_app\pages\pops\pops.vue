<template>
	<view>
		<uv-modal ref="modal" confirm-color="#222" cancelColor="#999" title="标题名称" showCancelButton @confirm="confirm">
			<view class="fcc">
				这里有一段文字描述这里有一段文字描述
			</view>
		</uv-modal>
		<button @click="openModal">基本信息弹出框</button>
		
		
		
		<uv-modal confirm-color="#222" cancelColor="#999"  ref="modal2" title="标题名称" showCancelButton @confirm="confirm">
			<uv-textarea border="none" v-model="textareaValue" count maxlength="100" placeholder="请输入内容"></uv-textarea>
		</uv-modal>
		<button @click="openModal2">编辑昵称</button>

		<no-data></no-data>
		<no-data network></no-data>
		<no-data error></no-data>
		<no-data error>500</no-data>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				textareaValue:""
			};
		},
		methods: {
			openModal() {
				this.$refs.modal.open();
			},
			openModal2() {
				this.$refs.modal2.open();
			},
			confirm() {
				console.log('点击确认按钮');
			}
		},
	}
</script>

<style lang="scss">

</style>