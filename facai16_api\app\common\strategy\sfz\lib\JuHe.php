<?php
namespace app\common\strategy\sfz\lib;
use app\common\strategy\sfz\SfzInterface;
use app\common\utils\Result;
use think\facade\Config;


/**
 * 聚合
 */
class JuHe implements SfzInterface
{
    protected $config;

    public function __construct()
    {
        $this->config    = Config::get('serve_sfz.strategy.JuHe');
    }

    /**
     * 银行卡
     * @param array $data
     * @return array
     */
    public function send(array $data): array
    {

        $params  = [
            'key'       => $this->config['key'],
            'realname'  => $data['name'],
            'idcard'    => $data['idcard'],
        ];

        $data = $this->request($params);

        $data = json_decode($data, true);

        if (isset($data['result']['res']))
        {
            if ($data['result']['res'] != 1)
            {
                return Result::fail($data['result']['message']);
            } else
            {
                return Result::success();
            }
        }
        else
        {
            return Result::fail($data['reason']);
        }

    }


    /**
     * 请求
     * @param array $requestParams
     * @return bool|string
     */
    public function request(array $requestParams)
    {
        // 基本参数配置
        $apiUrl             = $this->config['url'];

        $requestParamsStr   = http_build_query($requestParams);
        // 接口请求header
        $headers            = ["Content-Type: application/x-www-form-urlencoded"];
        // 发起接口网络请求
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($curl, CURLOPT_URL, $apiUrl . '?' . $requestParamsStr);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        if (1 == strpos("$" . $apiUrl, "https://"))
        {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }

        $response = curl_exec($curl);
        $httpInfo = curl_getinfo($curl);

        curl_close($curl);

        return $response;
    }
}




