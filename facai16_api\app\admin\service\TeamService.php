<?php
namespace app\admin\service;

use app\common\repository\TeamGiftRepository;
use app\common\repository\TeamLogRepository;
use app\common\repository\TeamRepository;
use app\common\utils\Result;


/**
 * 团队服务
 */
class TeamService
{
    /**
     * 用户等级列表
     * @param array $params
     * @return array
     */
    public function getTeamLists(array $params): array
    {

        $where = [];

        if (!empty($params['title']))
        {
            $where[] = ['title', '=', $params['title']];
        }

        $TeamRepo  = new TeamRepository();
        $data      = $TeamRepo->paginates($where,'*', $params['limit'], ['id'=> 'desc']);

        return Result::success($data);
    }


    /**
     * 添加
     * @param array $params
     * @return array
     */
    public function addTeam(array $params): array
    {
        $insert = [
            'title'        => $params['title'],
            'user'         => $params['user'],
            'invest'       => $params['invest'],
            'commission'   => $params['commission'],
            'signin'       => $params['signin'],
            'update_time'  => time(),
            'create_time'  => time()
        ];

        $TeamRepo  = new TeamRepository();
        $res       = $TeamRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 编辑
     * @param array $params
     * @return array
     */
    public function updateTeam(array $params): array
    {

        $update = [
            'title'        => $params['title'],
            'user'         => $params['user'],
            'invest'       => $params['invest'],
            'commission'   => $params['commission'],
            'signin'       => $params['signin'],
            'update_time'  => time(),
            'create_time'  => time(),
        ];

        $TeamRepo  = new TeamRepository();
        $res       = $TeamRepo->updateById($params['id'], $update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除
     * @param int $id
     * @return array
     */
    public function deleteTeam(int $id): array
    {
        $TeamRepo  = new TeamRepository();
        $res       = $TeamRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户升级记录
     * @param array $params
     * @return array
     */
    public function getTeamLogLists(array $params): array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        $TeamRepo  = new TeamLogRepository();
        $data      = $TeamRepo->paginates($where,'*', $params['limit'],['id' => 'desc']);

        return Result::success($data);
    }

    /**
     * 团队奖励记录
     * @param array $params
     * @return array
     */
    public function getTeamGiftLists(array $params): array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }


        $TeamRepo  = new TeamGiftRepository();
        $data      = $TeamRepo->paginates($where,'*', $params['limit'],['id' => 'desc']);

        foreach ($data['data'] as &$v)
        {
            $v['gift_time']  = date('Y-m-d H:i:s', $v['gift_time']);
        }

        return Result::success($data);
    }

}