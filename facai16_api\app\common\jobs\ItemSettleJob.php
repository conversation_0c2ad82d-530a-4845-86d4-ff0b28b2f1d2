<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\MoneyClass;
use app\common\repository\ItemLogRepository;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\BCalculator;
use app\common\utils\Record;
use think\facade\Db;
use think\facade\Log;
use think\queue\Job;

/**
 * 项目分红
 * Class ItemSettleJob
 * @package app\job
 */
class ItemSettleJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $id;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;


    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->id         = $data;


        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Record::exception('job', $exception);
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec()
    {
        $time           = time();
        $ItemOrderRepo  = new ItemOrderRepository();
        $order          = $ItemOrderRepo->findById($this->id);

        //无数据
        if (empty($order))
        {
            return;
        }

        //数据已经派发
        if ($order['item_status'] != 0 || $order['next_time'] > $time)
        {
            return;
        }

        $UserRepo = new UserRepository();
        $user     = $UserRepo->findById($order['uid']);

        //无数据
        if(!$user)
        {
            return;
        }

        $extra               = $order['profit_extra'];
        $profitRateExtra     = ($order['profit_rate'] + $extra) * $order['profit_more'] / 100;

        switch ($order['item_type'])
        {
            default:
                $profit       = BCalculator::calc($order['amount'])->mul($profitRateExtra)->result();
                $cycleNext    = $order['cycle_start'] + 1;

                if($cycleNext > $order['cycle_end'])
                {
                    $cycleNext = $order['cycle_end'];
                }
                break;
            case 1:
                $profit       = BCalculator::calc($order['amount'])->mul($profitRateExtra, $order['cycle_end'])->result();
                $cycleNext    = $order['cycle_end'];
                break;
        }


        Db::startTrans();

        try {

            //账变
            $MoneyLogRepo = new MoneyLogRepository();
            $res          = $MoneyLogRepo->fund($user['id'], $profit,MoneyClass::INCOME,(string) $order['id'],'项目返利:+' . $order['item_name'] . $profit .'元');

            if ($res['code'])
            {
                Record::log('job', $res['msg']);
                Db::rollback();
                return;
            }

            //数据统计
            $UserInfoRepo = new UserInfoRepository();
            $res          = $UserInfoRepo->statistic($user['id'],['income_money' => $profit,]);

            if ($res['code'])
            {
                Record::log('job', $res['msg']);
                Db::rollback();
                return;
            }

            //收益进度
            $profitNow = BCalculator::calc($order['profit_now'])->add($profit)->result();

            //写入收益记录
            $insert = [
                'uid'               => $user['id'],
                'username'          => $user['username'],
                'phone'             => $user['phone'],
                'is_test'           => $order['is_test'],
                'order_id'          => $order['id'],
                'order_no'          => $order['order_no'],
                'cycle_start'       => $cycleNext,
                'cycle_end'         => $order['cycle_end'],
                'profit_rate'       => $order['profit_rate'],
                'profit_more'       => $order['profit_more'],
                'profit_extra'      => $extra,
                'profit_now'        => $profitNow,
                'profit_total'      => $order['profit_total'],
                'create_time'       => $time,
                'update_time'       => $time,
            ];

            $ItemLogRepo = new ItemLogRepository();
            $res         = $ItemLogRepo->inserts($insert);


            if (!$res)
            {
                Record::log('job','写入结算记录表失败');
                Db::rollback();
                return;
            }

            //更新记录
            $update = [
                'cycle_start'       => $cycleNext,
                'next_time'         => $time + $order['cycle_time'],
                'last_time'         => $time,
                'profit_now'        => $profitNow,
                'update_time'       => $time,
                'item_status'       => ($cycleNext >= $order['cycle_end']) ? 2 : 0,
            ];


            $res = $ItemOrderRepo->updateById($order['id'], $update);

            if (!$res)
            {
                Record::log('job','修改订单失败');
                Db::rollback();
                return;
            }

            $where   = [];
            $where[] = ['uid','=',$user['id']];
            $info    = $UserInfoRepo->findByCondition($where);

            if ($info['invest_not_earn']  - $profit < 0)
            {
                $left = $info['invest_not_earn'];
            }
            else
            {
                $left = $profit;
            }

            $res   = $UserInfoRepo->statistic($user['id'],['invest_not_earn' => - $left]);

            if (!$res)
            {
                Record::log('job','修改订单失败');
                Db::rollback();
                return;
            }

            // 提交事务
            Db::commit();

            //利息返利
            $push  = [
                'money'     => $profit,
                'order_id'  => $order['id'],
                'uid'       => $user['id'],
            ];

            queue(TeamRebateJob::class, $push);

        } catch (\Exception $exception)
        {
            // 回滚事务
            Db::rollback();
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }
    }

    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('ItemSettleJob:' . $this->id,10);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('ItemSettleJob:' . $this->id);
    }

}

