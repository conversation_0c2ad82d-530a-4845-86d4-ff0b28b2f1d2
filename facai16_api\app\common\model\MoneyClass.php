<?php
namespace app\common\model;
use think\Model;


class MoneyClass extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $name = 'money_class';

//    use BaseRepository;


    const STYLE  = [
        //可提余额增加
        'INCREASE'            => 0,
        //可提需要钱少
        'DECREASE'            => 1,
        //冻结金额增加
        'FROZEN_INCREASE'     => 2,
        //冻结金额减少
        'FROZEN_DECREASE'     => 3,
        //可用冻结金额减少
        'FROZEN_MAX_DECREASE' => 4,
    ];

    /**
     * 充值
     */
    const RECHARGE  = 1;

    /**
     * 提现
     */
    const WITHDRAW  = 2;

    /**
     * 抽奖
     */
    const RAFFLE    = 3;

    /**
     * 购买
     */
    const BUY       = 4;

    /**
     * 收益
     */
    const INCOME    = 5;

    /**
     * 签到
     */
    const SIGNIN     = 6;

    /**
     * 一级返佣
     */
    const REBATE_V1 = 7;


    /**
     * 二级返佣
     */
    const REBATE_V2 = 8;


    /**
     * 提现拒绝
     */
    const WITHDRAW_REJECT = 9;



    /**
     * 返回本金
     */
    const RETURN_PRINCIPAL = 11;


    /**
     * 签到礼品领取
     */
    const SIGNIN_GIFT     = 12;


    /**
     * 积分增加
     */
    const POINTS_ADD     = 13;


    /**
     * 积分减少
     */
    const POINTS_DEL     = 14;


    /**
     * 优惠券增加
     */
    const COUPON_ADD     = 15;


    /**
     * 项目购买奖励
     */
    const ALLOWANCE      = 17;


    /**
     * 抽奖增加
     */
    const RAFFLE_ADD     = 18;

    /**
     * 抽奖减少
     */
    const RAFFLE_DEL     = 19;

    /**
     * 首存奖励
     */
    const FIRST_DEPOSIT_BONUS = 20;

    /**
     * 邀请奖励
     */
    const INVITE_MEMBER_BONUS = 25;

    /**
     * 余额宝存
     */
    const YUEBAO_DEPOSIT      = 26;

    /**
     * 余额宝取
     */
   const YUEBAO_WITHDRAWAL    = 27;

    /**
     * 三级返佣
     */
    const REBATE_V3 = 28;

    /**
     * 转账
     */
    const TRANSFER_IN = 29;

    /**
     * 月奖励
     */
    const MEMBER_MONTHLY_BONUS = 30;

    /**
     * 注册赠送
     */
    const REGISTER_GIFT_CASH  = 31;


    /**
     * 股权分红
     */
    const EQUITY_DIVIDENDS   = 32;


    /**
     * USDT充值奖励
     */
    const USDT_RECHARGE_GIFT = 33;

    /**
     * 团长升级奖励
     */
    const LEADER_UPGRADE_REWARDS   = 34;

    /**
     * 转账
     */
    const TRANSFER_OUT = 35;

    /**
     * 余额包收益
     */
    const YUE_BAO_INCOME = 36;

    /**
     * 复投转账
     */
    const REINVESTMENT_TRANSFER = 37;

    /**
     * 复投转账(进)
     */
    const REINVESTMENT_TRANSFER_IN = 38;


    /**
     * 复投奖励
     */
    const REINVESTMENT_GIFT         = 39;

    /**
     * 购买赠送现金
     */
    const BUY_ITEM_GIVE_CASH   = 40;
}