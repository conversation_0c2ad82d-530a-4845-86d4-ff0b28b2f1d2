<?php

return [
    'purchase_successful' => '购买成功',
    'successful_used_balance_purchased_good' => '进货商品：{title},成功，使用余额{money}',
    'purchased_goods_successful_gift_this_item' => '进货商品：{title}成功，赠送此商品',
    'successful_purchase_goods_with_complimentary_amount' => '进货商品：{title}成功，赠送金额{money}',
    'second_ago' => '{time}秒前',
    'minutes_ago' => '{time}分钟前',
    'hours_ago' => '{time}小时前',
    'days_ago' => '{time}天前',
    'login_successful' => '登录成功',
    'login_failed' => '登入失败',
    'user_login' => '用户登入',
    'registration_successful_initial_security_code' => '注册成功，初始安全码为123456',
    'success' => '成功',
    'the_purchase_amount_must_reach_success' => '进货金额须达到{title}成功，使用余额{money}',
    'successful_group_buying' => '拼团成功',
    'verification_code_operated_please_ignore_message' => '您的验证码是 {code}。如非本人操作，请忽略本短信',
    'successfully_sent' => '发送成功',
    'real_name_successful' => '实名成功',
    'recharge_successful_obtained' => '充值成功：获得 {money}',
    'invite_friends_to_make_their_first_deposit_get' => '邀请好友首充：获得 {money}',
    'verification_failed' => '验证失败',
    'order_status_conflict' => '订单状态冲突',
    'sign_in_daily_and_receive' => '每日签到,获得{money}',
    'fail' => '失败',
    'congratulations_on_obtaining' => '恭喜获得：{title}',
    'lucky_draw_get' => '幸运抽奖：{title}，获得{money}',
    'lucky_draw_success_product_will_given_as_gift' => '幸运抽奖：{title}成功，赠送此商品',
    'modified_successfully' => '修改成功',
    'cloud_quickpass' => '云闪付',
    'recharge_application_successful_please_be_patient_and_wait' => '充值申请成功，请耐心等待',
    'jumping_please_wait' => '正在跳转，请稍后',
    'withdrawal_application_deduction' => '提现申请：扣除{money}',
    'ordinary_team_leader'  => '普通团队长',
    'red_envelope_successfully_sent_please_patient_wait_for_review' => '发送红包成功，请耐心等待审核',
    'give_red_envelopes_reduce_money' => '红包给{phone},减少{money}',
    'deposit_amount_in_fresh_food_base' => '生鲜基地存入金额：{money}',
    'deposit_successful' => '存入成功',
    'withdrawal_amount_from_fresh_food_base' => '生鲜基地取出金额{money}',
    'purchased_goods_revenue' => '进货商品：{title}，收益{money}',
    'purchased_goods_periodic_return_of_principal' => '进货商品 {title}，周期返还本金{money}',
    'purchased_goods_end_return_of_principal' => '进货商品：{title}结束，返还本金{money}',
    'purchased_goods_end_refund_of_gift_amount' => '进货商品：{title}结束，返还赠送金额{money}',
    'invite_friends_for_profit_obtain' => '邀请好友盈利：获得{money}',
    'group_buying_successful_product_will_be_given_as_gift' => '拼团：{title}成功，赠送此商品',
    'cashback_for_failed_group_buying' => '拼团失败返现{money}',
    'revenue_from_fresh_produce_base'  => '生鲜基地收益：{money}',
    'team_leader_upgrade' => '生鲜基地收益：{money}',
    'investment_upgrade' => '投资升级：{title}',
    'invitation_for_investment_upgrade' => '邀请投资升级：{title}',
    'team_leader_upgrade2' => '团长升级：{title}',
    'team_leader_upgrade_free_amount' => '团队长升级：{title}，赠送金额{money}',
    'liked_successfully' => '点赞成功',
    'abnormal_functionality'    => '功能异常',
    'product_recommendations'   => '产品建议',
    'interface_optimization'    => '界面优化',
    'remaining_sum'             => '余额',
    'courtesy_card'             => '优惠券',
    'user_points'               => '积分',
    'successful_promotion_successful_use_funds' => '推广成功，成功使用{type}{money}',
    'user_points_increase' => '用户积分，增加{money}',

    'shopping_mall'     => '商城',
    'investment'        => '投资',
    'activity'          => '活动',
    'agent'             => '代理',
    'other'             => '其他',

    'network_platform'    => '网络平台',
    'family_and_friends'  => '亲朋好友',
    'store_shop'          => '门店商铺',

    'successful_group_buying_use_points' => '兑换：{title}成功，使用积分{price}',

];