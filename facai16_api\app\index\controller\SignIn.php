<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\index\service\SignInService;
use think\facade\Request;
use think\response\Json;


/**
 * 签到
 */
class SignIn
{

    /**
     * 签到状态
     * @return Json
     */
    public function signIn(): Json
    {
        $SignInService   = new SignInService();
        $result          = $SignInService->signIn();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 签到记录
     * @return Json
     */
    public function signInLog(): Json
    {

        $SignInService   = new SignInService();
        $result          = $SignInService->signInLog();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 礼品
     * @return Json
     */
    public function gifts(): <PERSON><PERSON>
    {

        $SignInService   = new SignInService();
        //余额
        $result          = $SignInService->gifts();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 签到
     * @return Json
     */
    public function sign(): Json
    {
        $id              = (new UserRepository())->userByHeader('id');
        $RedisLock       = new RedisLock();
        $status          = $RedisLock->lock('sign:' . $id);
        $SignInService   = new SignInService();


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理上一个订单');
            }

            //余额
            $result          = $SignInService->sign();

            $RedisLock->unLock('sign:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('sign:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }

    }


    /**
     * 签到
     * @return Json
     */
    public function signTeam(): Json
    {
        $id              = (new UserRepository())->userByHeader('id');
        $RedisLock       = new RedisLock();
        $status          = $RedisLock->lock('signTeam:' . $id);
        $SignInService   = new SignInService();


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理上一个订单');
            }

            //余额
            $result          = $SignInService->signTeam();

            $RedisLock->unLock('signTeam:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('signTeam:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }

    }


    /**
     * 记录
     * @return Json
     */
    public function record(): Json
    {

        $SignInService   = new SignInService();
        //余额
        $result          = $SignInService->record();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 签到礼品记录
     * @return Json
     */
    public function giftLog(): Json
    {
        $SignInService   = new SignInService();
        $result          = $SignInService->giftLog();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 礼品
     * @return Json
     */
    public function gift(): Json
    {
        $params = Request::only([
            'id' => 0,
        ]);

        $RedisLock       = new RedisLock();
        $status          = $RedisLock->lock('gift:' . $params['id']);
        $SignInService   = new SignInService();


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理上一个订单');
            }

            //余额
            $result          = $SignInService->gift($params['id']);

            $RedisLock->unLock('gift:' . $params['id']);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('gift:' . $params['id']);

            return Ajax::message(Record::exception('index',$exception));
        }

    }

}