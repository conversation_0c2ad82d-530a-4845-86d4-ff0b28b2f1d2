<?php
namespace app\common\strategy\pay;

use app\common\strategy\pay\lib\BaJiPay;
use app\common\strategy\pay\lib\BingTongPay;
use app\common\strategy\pay\lib\BingTongPay2;
use app\common\strategy\pay\lib\DfPay;
use app\common\strategy\pay\lib\EggPay;
use app\common\strategy\pay\lib\Epay;
use app\common\strategy\pay\lib\FeiCuiPay;
use app\common\strategy\pay\lib\MtPay;
use app\common\strategy\pay\lib\ShibaPay;
use app\common\strategy\pay\lib\SiHaiPay;
use app\common\strategy\pay\lib\ThsFastPay;
use app\common\strategy\pay\lib\XingKongPay;
use app\common\strategy\pay\lib\DarLiePay;
use app\common\strategy\pay\lib\HaiYangPay;
use app\common\strategy\pay\lib\HengTongsPay;
use app\common\strategy\pay\lib\XingLianPayVx;
use app\common\strategy\pay\lib\XingLianPayYL;
use app\common\strategy\pay\lib\XingLianPayYSF;
use app\common\strategy\pay\lib\XingLianPayZfb;
use app\common\strategy\pay\lib\YouYiPay;
use app\common\strategy\pay\lib\BiTongPayYL;


/**
 * 支付发送服务
 * Class SmsService
 * @package app\common\strategy\sms
 */
class PayService
{
    /**
     * 发起
     * @param string $orderNo
     * @param float $money
     * @param string $upper
     * @param string $channel
     * @return mixed
     */
    public function send(string $orderNo, float $money, string $upper, string $channel = '')
    {

        $data = [
            'orderNo'  => $orderNo,
            'money'    => $money,
            'channel'  => $channel,
        ];

        switch ($upper)
        {
            case 'HengTongsPay':
                $strategy = new HengTongsPay();
                break;

            case 'HaiYangPay':
                $strategy = new HaiYangPay();
                break;

            case 'DarLiePay':
                $strategy = new DarLiePay();
                break;

            case 'BingTongPay':
                $strategy = new BingTongPay();
                break;

            case 'BiTongPayYL':
                $strategy = new BiTongPayYL();
                break;

            case 'XingKongPay':

                $strategy = new XingKongPay();
                break;

            case 'XingLianPayZfb':
                $strategy = new XingLianPayZfb();
                break;

            case 'XingLianPayVx':
                $strategy = new XingLianPayVx();
                break;
            case 'XingLianPayYL':
                $strategy = new XingLianPayYL();
                break;
            case 'XingLianPayYSF':
                $strategy = new XingLianPayYSF();
                break;
            case 'BingTongPay2':
                $strategy = new BingTongPay2();
                break;
            case 'DfPay':
                $strategy = new DfPay();
                break;

            case 'ThsFastPay':
                $strategy = new ThsFastPay();
                break;

            case 'Epay':

                $strategy = new Epay();
                break;

            case 'EggPay':
                $strategy = new EggPay();
                break;

            case 'FeiCuiPay':
                $strategy = new FeiCuiPay();
                break;
            case 'SiHaiPay':
                $strategy = new SiHaiPay();
                break;
            case 'YouYiPay':
                $strategy = new YouYiPay();
                break;

            case 'MtPay':
                $strategy = new MtPay();
                break;
            case 'ShibaPay':
                $strategy = new ShibaPay();
                break;
            case 'BaJiPay':
                $strategy = new BaJiPay();
                break;
            default:
               return '';
        }


        $PayContext = new PayContext($strategy);

        return $PayContext->sendMessage($data);
    }


    /**
     * 回调
     * @return mixed
     */
    public function notify($switch)
    {

        switch ($switch)
        {
            case 'HengTongsPay':
                $strategy = new HengTongsPay();
                break;

            case 'HaiYangPay':
                $strategy = new HaiYangPay();
                break;
            case 'DarLiePay':
                $strategy = new DarLiePay();
                break;

            case 'BingTongPay':
                $strategy = new BingTongPay();
                break;

            case 'BiTongPayYL':
                $strategy = new BiTongPayYL();
                break;

            case 'XingKongPay':
                $strategy = new XingKongPay();
                break;

            case 'XingLianPayZfb':
                $strategy = new XingLianPayZfb();
                break;

            case 'XingLianPayVx':
                $strategy = new XingLianPayVx();
                break;

            case 'XingLianPayYL':
                $strategy = new XingLianPayYL();
                break;
            case 'XingLianPayYSF':
                $strategy = new XingLianPayYSF();
                break;
            case 'BingTongPay2':
                $strategy = new BingTongPay2();
                break;
            case 'DfPay':
                $strategy = new DfPay();
                break;

            case 'ThsFastPay':
                $strategy = new ThsFastPay();
                break;

            case 'FeiCuiPay':
                $strategy = new FeiCuiPay();
                break;

            case 'Epay':
                $strategy = new Epay();
                break;
            case 'EggPay':
                $strategy = new EggPay();
                break;
            case 'SiHaiPay':
                $strategy = new SiHaiPay();
                break;
            case 'YouYiPay':
                $strategy = new YouYiPay();
                break;

            case 'MtPay':
                $strategy = new MtPay();
                break;
            case 'ShibaPay':
                $strategy = new ShibaPay();
                break;
            case 'BaJiPay':
                $strategy = new BaJiPay();
                break;

            default:
                return '';
        }


        $PayContext = new PayContext($strategy);

        return $PayContext->notifyMessage();
    }

}