<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * MtPay
 */
class MtPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'MtPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'mchNo'         => $this->config['mch_id'],
            'appId'         => $this->config['app_id'],
            'mchOrderNo'    => $data['orderNo'],
            'wayCode'       => $data['channel'],
            'amount'        => $data['money'] * 100,
            'currency'      => 'cny',
            'clientIp'      => Request::ip(),
            'subject'       => $data['orderNo'],
            'body'          => $data['orderNo'],
            'notifyUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),
            'returnUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),
            'reqTime'       => time().'000',
            'version'       => '1.0',
            'signType'      => 'MD5'
        ];

        $param['sign']  = $this->signature($param);

        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        return $result['data']['payData'] ?? '';
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"ifCode":"itpay","amount":"500","payOrderId":"P1917807523608829953","mchOrderNo":"PAYVBHEGAHFJBIIJJA","subject":"PAYVBHEGAHFJBIIJJA","wayCode":"ITPAY","sign":"FF6D2429FD34EBB92B5B26E0600C9D83","channelOrderNo":"P1917807523608829953","reqTime":"1746076016264","body":"PAYVBHEGAHFJBIIJJA","createdAt":"1746075919427","appId":"681216e5e4b0fd87d4a42e27","clientIp":"***************","successTime":"1746076016000","currency":"cny","state":"2","mchNo":"M1746015973"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];

        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['mchOrderNo'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return strtoupper($sign);
    }

}