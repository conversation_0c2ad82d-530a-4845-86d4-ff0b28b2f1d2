<?php
namespace app\index\controller;

use app\common\strategy\pay\PayService;
use app\common\utils\Ajax;
use app\index\service\ApiService;
use think\facade\Request;
use think\facade\Validate;
use think\response\Json;

/**
 * 接口
 */
class Api
{

    /**
     * 支付 回调
     * @return mixed|string
     */
    public function notify()
    {
        $channel    = Request::param('ZhiFuTongDao','');
        $PayService = new PayService();
        return  $PayService->notify($channel);
    }


    /**
     * 支付 回调
     * @return mixed|string
     */
    public function callback()
    {
        $channel    = Request::param('ZhiFuTongDao','');
        $PayService = new PayService();
        return  $PayService->notify($channel);
    }



    /**
     * 客服
     * @return Json
     */
    public function customer(): Json
    {
        $ApiService         = new ApiService();
        $result             = $ApiService->customer();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 轮播图详情
     * @return Json
     */
    public function banner(): Json
    {
        $IndexService = new ApiService();
        $result       = $IndexService->banner();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 提示
     * @return Json
     */
    public function notice(): Json
    {
        $IndexService = new ApiService();
        $result       = $IndexService->notice();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 用户等级
     * @return Json
     */
    public function levels(): Json
    {
        $IndexService = new ApiService();
        $result       = $IndexService->levels();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 配置接口
     * @return Json
     */
    public function setting(): Json
    {
        $key            = Request::param('key', '');
        $ApiService     = new ApiService();
        $data           = $ApiService->setting($key);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 图片
     * @return Json
     */
    public function upload(): Json
    {
        $file    = Request::file('file');

        //上传图片文件
        $validate = Validate::rule([
            'image' => 'file|fileExt:jpg,png,gif|fileSize:10485760'
        ]);

        //得到上传文件和规则比对
        $result = $validate->check(['image' => $file,]);

        if (!$result)
        {
            return Ajax::fail('上传失败');
        }

        $ApiService     = new ApiService();

        $data           = $ApiService->upload($file);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }


    /**
     * 问答列表
     * @return json
     */
    public function question(): Json
    {
        $ApiService     = new ApiService();
        $data           = $ApiService->question();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }
}