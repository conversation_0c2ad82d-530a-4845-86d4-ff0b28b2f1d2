校订时间：2022-08-25
后端相关：
1、tab采用4个空格
2、所有"{"和"}" 单行显示
3、;后一定要跟换行或者空格
4、运算符前后必须有空格。
5、所有函数和方法、类必须有注释。至少简易的标明其含义或意义。
6、变量为非object类型内容时，使用小驼峰命名法，变量为object时，采用大驼峰命名方法。
7、变量或方法名必须有意义。
8、方法名定义：
  1)返回为数据时，以get为开头命名定义，意义为获取***的方法。
  2)检测***，且返回数据为布尔型，以check为开头命名定义，意义为检查****的方法。
  3)新增数据时，以add开头命名定义，意义为新增**的方法。
  4)修改数据时，以update开头命名定义，意义为修改***的方法。
  5)删除数据时，以delete开头命名定义，意义为删除***的方法。
9、类名采用采用大驼峰命名方法。
10、此项目运行在PHP7环境下，通常情况下需要定义方法参数的类型，除非参数为mixed
11、数组采用简易写法$array = [];
12、代码中不可出现任何写死的数字，数字定义为常量来使用。注意以下情况：
  1)只在数据库单表中使用的数字，例如，提款状态，0为未处理，1为处理成功，2为处理失败，3为处理中，此数字定义在对应model类中，定义为const常量，const命名格式为 WORD_WORD_WORD。
  2)业务中重复使用的数字，或者在数据库多表中使用的数字，在\app\common\define中定义，不同的定义在不同的类中。
13、配置文件内容，除特殊情况外，不可修改extra文件夹下以外的配置文件。
  1)business文件中放业务逻辑使用的配置，例如登录密码错误X次以上显示验证码。
  2)system文件中放系统相关的配置，例如中转站URL。
  3)api文件中存放三方平台密钥信息。
  4)如有其它配置不在以上两种业务范围内，可新建配置（需请示领导）。
14、后期需要处理的地方一定要写todo，方便在后期做处理。
15、关键节点一定要写log，方便线上出现问题后方便做调试。
16、业务不是特别熟悉的必须自己先写流程图、或者在代码中写入代码逻辑的注释，屡清楚业务逻辑后再进行开发。
17、参数接收时，应该做好类型和范围的判断，比如说int类型不能接收字符串也不做处理，另外如果是大于0的，需要加大于0的判断等等。枚举类型的参数也得做好判断，防止if else判断跳过了所有的
限制，不应该相信前端提交的任意参数值。甚至可以重复判断。
19、一切开发遵从mmvc原则，controller调用module里的方法，module调用model。controller只是接收参数(需进行基础的类型判断)，并且调用module方法返回数据，controller不允许调用model;
module写所有的参数校验（如是否合法范围等等），业务逻辑处理，包括调用model；model仅写数据库操作，不涉及到任何业务逻辑的处理。需严格遵循这种流程。
20、 关于数据tp数据操作:不要用save去新增或者更新数据;更新用->where()->update();不能直接update()更新, 新增用insert();禁止使用saveAll();禁止使用create();




