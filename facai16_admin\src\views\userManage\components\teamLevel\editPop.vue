<template>
    <el-form label-width="180px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="名称" required>
            <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="直推人数" required>
            <el-input v-model="form.user" />
        </el-form-item>
        <el-form-item label="团队业绩" required>
            <el-input v-model="form.invest" />
        </el-form-item>
        <el-form-item label="团队提成" required>
            <el-input v-model="form.commission" />
        </el-form-item>
        <el-form-item label="签到奖励" required>
            <el-input v-model="form.signin" />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import { withdrawTypeEnums, getLabelByVal } from '@/config/enums'

const form = ref({
    title: "",
    user: "",
    invest: "",
    commission: "",
    signin: "",
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(() => {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({ form })

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/deep/ .el-radio-group {
    width: 220px;
}

.form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
    width: 100%;
}

/deep/ .el-form-item {
    align-items: flex-start;
}
</style>