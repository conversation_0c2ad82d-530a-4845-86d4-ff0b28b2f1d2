<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input v-model="searchForm.title" placeholder="搜索标题" clearable />
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchForm.status" placeholder="状态" clearable>
          <el-option
            v-for="item in saleStatusEnums"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editItem('add', {})"
        v-permission
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <!-- <el-table-column prop="sort" sortable label="排序" width="60" /> -->
      <el-table-column prop="title" label="标题" width="150" />
      <el-table-column prop="class_name" label="分类" width="160" />
      <el-table-column prop="video_link" label="详情视频" width="160" />
      <el-table-column prop="status" sortable  label="状态" width="200">
        <template #default="scope">
          {{ getLabelByVal(scope.row.status, saleStatusEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="img" label="图片" width="200">
        <template #default="scope">
          {{ scope.row.img ? "" : "--" }}
          <el-image
            v-if="scope.row.img"
            class="previewImg"
            :preview-teleported="true"
            :src="proxy.IMG_BASE_URL + scope.row.img"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[proxy.IMG_BASE_URL + scope.row.img]"
            show-progress
            :initial-index="0"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="deduction" label="现金券抵扣" width="200" />
      <el-table-column prop="invest" label="起投金额" width="200" />
      <el-table-column prop="gift_coupon" label="奖励现金券" width="120" />
      <el-table-column prop="coupon_limit" label="现金券每天限制" width="120" />
      <el-table-column prop="profit_rate" label="收益率(%)" width="200" />
      <el-table-column prop="profit_cycle" label="周期" width="200" />
      <el-table-column prop="progress" label="进度" width="200" />

      <el-table-column prop="create_at" label="添加时间" width="160" />
      <el-table-column
        label="操作"
        fixed="right"
        width="200"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editItem('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="编辑/新增" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { saleStatusEnums, getLabelByVal } from "@/config/enums";
import EditPop from "./components/projectList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  title: "",
  status: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);
const formType = ref("add");

onMounted(() => {
  getList();
});

const editItem = (type, row = {}) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url = formType.value == "add" ? "item/addItem" : "item/updateItem";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      id: data.id,
      title: data.title,
      img: data.img,
      status: data.status,
      // sort: data.sort,
      class_id: data.class_id,
      invest: data.invest,
      invest_scale: data.invest_scale,
      invest_limit: data.invest_limit,
      profit_type: data.profit_type,
      profit_rate: data.profit_rate,
      profit_more: data.profit_more,
      profit_cycle: data.profit_cycle,
      profit_cycle_time: data.profit_cycle_time,
      gift_points: data.gift_points,
      gift_raffle: data.gift_raffle,
      gift_bonus: data.gift_bonus,
      content: data.content,
      desc: data.desc,
      progress: data.progress,
      progress_cycle: data.progress_cycle,
      progress_rate: data.progress_rate,
      progress_time: data.progress_time,
      deduction: data.deduction,
      gift_coupon: data.gift_coupon,
      video_link: data.video_link,
      coupon_limit: data.coupon_limit,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "item/deleteItem?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Item/getItemLists?class_id=2",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data.sort((a, b) => a.status - b.status);
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
