<?php
namespace app\index\service;

use app\common\repository\ArticleRepository;
use app\common\repository\BankRepository;
use app\common\utils\Result;
use app\common\utils\Uri;


/**
 * 文章模块
 */
class ArticleService
{
    /**
     * 新闻列表
     * @param $class
     * @return array
     */
    public function lists($class): array
    {
        $where = [];

        if ($class)
        {
            $where[] = ['class_id', '=', $class];
        }

        $ArticleRepo       = new  ArticleRepository();
        $data              = $ArticleRepo->paginates($where, '*' ,12);

        foreach ($data['data'] as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return Result::success($data);
    }

    /**
     * 新闻内容
     * @param int $id
     * @param string $code
     * @return array
     */
    public function info($id, string $code): array
    {

        $ArticleRepo          = new  ArticleRepository();

        if ($id)
        {
            $data              = $ArticleRepo->findById($id);
        }
        else
        {
            $data              = $ArticleRepo->findByCondition(['code' => $code]);
        }
        $data['img']           = Uri::file($data['img']);
        return Result::success($data);
    }
}