<?php
declare (strict_types = 1);

namespace app\common\command\nouse;


use app\common\utils\File;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;

/**
 * 数据备份
 * Class Backup
 * @package app\common\command
 */
class Backup extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('Backup')->setDescription('the Backup command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        // 数据库主机
        $host       = env('database.hostname');
        // 数据库用户名
        $username   = env('database.username');
        // 数据库密码
        $password   = env('database.password');
        // 数据库名称
        $dbname     = env('database.database');
        // 备份目录
        $backupDir  = runtime_path('backup');
        // 备份文件名
        $backupFile = $backupDir . $dbname . '_' . date('Y-m-d_H-i-s') . '.sql';

        // 检查备份目录是否存在，如果不存在则创建
        File::mkdir($backupDir, 0755, true);

        // 构造 mysqldump 命令
        $command = "mysqldump -h $host -u $username -p$password $dbname > $backupFile";

        // 执行命令
        exec($command, $output, $result);

        // 检查执行结果
        if ($result === 0)
        {
            Log::channel('command')->info("数据库备份成功，备份文件：" . $backupFile);
        } else
        {
            Log::channel('command')->error("数据库备份失败");
        }
    }



}

