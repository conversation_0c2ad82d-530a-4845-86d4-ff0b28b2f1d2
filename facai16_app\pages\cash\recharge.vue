<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							充值
						</view>
					</template>
					<template #right>
						<view class="color-#fff w-128 h-46 t-26 fcc f-pr">
							<image src="/static/cash/czmx.png" mode="aspectFit" class="w-128 h-49 block"></image>
							<view class="fcc f-pa inset">
								充值明细
							</view>
						</view>
					</template>
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="f-pr">
						<image src="/static/cash/kbg.png" mode="widthFix" class="w-750 h-300 block"></image>
						<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
							<view class="flex pt-70 mb-40">
								<view class="w-280">
									<view class="mb-4">
										账户余额(USDT)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										121234.00
									</view>
								</view>
								<view class="w-280">
									<view class="mb-4">
										账户余额(元)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										121234.00
									</view>
								</view>
							</view>
							<view>
								充值请联系在线客服获取平台收款账号,请转账成功后提交订单
							</view>
						</view>
					</view>
					
					<view class="mx-34 r-20 bg-#fff px-32 mb-32">
						<view class="t-32 color lh-45 pt-26 pb-15">
							选择充值方式
						</view>
						<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>
						<view class="flex flex-wrap justify-between pb-20">
							<view class="mb-12 w-300 92" @click="tabIndex =0">
								<image src="/static/cash/cz-u-h.png" mode="widthFix" class="w-300 h-92 block" v-if="tabIndex==0"></image>
								<image src="/static/cash/cz-u.png" mode="widthFix" class="w-300 h-92 block" v-else></image>
							</view>
							<view class="mb-12 w-300 92" @click="tabIndex = 1">
								<image src="/static/cash/cz-b-h.png" mode="widthFix" class="w-300 h-92 block" v-if="tabIndex==1"></image>
								<image src="/static/cash/cz-b.png" mode="widthFix" class="w-300 h-92 block" v-else></image>
							</view>
							
						</view>
					</view>
					
					<view class="mx-34 r-20 bg-#fff px-32 py-40">
						<view class="fcc">
							<image src="/static/cash/kefu.png" mode="aspectFit" class="w-198 h-175 mb-83"></image>
							
						</view>
						<view class="btn-full fcc">
							联系客服
						</view>
					</view>	
					
					
					
					<view class="h-40"></view>
				</scroll-view>
				
			</view>
			<view>
				3
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIndex:0
			};
		}
	}
</script>

<style lang="scss" scoped>
	
	.page-bg{
		background: url(/static/cash/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>
