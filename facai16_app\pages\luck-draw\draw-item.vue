<template>
	<view class=" py-21 fc-bet">
		<view class="mr-16">
			<image src="/static/my/tx.png" mode="aspectFill" class="block size-80 r-100"></image>
		</view>
		<view class="flex1">
			<view class="c2 t-30 lh-42">
				{{item.raffle_name}}
			</view>
			<view class="c9 t-24 lh-26">
				{{item.create_at}}
			</view>
		</view>
		<view class="color t-32 lh-45">
			{{item.amount}}元
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item']
	}
</script>

<style>

</style>