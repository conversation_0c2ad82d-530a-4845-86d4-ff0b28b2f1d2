# 管理后台 (facai16_admin) 详细结构

## 目录结构

```
facai16_admin/
├── src/
│   ├── components/        # 公共组件
│   │   └── adminTable.vue # 通用表格组件
│   ├── views/             # 页面视图
│   │   ├── userManage/    # 用户管理
│   │   ├── projectManage/ # 项目管理
│   │   ├── operationManage/ # 运营管理
│   │   ├── goodsManage/   # 商品管理
│   │   ├── payments/      # 支付管理
│   │   ├── sysSetting/    # 系统设置
│   │   ├── auth/          # 权限管理
│   │   └── backendManage/ # 后台管理
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── utils/             # 工具函数
│   ├── config/            # 配置文件
│   ├── layout/            # 布局组件
│   └── directive/         # 自定义指令
├── public/                # 静态资源
└── package.json           # 依赖配置
```

## 核心页面功能

### 用户管理模块 (views/userManage/)

#### userList.vue - 用户列表
**功能**:
- 用户信息展示
- 用户状态管理
- 用户资金操作
- 用户转移功能
- 用户等级设置

**组件依赖**:
- `editUserForm.vue` - 编辑用户表单
- `userMoneyForm.vue` - 资金操作表单
- `userTransfer.vue` - 用户转移表单
- `userStateForm.vue` - 状态修改表单

#### userTransfer.vue - 转移审核
**功能**:
- 转移申请列表
- 审核操作
- 状态管理

#### userWallet.vue - 用户钱包
**功能**:
- 钱包信息管理
- 资金流水查看

#### withdrawList.vue - 提现管理
**功能**:
- 提现申请列表
- 提现审核
- 提现记录

### 项目管理模块 (views/projectManage/)

#### projectList.vue - 项目列表
**功能**:
- 项目信息管理
- 项目状态控制
- 项目配置

#### projectRecord.vue - 投资记录
**功能**:
- 投资记录查看
- 收益统计
- 数据导出

### 运营管理模块 (views/operationManage/)

#### noticeList.vue - 公告管理
**功能**:
- 公告发布
- 公告编辑
- 公告状态管理

#### articleList.vue - 文章管理
**功能**:
- 文章内容管理
- 富文本编辑
- 文章分类

### 系统设置模块 (views/sysSetting/)

#### configList.vue - 系统配置
**功能**:
- 系统参数配置
- 功能开关设置
- 基础数据管理

#### menuList.vue - 菜单管理
**功能**:
- 菜单结构管理
- 权限分配
- 路由配置

## 公共组件

### adminTable.vue - 通用表格
**功能**:
- 数据表格展示
- 分页处理
- 排序筛选
- 操作按钮集成

**属性**:
- `tableData` - 表格数据
- `columns` - 列配置
- `pagination` - 分页配置
- `operations` - 操作按钮配置

## 路由配置 (router/index.js)

### 路由结构
```javascript
{
  path: '/userManage',
  name: 'userManage',
  children: [
    { path: 'userList', component: userList },
    { path: 'userTransfer', component: userTransfer },
    { path: 'userWallet', component: userWallet },
    { path: 'withdrawList', component: withdrawList }
  ]
}
```

## 状态管理 (store/index.js)

### State状态
- `userInfo` - 当前登录用户信息
- `menuList` - 菜单权限列表
- `configList` - 系统配置列表

### Actions操作
- `login()` - 用户登录
- `logout()` - 用户登出
- `getMenus()` - 获取菜单权限
- `getConfigs()` - 获取系统配置

## 工具函数 (utils/)

### axios.js - HTTP请求
**功能**:
- 请求拦截器
- 响应拦截器
- 错误处理
- Token自动添加

### auth.js - 认证工具
**功能**:
- Token管理
- 登录状态检查
- 权限验证

### utils.js - 通用工具
**功能**:
- 日期格式化
- 数据处理
- 文件上传

## 配置文件

### enums.js - 枚举配置
**内容**:
- 用户状态枚举
- 订单状态枚举
- 支付状态枚举
- 审核状态枚举

## 环境配置

### .env.development - 开发环境
```
VUE_APP_BASE_URL=/facai16
VUE_APP_BASE_API_URL=https://facai16-api.g20-1.com/admin.php/
VUE_APP_IMG_BASE_URL=https://facai16-api.g20-1.com
```

### .env.facai - 生产环境
```
VUE_APP_BASE_URL=https://facai16-api.g20-1.com/admin.php
VUE_APP_BASE_API_URL=https://facai16-api.g20-1.com/admin.php/
VUE_APP_IMG_BASE_URL=https://facai16-api.g20-1.com
```
