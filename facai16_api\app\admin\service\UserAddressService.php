<?php
namespace app\admin\service;

use app\common\repository\UserAddressRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Result;

/**
 * 用户地址
 */
class UserAddressService
{
    /**
     * 获取地址列表
     * @param $params
     * @return array
     */
    public function getUserAddressLists($params): array
    {

        $where = [];

        if (!empty($params['start_time']))
        {
            $where[] = ['create_time', '>=', strtotime($params['start_time'])];
        }

        if (!empty($params['end_time']))
        {
            $where[] = ['create_time', '<', strtotime($params['end_time'])];
        }


        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        $UserAddressRepo = new UserAddressRepository();
        $data            = $UserAddressRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 获取地址信息
     * @param $id
     * @return array
     */
    public function getUserAddressInfo($id): array
    {
        $UserAddressRepo    = new UserAddressRepository();
        $data               = $UserAddressRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加用户地址
     * @param $phone
     * @param $addressName
     * @param $addressPhone
     * @param $addressCity
     * @param $addressPlace
     * @param $default
     * @return array
     */
    public function addUserAddress($phone, $addressName, $addressPhone, $addressCity, $addressPlace, $default): array
    {
        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findByCondition(['phone' => $phone]);

        if (!$user)
        {
            return Result::fail('没有用户信息');
        }

        $where           = [];
        $where[]         = ['uid', '=', $user['id']];
        $UserAddressRepo = new UserAddressRepository();
        $has             = $UserAddressRepo->findByCondition($where);

        if ($has)
        {
            return Result::fail('已经存在地址请勿重复提交');
        }

        $insert = [
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'is_test'       => $user['is_test'],
            'uid'           => $user['id'],
            'address_name'  => $addressName,
            'address_phone' => $addressPhone,
            'address_city'  => $addressCity,
            'address_place' => $addressPlace,
            'default'       => $default,
            'create_time'   => time(),
            'update_time'   => time()
        ];

        $UserAddressRepo    = new UserAddressRepository();
        $res                = $UserAddressRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新用户地址
     * @param $id
     * @param $phone
     * @param $addressName
     * @param $addressPhone
     * @param $addressCity
     * @param $addressPlace
     * @param $default
     * @return array
     */
    public function updateUserAddress($id, $phone, $addressName, $addressPhone, $addressCity, $addressPlace, $default): array
    {

        $UserAddressRepo = new UserAddressRepository();
        $info            = $UserAddressRepo->findById($id);

        if (!$info)
        {
            return Result::fail('订单不存在');
        }

        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findByCondition(['phone' => $phone]);

        if (!$user)
        {
            return Result::fail('没有用户信息');
        }

        $update = [
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'uid'           => $user['id'],
            'is_test'       => $user['is_test'],
            'address_name'  => $addressName,
            'address_phone' => $addressPhone,
            'address_city'  => $addressCity,
            'address_place' => $addressPlace,
            'default'       => $default,
            'update_time'   => time()
        ];

        $res                = $UserAddressRepo->updateById($id, $update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除用户地址
     * @param $id
     * @return array
     */
    public function deleteUserAddress($id): array
    {
        $UserAddressRepo    = new UserAddressRepository();

        $res                = $UserAddressRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    
}
