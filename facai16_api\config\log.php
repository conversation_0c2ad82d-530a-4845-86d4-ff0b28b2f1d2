<?php

// +----------------------------------------------------------------------
// | 日志设置
// +----------------------------------------------------------------------
return [
    // 默认日志记录通道
    'default'      => env('log.channel', 'file'),
    // 日志记录级别
    'level'        => [],
    // 日志类型记录的通道 ['error'=>'email',...]
    'type_channel' => [],
    // 关闭全局日志写入
    'close'        => false,
    // 全局日志处理 支持闭包
    'processor'    => null,

    // 日志通道列表
    'channels'     => [
        'file' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => '',
            // 单文件日志写入
            'single'         => false,
            // 独立日志级别
            'apart_level'    => [],
            // 最大日志文件数量
            'max_files'      => 0,
            // 使用JSON格式记录
            'json'           => false,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 日志输出格式化
            'format'         => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => false,
        ],

        // 其它日志通道配置
        'command' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/command',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        // 其它日志通道配置
        'recharge' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/recharge',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        // 其它日志通道配置
        'job' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/job',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],


        // 其它日志通道配置
        'index' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/index',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        // 其它日志通道配置
        'admin' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/admin',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        // 其它日志通道配置
        'repository' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/repository',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],


        'http' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/http',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],


        'oss' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/oss',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],


        'utils' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/utils',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        'pay' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/pay',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],

        'sms' => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/sms',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],


        'operation'  => [
            'type'          => 'File',
            'path'          => app()->getRootPath() . 'runtime/operation',
            'time_format'   => 'Y-m-d H:i:s',
            'format'        => '[%s][%s]:%s'
        ],



    ],

];
