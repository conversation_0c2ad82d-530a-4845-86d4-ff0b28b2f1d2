<?php
namespace app\admin\service;

use app\common\repository\ArticleClassRepository;
use app\common\repository\ArticleRepository;
use app\common\repository\BankRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 文章
 */
class ArticleService
{
    /**
     * 文章列表
     * @param $params
     * @return array
     */
    public function getArticleLists($params): array
    {

        $where = [];

        if (!empty($params['title']))
        {
            $where[] = ['title', '=', $params['title']];
        }

        $ArticleRepo   = new ArticleRepository();
        $data          = $ArticleRepo->paginates($where,'*', $params['limit']);

        return Result::success($data);
    }

    /**
     * 文章信息
     * @param $id
     * @return array
     */
    public function getArticleInfo($id): array
    {
        $ArticleRepo = new ArticleRepository();
        $data        = $ArticleRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加文章
     * @param $params
     * @return array
     */
    public function addArticle($params): array
    {
        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $ArticleClassRepo       =  new ArticleClassRepository();
            $class                  =  $ArticleClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }

        $ArticleRepo = new ArticleRepository();
        $res         = $ArticleRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新文章
     * @param $params
     * @return array
     */
    public function updateArticle($params): array
    {

        $time                   = Request::time();
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $ArticleClassRepo       =  new ArticleClassRepository();
            $class                  =  $ArticleClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }

        $ArticleRepo = new ArticleRepository();
        $res         = $ArticleRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除文章
     * @param $id
     * @return array
     */
    public function deleteArticle($id): array
    {
        $ArticleRepo = new ArticleRepository();
        $res         = $ArticleRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 文章类型列表
     * @return array
     */
    public function getArticleClassLists(): array
    {
        $ArticleRepo = new ArticleClassRepository();
        $data        = $ArticleRepo->paginates();

        return Result::success($data);
    }


    /**
     * 文章类型信息
     * @param $id
     * @return array
     */
    public function getArticleClassInfo($id): array
    {
        $ArticleRepo = new ArticleClassRepository();
        $data        = $ArticleRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加文章类型
     * @param $params
     * @return array
     */
    public function addArticleClass($params): array
    {

        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        $ArticleRepo = new ArticleClassRepository();
        $res         = $ArticleRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新文章类型
     * @param $params
     * @return array
     */
    public function updateArticleClass($params): array
    {
        $ArticleRepo = new ArticleClassRepository();

        $res       = $ArticleRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除文章类型
     * @param $id
     * @return array
     */
    public function deleteArticleClass($id): array
    {
        $ArticleRepo = new ArticleClassRepository();

        $res       = $ArticleRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}
