<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							资金明细
						</view>
					</template>

				</uv-navbar>

				<view
					class="mx-32 r-100 border-1 border-solid border-#407CEE bg-#000 bg-op-50 p-8 t-28 lh-40 color flex">
					<!-- <view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==0}" @click="tabIndex = 0">
						全部
					</view> -->
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==0}"
						@click="tabIndex = 0">
						充值
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==1}"
						@click="tabIndex = 1">
						提现
					</view>
				</view>
				<view class="h-16"></view>
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30 overflow-y-hidden">
				<RechargeRecord ref="rechargeRecord" v-if="tabIndex==0" />
				<WithdrewalRecord ref="withdrewalRecord" v-if="tabIndex==1" />
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import RechargeRecord from './components/recharge-record.vue'
	import WithdrewalRecord from './components/withdrewal-record.vue'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		components: {
			RechargeRecord,
			WithdrewalRecord
		},
		data() {
			return {
				tabIndex: -1
			};
		},
		watch: {
			tabIndex(newVal, oldVal) {
				this.$nextTick(() => {
					if (newVal == 0) {
						this.$refs.rechargeRecord.onRefresh(0)
					} else {
						this.$refs.withdrewalRecord.onRefresh(0)
					}
				})
			}
		},
		methods: {

		},
		onReady() {
			console.log(this.$route)
			this.$nextTick(() => {
				if (this.$route.query && this.$route.query['tabIndex']) {
					this.tabIndex = +this.$route.query['tabIndex']
				} else {
					this.tabIndex = 0
				}

			})
		}
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>