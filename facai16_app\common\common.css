.page-wraper {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
}

.page-main {
	flex: 1;
	position: relative;
}

.m-scroll-view {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	height: 100%;
	/* background: linear-gradient(to bottom, #C644FC,#f5f5f5); */
	background-color: #f5f5f5;
}

.title-text {
	color: #141416;
	font-size: 40rpx;
	font-weight: bold;
}

.common-button {
	background: #1C84EC;
	color: white;
	border: none;
}
.flex-row{
	display: flex;
	flex-direction: row;
	align-items: center;
}

.flex-column{
	display: flex;
	flex-direction: column;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.flex-row-center {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
}

.flex-row-space-between {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}
