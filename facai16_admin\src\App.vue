<template>
  <Layout />
</template>

<script setup>
import Layout from '@/layout/index'
</script>

<style lang="less">
* {
  box-sizing: border-box;
}

html, body, p, img, h1, h2, h3, h4, h5, h6, ul, li {
  margin: 0;
  padding: 0;
}
.previewImg {
  width: 100px; 
  max-height: 150px;
  height: auto;
  margin: 0 auto;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}

*::-webkit-scrollbar {
    width: 4px;
  }

*::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 10px;
}

*::-webkit-scrollbar-thumb {
    background: #aaa;
    border-radius: 10px;
  }

*::-webkit-scrollbar-thumb:hover {
    background: #aaa;
    border-radius: 10px;
  }

*::-webkit-scrollbar-thumb:active {
    background: #aaa;
    border-radius: 10px;
  }

  .link {
    color: #90dcfe;
    cursor: pointer;
  }
  .green { color: green;}
  .red { color: red;}
</style>
