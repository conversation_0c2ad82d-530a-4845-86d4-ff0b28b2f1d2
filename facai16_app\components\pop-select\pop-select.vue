<template>
	<view>
		<uni-popup ref="popup" type="bottom" :safeArea="false" isMaskClick>
			<view>
				
				<view class="pop-box bg-fff">
					<view class="pt-32 t-26 c9 lh-37 fcc">
						{{title}}
					</view>
					<view class="item-row fcc" v-for="(item,index) in list" :key="index" @click="clk(index)">
						{{item}}
					</view>
					
					<view class="item-row  item-close">
						<view class="flex1 fcc" @click="close">
							取消
						</view>
					</view>
					<view class="btn-area"></view>
					
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name:"pop-select",
		props: {
			title: {
				type: String,
				default: '标题'
			},
			list:{
				type:Array,
				default:()=>{
					return []
				}
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
			clk(item){
				this.$emit('select',item)
				this.close()
			},
			open() {
				this.$refs.popup.open()
			},
			close(){
				this.$refs.popup.close()
			}
		},
	}
</script>

<style lang="scss" scoped>
	
	.pop-box{
		background: #F6F9FC;
		border-radius: 32rpx 32rpx 0px 0px;
	}
	.bg-fff{
		background: #fff;
	}
	.min{
		min-height: 42vh;
	}
	.item-row {
		text-align: center;
		font-size: 32rpx;
		color: #222;
		
		line-height: 109rpx;
		border-bottom: 1rpx solid #f7f7f7;
	}
	.item-close{
		border: 0;
	}
</style>