<?php
namespace app\common\repository;
use app\common\model\UserState;


class UserStateRepository extends UserState
{
    use BaseRepository;




    public function registerUsers($today, $tomorrow, $isTest, $members): int
    {
        $where                  = [];
        $where[]                = ['create_time', '>=', $today];
        $where[]                = ['create_time', '<', $tomorrow];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];
        $members && $where[] = ['uid', 'in', $members];

        return $this->countByCondition($where);
    }



    public function realNameUsers($today, $tomorrow, $isTest, $members): int
    {
        $where                  = [];
        $where[]                = ['create_time', '>=', $today];
        $where[]                = ['create_time', '<', $tomorrow];
        $where[]                = ['sfz_status', '=', 0];
        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

        $members && $where[] = ['uid', 'in', $members];

        return $this->countByCondition($where);
    }
}
