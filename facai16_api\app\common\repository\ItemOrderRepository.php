<?php
namespace app\common\repository;
use app\common\model\ItemOrder;
use app\common\utils\Record;


class ItemOrderRepository extends ItemOrder
{
    use BaseRepository;



    /**
     * 去重count
     */
    public function uniqCountBy($star, $end, $field ='uid', $isTest = '', $members = []): int
    {
        $where      = [];
        $where[]    = ['create_time', '>=', $star];
        $where[]    = ['create_time', '<', $end];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

        $members && $where[] = ['uid', 'in', $members];

        return $this->distinct(true)->field($field)->where($where)->count();
    }


    /**
     * 总订单量
     */
    public function totalItemNum($star, $end,  $isTest = '', $members = []): int
    {
        $where      = [];
        $where[]    = ['create_time', '>=', $star];
        $where[]    = ['create_time', '<', $end];

        is_numeric($isTest) &&  $where[] = ['is_test', '=', $isTest];

        $members && $where[] = ['uid', 'in', $members];

        return $this->field('id')->where($where)->count();
    }


    public function selectNotFinishList($where1,$where2): array
    {
        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];

            $data = $this->master(true)->whereOr($where1)->order($order)->field('*')->paginate(10);

            return  $data->toArray();

        }catch (\Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->paginates');
            return [];
        }
    }

}
