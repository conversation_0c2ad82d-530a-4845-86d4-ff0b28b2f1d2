<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							投资记录
						</view>
					</template>
				</uv-navbar>

				<view
					class="mx-32 r-100 border-1 border-solid border-#407CEE bg-#000 bg-op-50 p-8 t-28 lh-40 color flex">
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==1}"
						@click="tabIndex = 1">
						普通专区
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==2}"
						@click="tabIndex = 2">
						VIP专区
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==3}"
						@click="tabIndex = 3">
						福利专区
					</view>
				</view>
				<view class="h-16"></view>
			</view>
			<view class="flex1 f-pr c9 t-22 lh-30">
				<InvestRecordListVue ref='project1' tab='1' v-if="tabIndex==1" />
				<InvestRecordListVue ref='project2' tab='2' v-if="tabIndex==2" />
				<InvestRecordListVue ref='project3' tab='3' v-if="tabIndex==3" />
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import InvestRecordListVue from './invest-record-list.vue';
	export default {
		components: {
			InvestRecordListVue
		},
		data() {
			return {
				tabIndex: 0
			};
		},
		watch: {
			tabIndex(newVal) {
				if (newVal > 0) {
					this.$nextTick(() => {
						this.$refs[`project${this.tabIndex}`].getRecord()
					})
				}
			}
		},
		onReady() {
			this.$nextTick(() => {
				this.tabIndex = 1
			})
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>