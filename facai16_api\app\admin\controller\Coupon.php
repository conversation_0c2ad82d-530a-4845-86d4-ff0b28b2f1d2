<?php
namespace app\admin\controller;

use app\admin\service\CouponService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 优惠券
 */
class Coupon
{
    /**
     * 优惠券列表
     * @return mixed
     */
    public function getCouponLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'status',
            'limit' => 10
        ]);

        $CouponService   = new CouponService();
        $data            = $CouponService->getCouponLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 添加优惠券
     * @return Json
     */
    public function addCoupon(): Json
    {
        $params      = Request::only([
            'phone',
            'amount',
            'status',
            'expire_time',
            'allow'
        ]);

        $CouponService   = new CouponService();
        $data            = $CouponService->addCoupon($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 更新优惠券
     * @return Json
     */
    public function updateCoupon(): Json
    {
        $params      = Request::only([
            'phone',
            'amount',
            'status',
            'expire_time',
            'id' => 0,
            'allow'

        ]);

        $CouponService   = new CouponService();
        $data            = $CouponService->updateCoupon($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 删除反馈
     * @return Json
     */
    public function deleteCoupon(): Json
    {
        $id              = Request::param('id',0);
        $CouponService   = new CouponService();
        $data            = $CouponService->deleteCoupon($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}