<?php

namespace app\common\strategy\pay\lib;

use app\common\define\Payment;
use app\common\repository\UserRechargeRepository;
use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\PayService;
use app\common\utils\Times;
use think\facade\Config;
use think\facade\Db;
use think\facade\Request;


class ThreePartPayment implements PayInterface
{
    protected $config;

    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.ThreePartPayment');
    }

    /**
     * 充值
     * @param array $data
     * @return mixed
     */
    public function send(array $data)
    {
        $url        = Request::root(true);

        $notifyUrl  = $url . 'plugIn/recharge_bank_notify?strategy=' . Payment::THREE_PART_PAYMENT;

        $params     = [
            'callbackUrl'   => $notifyUrl,
            'channelCode'   => $this->config['channelCode'],
            'orderId'       => $data['order_no'],
            'orderMoney'    => $data['money'],
            'userCode'      => $this->config['userCode'],
        ];


        $data    = $this->signature($params);
        $url     = $this->config['url'];
        $headers = array('Content-Type: application/x-www-form-urlencoded');

        $ch = curl_init();
        curl_setopt($ch,CURLOPT_URL,$url); //支付请求地址
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response=curl_exec($ch);

        //$res=simplexml_load_string($response);

        curl_close($ch);

        return json_decode($response, true);
    }

    /**
     * 回调
     */
    public function notify()
    {
        $data = Request::param();

        $sign = $data['sign'];

        unset($data['sign']);

        $verify     = $this->signature($data);

        $PayService = new PayService();


        if($verify['sign'] == $sign)
        {
            $orderNo   = $data['orderId'];

            if($data['orderStatus'] == 2)
            {
                $res        = $PayService->recharge($orderNo);

                return   empty($res) ? 'success' : __('order_status_conflict');

            }else
            {

                $UserRechargeRepo = new UserRechargeRepository();

                $where      = [];
                $where[]    = ['order_no', '=', $orderNo];
                $recharge   = $UserRechargeRepo->findByCondition($where);

                if(empty($recharge) || $recharge['status'] > 0 || $recharge['way_id'] > 1)
                {
                    return 'success';
                }

                $update = [
                    'status'    => 2,
                    'edittime'  => Times::Date(),
                ];

                $UserRechargeRepo->updateById($recharge['id'], $update);
            }

            return 'success';
        }

        return __('verification_failed');
    }


    /**
     * 签名
     * @param array $data
     * @return array
     */
    protected function signature(array $data): array
    {
        $key = $this->config['key'];

        ksort($data);

        $str = '';

        foreach ($data as $k => $v)
        {
            if(!empty($v))
            {
                $str .= $k . '=' . $v . '&';
            }
        }

        $str = rtrim($str,'&');

        $str .= "&key=" . $key;

        $data['sign'] = strtolower(md5($str));

        return $data;
    }

}
