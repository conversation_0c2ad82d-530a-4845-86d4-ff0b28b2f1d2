<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\MessageService;
use think\facade\Request;
use think\response\Json;

/**
 * 站内信
 */
class Message
{

    /**
     * 站内信列表
     * @return void
     */
    public function lists() : <PERSON><PERSON>
    {
        $MessageService     = new MessageService();
        $result             = $MessageService->lists();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 站内信内容
     * @return Json
     */
    public function content() : J<PERSON>
    {

        $params             = Request::only(['id']);
        $MessageService     = new MessageService();
        $result             = $MessageService->content($params['id']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}
