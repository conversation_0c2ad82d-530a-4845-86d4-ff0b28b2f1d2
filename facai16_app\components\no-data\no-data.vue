<template>
	<view>
		<view class="nodata" v-if="network">
			 <image src="/static/no-internet.png" mode="aspectFit"></image>
			 <view>
				 <slot>网络不给力~</slot>
				 <view class="color fcc t-32 mt-35">
					 点击刷新
				 </view>
			 </view>
		</view>
		<view class="nodata" v-else-if="error">
			 <image src="/static/404.png" mode="aspectFit"></image>
			 <view>
				 <slot>404</slot>~
			 </view>
		</view>
		<view class="nodata" v-else-if="loading">
			 <image src="/static/no-so.png" mode="aspectFit"></image>
			 <view>
				 <slot>没有符合条件的结果～</slot>~
			 </view>
		</view>
		<view class="nodata" v-else>
			 <image src="/static/no-data.png" mode="aspectFit"></image>
			 <view>
				 <slot>暂无数据</slot>~
			 </view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"no-data",
		props: {
			network: {
				type: Boolean,
				default:false 
			},
			loading: {
				type: <PERSON>olean,
				default:false 
			},
			error: {
				type: Boolean,
				default:false 
			},
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">

</style>