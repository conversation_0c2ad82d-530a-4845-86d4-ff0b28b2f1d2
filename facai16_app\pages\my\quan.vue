<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							我的卡券
						</view>
					</template>

				</uv-navbar>


				<!-- <view
					class="mx-32 r-100 border-1 border-solid border-#407CEE bg-#000 bg-op-100 p-8 t-28 lh-40 color flex">
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==0}"
						@click="tabIndex = 0">
						全部
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==1}"
						@click="tabIndex = 1">
						已使用
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==2}"
						@click="tabIndex = 2">
						已过期
					</view>
				</view>
				<view class="h-16"></view> -->
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30">
				<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true"
					:refresher-triggered="refresherTriggered" @refresherrefresh='onRefresh' scroll-y="true"
					refresher-background='#ffffff00' style="height: calc(100vh - 50px);">
					<view class="p-12">
						<view class="f-pr" v-for="item in list" :key="item.id">
							<image src="/static/details/quanbg.png" mode="aspectFit" class="w-726 h-220 block"></image>
							<view class="f-pa w-686 h-180 top-20 left-20 flex">
								<view class="w-236 fcc-h color">
									<view class="t-28">
										<text>￥</text><text class="DIN t-80">{{Number(item.amount).toFixed(2)}}</text>
									</view>
									<!-- <view class="t-28">
										现金券
									</view> -->
								</view>
								<view class="flex1  flex flex-col justify-center">
									<view class="mb-16 t-32 lh-45 color-#070E38">
										{{item.desc}}
									</view>
									<!-- <view class="mb-15 color-#83869B t-24 lh-33">
										满500元可用
									</view> -->
									<view class="t-22 lh-30 color-#B5B7C9">
										{{item.create_at}}
									</view>
								</view>
								<!-- <view class="w-178 fcc">
									<view class="w-128 h-60 bg-#407CEE r-90 t-24 color-#fff fcc">
										立即使用
									</view>
								</view> -->
							</view>
						</view>
					</view>
					<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
					<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
						@loadmore="scrollHandle" />
					<view class="h-20"></view>
					<view class="btn-area"></view>
				</scroll-view>
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import * as CashApi from '@/api/cash.js'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		onShow() {
			this.getRecord()
		},
		methods: {
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await CashApi.getMoneyLog({
						class_id: '13,14',
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>