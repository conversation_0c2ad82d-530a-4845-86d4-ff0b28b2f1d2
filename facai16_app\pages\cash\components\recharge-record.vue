<template>
	<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true" :refresher-triggered="refresherTriggered"
		@refresherrefresh='onRefresh' scroll-y="true" refresher-background='#ffffff00'
		style="height: calc(100vh - 100px);">
		<view class="p-32 !pt-16">
			<box-box :blue="true">
				<view class="mx-32 py-32 border-b border-b-solid border-white border-op-20"
					v-for="(item,index) in list">
					<view class="fc-bet">
						<view>
							<view class="t-32 lh-45 fw-600 mb-4">
								{{ item.class_name }}
							</view>
							<view>
								{{ item.create_at }}
							</view>
						</view>
						<view>
							<view class="t-32 lh-45 fw-600 mb-4 DIN text-right">
								{{ item.amount }}
							</view>
							<uv-text type="warning" v-if="item.status == 1" align='right' text="待审核"></uv-text>
							<uv-text type="success" v-if="item.status == 0" align='right' text="成功"></uv-text>
							<uv-text type="error" v-if="item.status == 2" align='right' text="已拒绝"></uv-text>
						</view>
					</view>
				</view>
				<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
			</box-box>
			<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
				@loadmore="scrollHandle" />
		</view>
		<view class="h-20"></view>
		<view class="btn-area"></view>
	</scroll-view>
</template>

<script>
	import * as CashApi from '@/api/cash.js'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		onShow() {
			console.log('onShow')
		},
		methods: {
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await CashApi.rechargeRecord({
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
			toCopy(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					},

					fail: () => {
						uni.showToast({
							title: "复制失败"
						})
					}

				})
			}
		},
	}
</script>