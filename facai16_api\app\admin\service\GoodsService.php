<?php
namespace app\admin\service;

use app\common\repository\ArticleClassRepository;
use app\common\repository\FeedbackRepository;
use app\common\repository\GoodsClassRepository;
use app\common\repository\GoodsRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;
use think\response\Json;

/**
 * 产品
 */
class GoodsService
{

    /**
     * 产品列表
     * @param array $params
     * @return array
     */
    public function getGoodsLists(array $params): array
    {
        $GoodsRepo = new GoodsRepository();
        $data      = $GoodsRepo->paginates([],'*',$params['limit'],['id'=> 'desc']);
        return Result::success($data);
    }

    /**
     * 获取产品信息
     * @param $id
     * @return array
     */
    public function getGoodsInfo($id): array
    {
        $GoodsRepo = new GoodsRepository();
        $data      = $GoodsRepo->findById($id);
        return Result::success($data);
    }

    /**
     * 添加产品
     * @param $params
     * @return array
     */
    public function addGoods($params): array
    {

        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $GoodsClassRepo         =  new GoodsClassRepository();
            $class                  =  $GoodsClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }

        $GoodsRepo = new GoodsRepository();
        $res       = $GoodsRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新产品
     * @param $params
     * @return array
     */
    public function updateGoods($params): array
    {

        $time                   = Request::time();
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $GoodsClassRepo         =  new GoodsClassRepository();
            $class                  =  $GoodsClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }

        $GoodsRepo = new GoodsRepository();

        $res       = $GoodsRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除产品
     * @param $id
     * @return array
     */
    public function deleteGoods($id): array
    {
        $GoodsRepo = new GoodsRepository();

        $res       = $GoodsRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 获取产品类型列表
     * @return array
     */
    public function getGoodsClassLists(): array
    {
        $GoodsRepo = new GoodsClassRepository();
        $data      = $GoodsRepo->paginates([],'*',10,['id'=> 'desc']);
        return Result::success($data);
    }

    /**
     * 获取产品类型信息
     * @param $id
     * @return array
     */
    public function getGoodsClassInfo($id): array
    {
        $GoodsRepo = new GoodsClassRepository();
        $data      = $GoodsRepo->findById($id);
        return Result::success($data);
    }

    /**
     * 添加产品类型
     * @param $params
     * @return array
     */
    public function addGoodsClass($params): array
    {
        $params['create_time'] = Request::time();
        $params['update_time'] = Request::time();

        $GoodsRepo = new GoodsClassRepository();
        $res       = $GoodsRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新产品类型
     * @param $params
     * @return array
     */
    public function updateGoodsClass($params): array
    {
        $params['update_time'] = Request::time();


        $GoodsRepo = new GoodsClassRepository();

        $res       = $GoodsRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除产品类型
     * @param $id
     * @return array
     */
    public function deleteGoodsClass($id): array
    {
        $GoodsRepo = new GoodsClassRepository();

        $res       = $GoodsRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}
