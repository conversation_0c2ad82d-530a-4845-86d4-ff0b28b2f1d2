<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							提现
						</view>
					</template>
					<template #right>
						<navigator url="/pages/cash/record?tabIndex=1">
							<view class="color-#fff w-128 h-46 t-26 fcc f-pr">
								<image src="/static/cash/czmx.png" mode="aspectFit" class="w-128 h-49 block"></image>
								<view class="fcc f-pa inset">
									提现明细
								</view>
							</view>
						</navigator>
					</template>
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="f-pr">
						<image src="/static/cash/kbg.png" mode="widthFix" class="w-750 h-300 block"></image>
						<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
							<view class="flex pt-70 mb-40">
								<!-- <view class="w-280">
									<view class="mb-4">
										账户余额(USDT)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										{{userInfo.money}}
									</view>
								</view> -->
								<view class="w-280">
									<view class="mb-4">
										账户余额(元)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										{{userInfo.money}}
									</view>
								</view>
							</view>

						</view>
					</view>
					<view class="mx-34 mb-32">
						<view class="bg-#fff r-20">
							<view class="px-32 r-20">
								<view class="t-32 color lh-45 pt-26 pb-19">
									选择提现方式
								</view>
								<uv-gap height="1" bgColor="#96B6FC" marginBottom="32rpx"></uv-gap>
								<view class="flex flex-wrap justify-between pb-20">
									<!-- <view class="mb-12 w-300 92" @click="tabIndex =0">
										<image src="/static/cash/cz-u-h.png" mode="widthFix" class="w-300 h-92 block"
											v-if="tabIndex==0"></image>
										<image src="/static/cash/cz-u.png" mode="widthFix" class="w-300 h-92 block"
											v-else></image>
									</view> -->
									<view class="mb-12 w-300 92" @click="tabIndex = 1">
										<image src="/static/cash/tx-b-h.png" mode="widthFix" class="w-300 h-92 block"
											v-if="tabIndex==1"></image>
										<image src="/static/cash/tx-b.png" mode="widthFix" class="w-300 h-92 block"
											v-else></image>
									</view>
								</view>
								<view v-if="tabIndex == 1" class="pb-20">
									<view v-for="(item,index) in bankList" :key="item.id" @click="clickPayment(item)">
										<BankItem :item="item" :currentId='currentId' />
										<uv-gap height="1" margin-bottom="5px"></uv-gap>
									</view>
									<view class="py-10">
										<navigator url="/pages/my/add-bank">
											<view class="btn-full fcc">
												添加银行卡
											</view>
										</navigator>
										<view class="btn-area"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="mx-34 mb-32" v-if="tabIndex==1">
						<view class="bg-#fff r-20">
							<view class="px-32">
								<view class="t-32 color lh-45 pt-26  fc-bet pb-19">
									<text>
										选择金额(元)
									</text>
								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>


								<view class="flex flex-wrap mr-[-30rpx]">
									<view class="fcc w-196 h-92 mr-12 mb-20 r-20 DIN  t-40"
										v-for="(item,index) in moneyList" :key="index" @click="moneyClick(item,index)"
										:class="[index == moneyIndex ? 'bg-#407CEE color-#fff' : 'bg-#EDF5FF c2']">
										{{item}}
									</view>
								</view>

								<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#EAF3FE fc">
									<view class="c2 t-26 lh-37 pl-36 pr-20 fw-600">
										自定义
									</view>
									<view class="w-1 bg-#979797 h-29"></view>
									<view class="input-row flex1 fc">
										<input type="tel" class="w-370 ml-20 h-80 lh-80 t-40 DIN" v-model="moneyVal">
									</view>
									<view class="c2 t-26 lh-37 pl-36 pr-20 ">
										元
									</view>
								</view>
								<view class="h-20"></view>
							</view>
						</view>
					</view>
					<view class="mx-34 mb-32" v-else>
						<view class="bg-#fff r-20">
							<view class="px-32">
								<view class="t-32 color lh-45 pt-26  fc-bet pb-19">
									<text>
										选择金额(USDT)
									</text>

								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>
								<view class="flex flex-wrap mr-[-30rpx]">
									<view class="fcc w-196 h-92 mr-12 mb-20 r-20 DIN  t-40"
										v-for="(item,index) in moneyList" :key="index" @click="moneyClick(item,index)"
										:class="[index == moneyIndex ? 'bg-#407CEE color-#fff' : 'bg-#E9F2FD c2']">
										{{item}}
									</view>
								</view>

								<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#EBF4FE fc">
									<view class="c2 t-26 lh-37 pl-36 pr-20 fw-600">
										自定义
									</view>
									<view class="w-1 bg-#979797 h-29"></view>
									<view class="input-row flex1 fc">
										<input type="tel" class="w-300 ml-20 h-80 lh-80 t-40 DIN" v-model="moneyVal">
									</view>
									<view class="c2 t-26 lh-37 pl-36 pr-20 ">
										USDT
									</view>
								</view>
								<view class="h-20"></view>
							</view>
						</view>
					</view>

					<view class="mx-34 mb-32">
						<view class="bg-#fff r-20">
							<view class="px-32">
								<view class="t-32 color lh-45 pt-26  fc-bet pb-19">
									<text>
										交易密码
									</text>

								</view>
								<uv-gap height="1" bgColor="#5A8BF9" marginBottom="28rpx"></uv-gap>
								<view
									class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30 border-1 border-solid border-#4C81F9">

									<view class="input-row flex1 fc">
										<input type="number" :maxlength="6" v-model="pin" password
											class="w-full h-80 lh-80 t-32 c2" placeholder="请输入交易密码">
									</view>

								</view>
								<view class="h-20"></view>
							</view>
						</view>
					</view>
					<view class="mx-34 mb-32">
						<view class="bg-#fff r-20">
							<view class="p-32">
								<view class="fc-bet mb-32">
									<image src="/static/sign/tit-arr.png" mode="aspectFit" class="block w-216 h-33">
									</image>
									<view class="fcc t-30 c2 lh-40 px-20">
										温馨提示
									</view>
									<image src="/static/sign/tit-arr.png" mode="aspectFit"
										class="block w-216 h-33 rotate-180deg"></image>
								</view>
								<view class="color-#ccc t-28 lh-40">
									温馨提示: <br>
									提现时间为9:00-20:00<br>
									银行卡最低提现额度为300USDT<br>
									最低提现额度为30USDT<br>
									提现成功后请提供交易成功凭证截图<br>
								</view>
							</view>
						</view>
					</view>

					<view class="h-40"></view>
				</scroll-view>

			</view>
			<view class="bg-#fff">
				<view class="py-10 px-40">
					<view class="btn-full fcc" @click="toWithdrawal">
						立即提现
					</view>
					<view class="btn-area"></view>
				</view>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import BankItem from '@/pages/my/bank-item.vue'

	export default {
		mixins: [needAuth],
		components: {
			BankItem
		},
		data() {
			return {
				tabIndex: 1,
				moneyIndex: 0,
				moneyList: [
					500, 3000, 6000, 10000, 20000, 50000
				],
				moneyVal: null,
				pin: null,
				currentMethod: {},
			};
		},
		computed: {
			...mapState(['userInfo', 'moneyDetail', 'bankList']),
			seletMoneyVal() {
				if (this.moneyIndex > -1) {
					return this.moneyList[this.moneyIndex]
				} else {
					return null
				}
			},
			payMoney() {
				return this.moneyVal || this.seletMoneyVal
			},
		},
		watch: {
			moneyVal(newValue, oldValue) {
				if (newValue) {
					this.moneyIndex = -1
				} else {
					this.moneyIndex = 0
				}
			},
			bankList(newValue, oldValue) {
				if (newValue.length > 0) {
					this.currentMethod = newValue.find(item => item.default);
				}
			}
		},
		methods: {
			async toWithdrawal() {
				let err = ''

				if (this.payMoney > this.userInfo.money) {
					err = '可提现额度不足'
				} else
				if (!this.currentMethod.id) {
					err = '请选择出款方式'
				} else if (!this.pin) {
					err = '请填写交易密码'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await CashApi.withdrawRecord({
							money: this.payMoney,
							id: this.currentMethod.id,
							pin: this.pin
						})
						uni.navigateTo({
							url: '/pages/cash/record?tabIndex=1'
						})
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			},
			moneyClick(item, index) {
				this.moneyIndex = index
				this.moneyVal = null
			},
			clickPayment(item) {
				this.currentMethod = item
			},
		},
		onShow() {

		}
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/cash/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>