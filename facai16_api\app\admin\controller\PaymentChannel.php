<?php
namespace app\admin\controller;


use app\admin\service\PaymentChannelService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 支付渠道
 */
class PaymentChannel
{
    /**
     * 获取支付分类
     * @return mixed
     */
    public function getPaymentChannelLists(): Json
    {
        $PaymentService = new PaymentChannelService();
        $data           = $PaymentService->getPaymentChannelLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取分类信息
     * @return Json
     */
    public function getPaymentChannelInfo(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentChannelService();
        $data           = $PaymentService->getPaymentChannelInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加分类信息
     * @return Json
     */
    public function addPaymentChannel(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentChannelService();
        $data           = $PaymentService->addPaymentChannel($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新渠道
     * @return Json
     */
    public function updatePaymentChannel(): Json
    {
        $param          = Request::param();

        $PaymentService = new PaymentChannelService();
        $data           = $PaymentService->updatePaymentChannel($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除渠道
     * @return Json
     */
    public function deletePaymentChannel(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentChannelService();
        $data           = $PaymentService->deletePaymentChannel($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}