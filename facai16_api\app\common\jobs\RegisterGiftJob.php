<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\MoneyClass;
use app\common\model\Shares;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SharesLogRepository;
use app\common\repository\SharesRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use think\facade\Log;
use think\queue\Job;

/**
 * 注册礼品
 * Class RegisterGiftJob
 * @package app\job
 */
class RegisterGiftJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data): void
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;


        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec(): void
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->findById($this->info['uid']);

        $SystemSetRepo   = new SystemSetRepository();
        $where           = [];
        $where[]         = ['key','=', 'register_gift_cash'];
        $cash            = $SystemSetRepo->valueByCondition($where,'val');

        if ($cash)
        {
            $MoneyLogRepo   = new MoneyLogRepository();
            $res            = $MoneyLogRepo->fund($this->info['uid'], $cash,MoneyClass::REGISTER_GIFT_CASH,'register','用户注册金额奖励 +' . $cash);

            if (empty($res))
            {
                Record::log('job','用户注册奖励失败' . json_encode($this->info));
            }
        }

        $SharesRepo = new SharesRepository();
        $share       = $SharesRepo->findById(Shares::DEFAULT);

        $insert = [
            'uid'         => $user['id'],
            'phone'       => $user['phone'],
            'username'    => $user['username'],
            'is_test'     => $user['is_test'],
            'shares'      => $share['shares'],
            'release'     => $share['release'],
            'money'       => $share['money'],
            'cycle_start' => 0,
            'cycle_end'   => $share['cycle'],
            'create_time' => time(),
            'update_time' => time(),
        ];

        $SharesLog      = new SharesLogRepository();
        $res            = $SharesLog->inserts($insert);

        if (empty($res))
        {
            Record::log('job','用户注册送股权奖励失败' . json_encode($this->info));
        }

    }



    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('RegisterGiftJob:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock(): void
    {
        $this->RedisLock->unLock('RegisterGiftJob:' . $this->info['uid']);
    }

}

