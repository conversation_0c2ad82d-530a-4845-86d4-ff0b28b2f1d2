<?php
namespace app\admin\controller;

use app\admin\service\UserService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 用户
 */
class User
{

    /**
     * 用户列表
     * @return Json
     */
    public function getUserLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'invite',
            'sfz_status',
            'level',
            'is_online',
            'online_time',
            'is_signin',
            'level_team',
            'sfz_name'          => '',
            'sort_point'        => '',
            'sort_yuebao'       => '',
            'sort_money'        => '',
            'sort_frozen_money' => '',
            'sort_coupon_num'   => '',
            'limit' => 10,
            'blow',
            'is_test',
        ]);


        $UserService    = new UserService();
        $data           = $UserService->getUserLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取用户信息全部
     * @return Json
     */
    public function getUserInfo(): Json
    {
        $id            = Request::param('id',0);
        $UserService   = new UserService();
        $data          = $UserService->getUserInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 获取用户信息
     * @return Json
     */
    public function getUser(): Json
    {
        $id            = Request::param('id',0);
        $UserService   = new UserService();
        $data          = $UserService->getUser($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新用户
     * @return Json
     */
    public function updateUser(): Json
    {
        $param         = Request::param();
        $UserService   = new UserService();
        $data          = $UserService->updateUser($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 用户统计信息
     * @return Json
     */
    public function getUserInfos(): Json
    {
        $id            = Request::param('id',0);
        $UserService   = new UserService();
        $data          = $UserService->getUserInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 用户状态信息
     * @return Json
     */
    public function getUserState(): Json
    {
        $id            = Request::param('id',0);
        $UserService   = new UserService();
        $data          = $UserService->getUserState($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 更新用户状态
     * @return Json
     */
    public function updateUserState(): Json
    {
        $params        = Request::param();
        $UserService   = new UserService();
        $data          = $UserService->updateUserState($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新用户状态
     * @return Json
     */
    public function updateUserInfo(): Json
    {
        $params        = Request::param();
        $UserService   = new UserService();
        $data          = $UserService->updateUserInfo($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 添加用户
     * @return Json
     */
    public function addUser(): Json
    {
        $param         = Request::only([
            'username' => '',
            'password' => '123456',
            'phone'    => '',
        ]);

        $UserService   = new UserService();
        $data          = $UserService->addUser($param['username'], $param['password'], $param['phone']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 删除用户
     * @return Json
     */
    public function deleteUser(): Json
    {
        $id            = Request::param('id',0);
        $UserService   = new UserService();
        $data          = $UserService->deleteUser($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 用户迁移
     * @return Json
     */
    public function userRemove(): Json
    {
        $params         = Request::only([
            'from_user' => '',
            'to_user'   => '',
            'lv'        => 1,
        ]);

        $UserService   = new UserService();
        $data          = $UserService->userRemove($params['from_user'], $params['to_user'], $params['lv']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取登入
     * @return Json
     */
    public function getLoginLists(): Json
    {
        $params         = Request::only([
            'phone'     => '',
            'starttime' => '',
            'endtime'   => '',
        ]);

        $UserService   = new UserService();
        $data          = $UserService->getLoginLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}