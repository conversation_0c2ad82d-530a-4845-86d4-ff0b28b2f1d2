<?php

namespace app\common\command;

use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\YueBaoRepository;
use app\common\utils\BCalculator;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;


/**
 * 余额宝
 * Class YueBao
 * @package app\common\command
 */
class YueBao extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('YueBao')->setDescription('the YueBao command');
    }


    /**
     * 余额宝
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        $time            = time();
        $SystemSetRepo   = new SystemSetRepository();
        $shouYiLv        = $SystemSetRepo->valueByCondition(['key' => 'yuebao_day_shouyi'],'val');
        $UserRepo        = new UserRepository();
        $MoneyLogRepo    = new MoneyLogRepository();
        $YueBaoRepo      = new YueBaoRepository();
        $UserInfoRepo    = new UserInfoRepository();

        $where      = [];
        $where[]    = ['type', '=', 1];
        $where[]    = ['finish_money', '>', 0];
        $where[]    = ['settle_time', '<', $time];
        $users      = $YueBaoRepo->distinctColumnByCondition($where,'uid');


        foreach ($users as $uid)
        {
            $user        = $UserRepo->findById($uid,'id,phone,username,is_test');

            if (empty($user))
            {
                continue;
            }

            $where      = [];
            $where[]    = ['uid', '=', $uid];
            $where[]    = ['type', '=', 1];
            $where[]    = ['settle_time', '<', $time];
            $total      = $YueBaoRepo->sumByCondition($where,'finish_money');

            if ($total <= 0)
            {
                continue;
            }


            Db::startTrans();

            try {

                $where      = [];
                $where[]    = ['uid', '=', $uid];
                $where[]    = ['type', '=', 1];
                $where[]    = ['settle_time', '<', $time];

                $update = [
                    'settle_time' => $time + 86400,
                    'update_time' => $time,
                ];

                $res = $YueBaoRepo->updateByCondition($where, $update);

                if (!$res)
                {
                    Db::rollback();
                    Record::log('command','YUE_BAO_INCOME11----' . json_encode($update));
                    continue;
                }

                $shouYi = BCalculator::calc($total)->mul($shouYiLv)->div(100)->result();

                $update = [
                    'yuebao_earn'  => $shouYi,
                    'yuebao_num'   => 1,
                ];

                $res = $UserInfoRepo->statistic($uid, $update);

                if ($res['code'])
                {
                    Db::rollback();
                    Record::log('command','YUE_BAO_INCOME1----' . $res['msg'] . '----' . json_encode($update));
                    continue;
                }

                $info        = "余额宝收益:" . $shouYi;

                $insert      = [
                    'uid'           => $user['id'],
                    'username'      => $user['username'],
                    'phone'         => $user['phone'],
                    'is_test'       => $user['is_test'],
                    'type'          => \app\common\model\YueBao::TYPE['benefits'],
                    'finish_money'  => 0,
                    'money'         => $shouYi,
                    'info'          => $info,
                    'create_time'   => time(),
                    'update_time'   => time(),
                ];

                $res        = $YueBaoRepo->inserts($insert);

                if (!$res)
                {
                    Db::rollback();
                    Record::log('command','YUE_BAO_INCOME2----'  . json_encode($insert));
                    continue;
                }

                $res          = $MoneyLogRepo->fund($user['id'], $shouYi,MoneyClass::YUE_BAO_INCOME,1, $info);

                if ($res['code'])
                {
                    Db::rollback();
                    Record::log('command','YUE_BAO_INCOME3----'  . json_encode($res));
                    continue;
                }

                // 提交事务
                Db::commit();

            } catch (\Exception $e)
            {
                // 回滚事务
                Db::rollback();
                Record::exception('command',$e,'YUE_BAO_INCOME3----');

            }

        }



    }

}

