<template>
    <el-form label-width="170px"  :model="form" class="demo-form-inline">
        <el-form-item label="当前用户[手机号码]" required>
            <el-input v-model="form.from_user" clearable />
        </el-form-item>
        <el-form-item label="上级用户[邀请码]" required>
            <el-input v-model="form.to_user" clearable />
        </el-form-item>
        <!-- <el-form-item label="等级" required>
            <el-select v-model="form.lv" placeholder="" clearable>
                <el-option label="等级一" :value="1" />
                <el-option label="等级二" :value="2" />
            </el-select>
        </el-form-item> -->
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'

const form = ref({
    from_user: '',
    to_user: '',
    // lv: '',
})


const props = defineProps(['item'])
onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
        form.value.from_user = form.value.phone
    })
})


defineExpose({form})
</script>

<style lang="less" scoped>
.demo-form-inline {
    margin: 0 auto;
}
.demo-form-inline .el-input {
    --el-input-width: 260px;
}

.demo-form-inline .el-select {
    --el-select-width: 260px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}

</style>