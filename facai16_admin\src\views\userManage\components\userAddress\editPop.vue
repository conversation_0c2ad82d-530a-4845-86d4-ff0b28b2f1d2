<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="后台手机" required>
            <el-input v-model="form.phone" />
        </el-form-item>
        <!-- <el-form-item label="总充值" required>
            <el-input v-model="form.recharge_money" disabled />
        </el-form-item>
        <el-form-item label="总提款" required>
            <el-input v-model="form.withdraw_money" disabled />
        </el-form-item> -->
        
        <el-form-item label="是否默认" required>
            <el-radio-group v-model="form.default">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
            </el-radio-group>
        </el-form-item>


        <el-form-item label="姓名" required>
            <el-input v-model="form.address_name" clearable />
        </el-form-item>
        <el-form-item label="手机号" required>
            <el-input v-model="form.address_phone" clearable />
        </el-form-item>
        <el-form-item label="省市区" required>
            <el-input v-model="form.address_city" clearable />
        </el-form-item>
        <el-form-item label="详细地址" required>
            <el-input v-model="form.address_place" clearable />
        </el-form-item>

    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import { withdrawTypeEnums, getLabelByVal } from '@/config/enums'

const form = ref({
    phone: '',
    default: '',
    address_name: '',
    address_phone: '',
    address_city: '',
    address_place: '',

})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(() => {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({ form })

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/deep/ .el-radio-group {
    width: 220px;
}

.form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
}

/deep/ .el-form-item {
    align-items: flex-start;
}
</style>