<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * BiTongPayYL
 */
class BiTongPayYL implements PayInterface
{
    //支付类型
    const CHANNEL = 'BiTongPayYL';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'mchId'         => $this->config['mch_id'],
            'appId'         => $this->config['app_id'],
            'productId'     => $data['channel'],
            'mchOrderNo'    => $data['orderNo'],
            'amount'        => (string)($data['money'] * 100),
            'clientIp'      => request()->ip(),
            'subject'       => '充值',
            'body'          => '充值',
            'notifyUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),
        ];

        $param['sign']  = $this->signature($param);
        $paramsStr      = http_build_query($param);
        $result         = httpPost($this->config['url'], $paramsStr);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $uri            = '';

        if(isset($result['retCode'])  && $result['retCode'] == 'SUCCESS')
        {
            $uri = $result['payParams']['payUrl'] ?? '';
        }

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"status":"2","code":"drenjie-zfb","orderno":"17402125841410","amount":"500.00","sign":"d24e591f6c68a22a5b0a3c4a68c10965"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];
        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['mchOrderNo'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($data){

        ksort($data);  //字典排序
        reset($data);

        $md5str = "";

        foreach ($data as $key => $val) {
            if( strlen($key)  && strlen($val) ){
                $md5str = $md5str . $key . "=" . $val . "&";
            }
        }
        $sign = md5($md5str . "keySign=" . $this->config['sign']. "Apm");  //签名

        return $sign;

    }

}