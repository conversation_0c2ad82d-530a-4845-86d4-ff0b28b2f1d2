<template>
  <el-form
    label-width="80px"
    :inline="true"
    ref="formRef"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="用户名" required>
      <el-input v-model="form.username" disabled clearable />
    </el-form-item>
    <el-form-item label="账变类型" prop="class_id" :rules="[{
        required: true,
        message: '请选择账变类型',
        trigger: ['change']
    }]">
        <el-select v-model="form.class_id" placeholder="账变类型" clearable>
          <el-option
            v-for="item in typesEnum"
            :label="item.title"
            :key="item.title"
            :value="item.id"
          />
        </el-select>
    </el-form-item>
    <!-- <el-form-item label="余额" required>
            <el-input v-model="form.money" clearable />
        </el-form-item>
        <el-form-item label="类型" required>
            <el-radio-group v-model="form.recharge_type">
                <el-radio value="1">增加</el-radio>
                <el-radio value="2">减少</el-radio>
            </el-radio-group>
        </el-form-item> -->
    <el-form-item label="金额" prop="money3" :rules="[{
        required: true,
        message: '请输入金额',
        trigger: ['blur']
    }]">
      <el-input v-model="form.money3" clearable />
    </el-form-item>

    <!-- <el-form-item label="备注" required>
            <el-input v-model="form.remark" clearable />
        </el-form-item> -->
  </el-form>
</template>

<script setup>
import { getCurrentInstance, onMounted, ref, nextTick } from "vue";

const form = ref({
  username: "",
  money3: "",
  class_id: ''
});
const formRef = ref(null)

const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
    getTYpesEnum()
})

const { proxy } = getCurrentInstance()
const typesEnum = ref([])

const getTYpesEnum = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: '/MoneyLog/classes'
    })
    if (res.code == 0) {
        typesEnum.value = res.data
    }
}


defineExpose({ form, formRef });
</script>

<style lang="less" scoped>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}
</style>
