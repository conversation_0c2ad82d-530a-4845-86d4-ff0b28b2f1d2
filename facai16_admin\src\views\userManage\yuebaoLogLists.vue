<template>
  <adminTable
    :hideResetButton="true"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="用户ID" width="100" />
      <el-table-column prop="username" label="用户名" width="160" />
      <el-table-column prop="phone" label="手机号码" width="100" />
      <el-table-column prop="is_test" label="账号类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.is_test, accountTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="is_test" label="类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.type, yuebaoTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="money" label="金额" width="130" />
      <el-table-column prop="info" label="备注" width="130" />
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { accountTypeEnums, yuebaoTypeEnums, getLabelByVal } from "../../config/enums";
import { useRoute } from 'vue-router'

const route = useRoute()
const searchForm = ref({
  phone: ''
});

onMounted(() => {
  if (route.query && route.query.phone) {
    searchForm.value.phone = route.query.phone
  }
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);
const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/YueBao/yueBaoLog",
    params: {
      ...searchForm.value,
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
