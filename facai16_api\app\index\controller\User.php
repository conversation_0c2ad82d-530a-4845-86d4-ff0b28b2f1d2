<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\UserService;
use think\facade\Request;
use think\response\Json;

/**
 * 用户信息
 * Class User
 * @package app\home\controller
 */
class User
{
    /**
     * 等级
     * @return Json
     */
    public function level(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->level();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 集字
     * @return Json
     */
    public function words(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->words();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 用户信息
     */
    public function info(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->info();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 详情
     * @return Json
     */
    public function detail(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->detail();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 状态
     * @return Json
     */
    public function state(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->state();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 实名认证
     * @return Json
     */
    public function realName(): Json
    {
        $params        = Request::only([
            'name'      => '',
            'idcard'    => '',
            'bank'      => '',
            'address'   => '',
            'branch'    => ''
        ]);

        $UserService   = new UserService();
        $result        = $UserService->realName($params['name'], $params['idcard'], $params['bank'], $params['address'], $params['branch']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }




    /**
     * 资金管理
     * @return Json
     */
    public function fund(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->fund();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 修改密码
     * @return Json
     */
    public function editPass(): Json
    {
        $params      = Request::only(['password', 'confirmPassword', 'smscode' => '']);

        $UserService = new UserService();

        $result      = $UserService->editPass($params['password'], $params['confirmPassword'], $params['smscode']);


        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 修改密码
     * @return Json
     */
    public function editPin(): Json
    {

        $params      = Request::only(['password', 'confirmPassword', 'smscode' => '']);

        $UserService = new UserService();

        $result      = $UserService->editPin($params['password'], $params['confirmPassword'], $params['smscode']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }




    /**
     * 判断安全密码
     * @return Json
     */
    public function verifyPin(): Json
    {
        $params      = Request::only(['pin']);

        $UserService = new UserService();

        $result      = $UserService->verifyPin($params['pin']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 修改用户名
     * @return Json
     */
    public function editNickName(): Json
    {
        $params      = Request::only(['username',]);

        $UserService = new UserService();

        $result      = $UserService->editNickName($params['username']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 编辑头像
     * @return Json
     */
    public function editAvatar(): Json
    {
        $params      = Request::only(['img',]);

        $UserService = new UserService();

        $result      = $UserService->editAvatar($params['img']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 优惠券
     * @return Json
     */
    public function coupon(): Json
    {
        $UserService = new UserService();
        $result      = $UserService->coupon();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 会员二维码
     * @return Json
     */
    public function qrcode(): Json
    {
        $UserService = new UserService();

        $result      = $UserService->getQrcode();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}
