<?php
namespace app\admin\service;

use app\common\repository\ItemClassRepository;
use app\common\repository\ItemLogRepository;
use app\common\repository\ItemRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 项目
 */
class ItemService
{

    /**
     * 项目类别
     * @param $params
     * @return array
     */
    public function getItemLists($params): array
    {
        $where  = [];

        if (!empty($params['status']))
        {
            $where[] = ['status', '=', $params['status']];
        }

        if (!empty($params['title']))
        {
            $where[] = ['title', '=', $params['title']];
        }

        if (!empty($params['class_id']))
        {
            $where[] = ['class_id', '=', $params['class_id']];
        }


        $ItemRepo  = new ItemRepository();
        $data      = $ItemRepo->paginates($where);

        return Result::success($data);
    }


    /**
     * 项目信息
     * @param $id
     * @return array
     */
    public function getItemInfo($id): array
    {
        $ItemRepo = new ItemRepository();
        $data      = $ItemRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加项目
     * @param $params
     * @return array
     */
    public function addItem($params): array
    {
        $params                 = Arrays::withOut($params,['id']);

        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $ItemClassRepo          =  new ItemClassRepository();
            $class                  =  $ItemClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }

        $ItemRepo  = new ItemRepository();
        $res       = $ItemRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新项目
     * @param $params
     * @return array
     */
    public function updateItem($params): array
    {
        $time                   = Request::time();
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $ItemClassRepo          =  new ItemClassRepository();
            $class                  =  $ItemClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
        }


        $ItemRepo = new ItemRepository();

        $res       = $ItemRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除项目
     * @param $id
     * @return array
     */
    public function deleteItem($id): array
    {
        $ItemRepo = new ItemRepository();

        $res       = $ItemRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 项目
     * @param $params
     * @return array
     */
    public function getItemClassLists($params): array
    {
        $where = [];

        if (!empty($params['title']))
        {
            $where[] = ['title', '=', $params['title']];
        }

        $ItemRepo  = new ItemClassRepository();
        $data      = $ItemRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 项目分类
     * @param $id
     * @return array
     */
    public function getItemClassInfo($id): array
    {
        $ItemRepo = new ItemClassRepository();
        $data      = $ItemRepo->findById($id);
        return Result::success($data);
    }

    /**
     * 添加分类
     * @param $params
     * @return array
     */
    public function addItemClass($params): array
    {
        $params                 = Arrays::withOut($params,['id']);

        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        $ItemRepo = new ItemClassRepository();
        $res       = $ItemRepo->insert($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新分类
     * @param $params
     * @return array
     */
    public function updateItemClass($params): array
    {

        $time                   = Request::time();
        $params['update_time']  = $time;

        $ItemRepo = new ItemClassRepository();

        $res       = $ItemRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除分类
     * @param $id
     * @return array
     */
    public function deleteItemClass($id): array
    {
        $ItemRepo = new ItemClassRepository();

        $res       = $ItemRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 结算记录
     * @param array $params
     * @return array
     */
    public function getItemLogLists(array $params): array
    {

        $where  = [];

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }


        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        $ItemRepo = new ItemLogRepository();
        $data      = $ItemRepo->paginates($where,'*', $params['limit']);

        return Result::success($data);
    }

}
