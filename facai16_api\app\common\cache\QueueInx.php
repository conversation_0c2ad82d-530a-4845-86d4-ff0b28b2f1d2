<?php
namespace app\common\cache;
use think\facade\Cache;

/**
 * 队列的id
 * Class QueueInx
 * @package app\common\cache
 */
class QueueInx
{
    protected  $redis;


    public function __construct()
    {
        $this->redis =  Cache::store('redis')->handler();
    }

    /**
     * key值
     * @param string $key
     * @return string
     */
    public  function key(string $key = ''): string
    {
        return 'queue-index:' . $key;
    }

    /**
     * 设置
     * @param string $key
     * @param mixed $value
     * @param int $expires
     */
    public function set(string $key = '', $value ='', int $expires = 60)
    {
        $this->redis->set($this->key($key), $value);
        $this->redis->expire($this->key($key), $expires);
    }

    /**
     * 获取
     * @param string $key
     * @return mixed
     */
    public function get(string $key = '')
    {
        return $this->redis->get($this->key($key));
    }


    /**
     * 删除全部
     * @param string $key
     * @return mixed
     */
    public function del(string $key = '')
    {
        return $this->redis->del($this->key($key));
    }
}