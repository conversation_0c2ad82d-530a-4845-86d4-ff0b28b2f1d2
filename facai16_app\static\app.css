@charset "UTF-8";
.flex {
  display: flex; }

.fc {
  display: flex;
  align-items: center; }

.f-h {
  display: flex;
  flex-direction: column; }

.fc-h {
  display: flex;
  flex-direction: column;
  align-items: center; }

.fcc-h {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; }

.fcc {
  display: flex;
  align-items: center;
  justify-content: center; }

.fc-bet {
  display: flex;
  align-items: center;
  justify-content: space-between; }

.flex1 {
  flex: 1; }

.f-pr {
  position: relative; }

.f-pa {
  position: absolute; }

.flex-page {
  display: flex;
  flex-direction: column; }

.scroll-view {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0; }

.page-bg {
  min-height: 100vh;
  background: url(/static/bg.png) 0 0 no-repeat #F6F9FC;
  background-size: 100% auto;
  color: #222; }

button {
  box-shadow: 0 0 0 0; }

button:after, button:before {
  display: none; }

.em2:after {
  content: "　　"; }

.bottom-area {
  padding-top: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom); }

.btn-area {
  height: calc(env(safe-area-inset-bottom) - 17rpx); }

.btn-area-safe {
  height: calc(env(safe-area-inset-bottom) + 17rpx); }

.inset {
  inset: 0; }

.index-swiper swiper, .index-swiper swiper-item, .index-swiper image {
  width: 676rpx;
  height: 217rpx; }

.index-swiper .points {
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2); }

.index-swiper .point {
  width: 50rpx;
  height: 4rpx;
  border-radius: 20px; }
  .index-swiper .point.on {
    background: #fff; }

.index-tab-bar .tab {
  position: relative;
  top: 5rpx; }

.index-tab-bar .on {
  top: 0;
  font-size: 36rpx;
  color: #222222; }
  .index-tab-bar .on .line {
    height: 8rpx;
    border-radius: 15rpx;
    background: rgba(121, 73, 255, 0.3);
    position: absolute;
    bottom: 7px;
    width: 100%;
    left: 0; }
  .index-tab-bar .on text {
    font-weight: 600;
    line-height: 50rpx; }

.index-tab-bar text {
  position: relative; }

.index-box-new {
  background: linear-gradient(342deg, #FFFFFF 0%, #FEEAE2 100%, #FEEAE2 100%); }

.index-box-hot {
  background: linear-gradient(342deg, #FFFFFF 0%, #E9E2FE 99%, #FEEAE2 100%); }

.index-box-tuan {
  background: linear-gradient(342deg, #FFFFFF 0%, #FFF9DA 100%, #FEEAE2 100%); }

.index-items {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap; }

.overflow {
  overflow: hidden; }

.index-item .line-txt {
  text-decoration: line-through; }

.futura {
  font-family: "futura" !important; }

.page-bg2 {
  background: url(/static/list/listbg.png) no-repeat #F6F9FC;
  background-size: 100% auto; }

.uv-line-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all; }

.side-nav-item {
  font-weight: 400;
  font-size: 26rpx;
  color: #222222;
  line-height: 37rpx;
  text-align: right;
  font-style: normal;
  height: 58rpx;
  text-align: center;
  line-height: 58rpx; }
  .side-nav-item.on {
    background: #222;
    color: #fff;
    border-radius: 100rpx; }

.side-nav-item + .side-nav-item {
  margin-top: 25rpx; }

.fixed-view {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  z-index: 88; }

.fixed-view.bottom {
  top: auto;
  bottom: 0 !important; }

.detail-table {
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #DDDDDD; }

.comment-total {
  height: 138rpx;
  background: #FFFFFF;
  box-shadow: 0px 0px 15px 0px rgba(137, 137, 137, 0.11);
  border-radius: 12px; }
  .comment-total .num {
    padding: 0 4rpx;
    background: #F7F7FF;
    border-radius: 2rpx;
    line-height: 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #220000; }

.ddd {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all; }

.ddd2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all; }

.comment-pics {
  margin-right: -30rpx; }
  .comment-pics .num {
    width: 75rpx;
    height: 53rpx;
    line-height: 53rpx;
    text-align: right;
    padding-right: 12rpx;
    box-sizing: border-box;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%); }

.showBigPicWrap {
  position: fixed;
  z-index: 999;
  background: #000; }
  .showBigPicWrap .fixed-view {
    z-index: 1000; }
  .showBigPicWrap .tab {
    display: flex;
    flex-direction: column; }
    .showBigPicWrap .tab .line {
      width: 24rpx;
      height: 4rpx;
      background: #FFFFFF;
      border-radius: 2rpx;
      margin: 4rpx auto 0;
      opacity: 0; }
    .showBigPicWrap .tab.on .line {
      opacity: 1; }
  .showBigPicWrap .bigPicWrap {
    position: absolute;
    left: 0;
    right: 0;
    top: 100px;
    bottom: 100px; }
    .showBigPicWrap .bigPicWrap swiper, .showBigPicWrap .bigPicWrap swiper-item, .showBigPicWrap .bigPicWrap image, .showBigPicWrap .bigPicWrap video {
      width: 750rpx;
      height: calc(100vh - 200px);
      background: #000; }

.coupon-item {
  background: url(/static/quan-bg.png) no-repeat;
  background-size: 100% 100%; }

button:after, button:before {
  display: none; }

.share-list button {
  background: #fff; }

.share-pic {
  width: 600rpx;
  height: 844rpx;
  background: #FFFFFF url(/static/gujia.png) center no-repeat;
  border-radius: 12px;
  background-size: contain;
  overflow: hidden; }

.showScreenshot {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column; }

.raduis-right {
  border-radius: 0 100px 100px 0; }

.to-add-address-box {
  height: 96rpx;
  background: url(/static/lg-line.png) 0 bottom no-repeat #fff;
  background-size: 100% auto; }

.address-box {
  background: url(/static/lg-line.png) 0 bottom no-repeat #fff;
  background-size: 100% auto; }

.uv-number-box__plus, .uv-number-box__minus {
  width: 36rpx !important;
  font-size: 20rpx !important; }
  .uv-number-box__plus .uvicon, .uv-number-box__minus .uvicon {
    font-size: 20rpx !important; }

.uv-number-box__input {
  width: 68rpx !important;
  margin: 0 !important;
  background: #fff !important;
  font-size: 20rpx !important; }

.uv-number-box {
  background: #FFFFFF;
  border-radius: 8rpx;
  border: 1px solid #B5B7C9; }

.uv-number-box__input {
  border-left: 1px solid #B5B7C9;
  border-right: 1px solid #B5B7C9; }

page {
  background: #F6F9FC; }

.nodata {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
  text-align: right;
  font-style: normal;
  padding-bottom: 32rpx; }
  .nodata image {
    width: 560rpx;
    height: 360rpx;
    margin-bottom: 32rpx; }

.page-login {
  background: url(/static/bg-login.png) no-repeat #F6F9FC;
  background-size: 100% auto;
  min-height: 100vh; }

.shadow {
  box-shadow: 0px 6rpx 24rpx 0px rgba(226, 229, 239, 0.6); }

.input input {
  font-size: 44rpx;
  font-family: futura_medium; }

.placeholder {
  font-size: 34rpx !important; }

.pageso {
  background: #F6F9FC !important; }

.lg-hd {
  background: linear-gradient(90deg, #6AE88C, #14A6BA); }

.lg-bd {
  border-radius: 0 0 32rpx 32rpx;
  background: linear-gradient(180deg, rgba(155, 249, 224, 0), #62f0be); }

.r-b-32 {
  border-radius: 0 0 32rpx 32rpx; }

.car-total-bar {
  width: 536rpx;
  height: 88rpx;
  background: #FFFFFF;
  box-shadow: 0px 0px 18rpx 0px rgba(144, 146, 183, 0.1);
  border-radius: 54rpx;
  backdrop-filter: blur(10px); }

.vip {
  display: flex;
  align-items: center;
  position: relative;
  height: 39rpx;
  padding-left: 49rpx;
  padding-right: 15rpx;
  background: linear-gradient(146deg, #222222 0%, #222222 100%);
  border-radius: 100px 100px 100px 8rpx;
  font-size: 26rpx;
  color: #FBC145; }
  .vip:after {
    content: "";
    display: block;
    width: 48rpx;
    height: 45rpx;
    background: url(/static/my/vip.png) no-repeat;
    background-size: contain;
    position: absolute;
    left: -6rpx;
    bottom: 4rpx; }

.my-yj {
  background: linear-gradient(142deg, #FCCB4E 0%, #F89C26 100%);
  border-radius: 24rpx 24rpx 8rpx 8rpx; }

.my-gift {
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  height: 30rpx;
  font-size: 18rpx;
  color: #510000;
  font-weight: bold;
  background: linear-gradient(156deg, #FFE28E 0%, #FFD263 23%, #FDA730 100%);
  border-radius: 15rpx 15rpx 15rpx 0rpx; }

.link-line:after {
  content: "";
  display: block;
  left: 32rpx;
  right: 0;
  bottom: 0;
  position: absolute;
  height: 1px;
  background: #E6E7EB; }

.uv-textarea.count {
  background: #F5F9FC !important; }

.uv-textarea.bg-fff {
  background: #fff !important; }

.uv-textarea__count {
  background: rgba(0, 0, 0, 0) !important; }

::marker {
  display: none !important; }

.bg-none {
  background: rgba(0, 0, 0, 0) !important; }

.uv-avatar-group__item {
  border: 1px solid #222;
  border-radius: 50px; }

.btn-savepic {
  width: 400rpx;
  height: 96rpx;
  background: linear-gradient(142deg, #FCCB4E 0%, #F89C26 100%);
  border-radius: 48rpx;
  font-size: 32rpx;
  color: #fff; }

.label-kan {
  padding: 0 16rpx;
  height: 44rpx;
  background: linear-gradient(156deg, #FFE28E 0%, #FFD263 23%, #FDA730 100%);
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  font-size: 24rpx;
  color: #222;
  position: absolute;
  right: 0;
  top: -22rpx; }

.btn-hdgz {
  background: #fff;
  height: 60rpx;
  padding: 0 32rpx;
  border-radius: 32rpx 0 0 32rpx;
  font-size: 28rpx;
  margin-right: -13px; }

.bg-kanyidao {
  background: linear-gradient(317deg, rgba(20, 166, 186, 0.1) 0%, rgba(106, 232, 140, 0.1) 100%); }

/* 基础进度条容器样式 */
.progress {
  display: flex;
  height: 20rpx;
  font-size: 30rpx;
  background: #EDEDED;
  border-radius: 50rpx;
  position: relative; }
  .progress .label {
    width: 150rpx;
    height: 53rpx;
    background: url(/static/zp/kan-label.png) no-repeat;
    background-size: contain;
    font-size: 24rpx;
    color: #fff;
    text-align: center;
    line-height: 45rpx;
    position: absolute;
    right: 0;
    bottom: 24rpx; }

/* 进度条内部的进度条样式 */
.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: white;
  text-align: center;
  white-space: nowrap;
  border-radius: 50rpx;
  background: linear-gradient(-317deg, #FFEBAB 0%, #FFA811 100%);
  transition: width 0.6s ease; }

/* 带有动画效果的进度条类，可根据需要添加到进度条容器上 */
.progress-bar-animated {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  -webkit-background-size: 20px 20px;
  background-size: 20px 20px;
  animation: progress-bar-stripes 2s linear infinite; }

/* 动画关键帧，定义进度条条纹的移动 */
@keyframes progress-bar-stripes {
  from {
    background-position: 20px 0; }
  to {
    background-position: 0 0; } }

.btn-lg-yellow {
  background: linear-gradient(142deg, #FCCB4E 0%, #F89C26 100%); }

page {
  overscroll-behavior-y: contain; }

.btn-lg-blue {
  background: linear-gradient(142deg, #B5AFFF 0%, #887FFF 100%); }

.btn-lg-blue-op {
  background: linear-gradient(142deg, rgba(209, 205, 255, 0.9) 0%, #887fff 100%); }

.textarea-p0 {
  padding: 0 !important; }

.redbag-item + .redbag-item {
  margin-left: 25rpx; }

.btn-lg-yellow2 {
  background: linear-gradient(142deg, rgba(252, 203, 78, 0.4) 0%, rgba(248, 156, 38, 0.4) 100%); }

.hd-total-bg {
  background: linear-gradient(142deg, #fff2d4 0%, #ffd570 100%); }

.btn-lg-red {
  background: linear-gradient(142deg, #FF8D00 0%, #FF3700 100%); }

.lucky--box {
  left: 37rpx;
  top: 45rpx; }

.btn-start {
  left: 50%;
  top: 50%;
  margin-top: 10rpx;
  transform: translateX(-50%) translateY(-50%); }

.jp-about-info {
  width: 33.3333%;
  height: 33.3333%;
  background: blue;
  opacity: 0; }

.sd {
  box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(60, 30, 206, 0.06), inset 0rpx 2rpx 0rpx 0rpx #FFFFFF; }

.sw .txt {
  transform: 0.3s; }

.sw0 .txt1 {
  padding-top: 80rpx; }

.sw0 .txt2 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw0 .txt3 {
  padding-top: 80rpx; }

.sw1 .txt3 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw1 .txt2,
.sw1 .txt4 {
  padding-top: 80rpx; }

.sw2 .txt4 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw2 .txt3,
.sw2 .txt5 {
  padding-top: 80rpx; }

.sw3 .txt5 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw3 .txt4,
.sw3 .txt6 {
  padding-top: 80rpx; }

.sw4 .txt6 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw4 .txt7,
.sw4 .txt5 {
  padding-top: 80rpx; }

.sw5 .txt7 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw5 .txt6,
.sw5 .txt8 {
  padding-top: 80rpx; }

.sw6 .txt8 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw6 .txt7,
.sw6 .txt9 {
  padding-top: 80rpx; }

.sw7 .txt9 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw7 .txt0,
.sw7 .txt8 {
  padding-top: 80rpx; }

.sw8 .txt0 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw8 .txt1,
.sw8 .txt9 {
  padding-top: 80rpx; }

.sw9 .txt1 {
  font-size: 32rpx;
  color: #B99AEB;
  line-height: 44rpx;
  font-weight: 700;
  padding-top: 100rpx; }

.sw9 .txt0,
.sw9 .txt2 {
  padding-top: 80rpx; }

.jc-tips {
  background: url(/static/vip/v/war-bg.png) no-repeat;
  background-size: 100% 100%; }
