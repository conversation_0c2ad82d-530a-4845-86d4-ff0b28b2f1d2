<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="标题" required>
      <el-input v-model="form.title"  placeholder="标题"   clearable />
    </el-form-item>
    <el-form-item label="状态" required>
      <el-select v-model="form.status" placeholder="状态" clearable>
        <el-option
          v-for="item in saleStatusEnums"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="排序" required>
      <el-input v-model="form.sort"  placeholder="排序"  clearable />
    </el-form-item>
    <el-form-item
        label="项目分类"
        prop="class_id"
      >
        <el-select v-model="form.class_id" placeholder="项目分类" clearable>
          <el-option
            v-for="item in typesEnum"
            :label="item.title"
            :key="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    <el-form-item label="投资金额" required>
      <el-input v-model="form.invest" placeholder="投资金额" clearable />
    </el-form-item>
    <el-form-item label="项目总额" required>
      <el-input v-model="form.invest_scale" placeholder="项目总额" clearable />
    </el-form-item>
    <el-form-item label="限投次数" required>
      <el-input v-model="form.invest_limit" placeholder="投资限投次数"  clearable />
    </el-form-item>
    <el-form-item label="收益类型" required>
      <el-select v-model="form.profit_type" placeholder="收益类型" clearable>
        <el-option
          v-for="item in (form.class_id != 2 ? profitTypeEnums : profitTypeBEnums)"
          :label="item.label"
          :key="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="利率(%)" required>
      <el-input v-model="form.profit_rate" placeholder="每期收益率(%)" clearable />
    </el-form-item>
    <el-form-item label="收益倍数" required>
      <el-input v-model="form.profit_more" placeholder="收益倍数" clearable />
    </el-form-item>
    <el-form-item label="项目周期" required>
      <el-input v-model="form.profit_cycle" placeholder="项目周期" clearable />
    </el-form-item>
    <el-form-item label="周期时间" required>
      <el-select v-model="form.profit_cycle_time" placeholder="周期时间" clearable>
        <el-option
          v-for="item in cycleTimeEnums"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="奖励积分" required>
      <el-input v-model="form.gift_points" placeholder="兑换券" clearable />
    </el-form-item>
    <el-form-item label="奖励抽奖" required>
      <el-input v-model="form.gift_raffle"  placeholder="奖励抽奖" clearable />
    </el-form-item>
    <el-form-item label="现金奖励" required>
      <el-input v-model="form.gift_bonus" placeholder="现金奖励" clearable />
    </el-form-item>
    <el-form-item label="奖励现金券" required>
      <el-input v-model="form.gift_coupon" placeholder="奖励现金券" clearable />
    </el-form-item>
    <!-- <el-form-item label="现金券每天限制" required>
      <el-input v-model="form.coupon_limit" placeholder="现金券每天限制" clearable />
    </el-form-item> -->
    <el-form-item label="项目描述" required>
      <el-input v-model="form.desc"  placeholder="项目描述" clearable />
    </el-form-item>
    <el-form-item label="详情视频" required>
      <el-input v-model="form.video_link"  placeholder="详情视频" clearable />
    </el-form-item>
    <el-form-item label="项目进度" required>
      <el-input v-model="form.progress"  placeholder="项目进度" clearable />
    </el-form-item>
    <el-form-item label="项目进度更新周期" required>
      <el-select v-model="form.progress_cycle" placeholder="项目进度更新周期" clearable>
        <el-option
          v-for="item in cycleTimeEnums"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="项目进度更新速率" required>
      <el-input v-model="form.progress_rate" placeholder="项目进度更新速率" clearable />
    </el-form-item>
    <!-- <el-form-item label="项目进度更新时间" required>
      <el-input v-model="form.progress_time" clearable />
    </el-form-item> -->
    <el-form-item
      label="项目图片"
      prop="img"
      :rules="[{ required: true, message: '请上传图片' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="successUpload"
        :on-error="handleErr"
        :multiple="false"
      >
        <img v-if="form.img" :src="proxy.IMG_BASE_URL + form.img" width="100%" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>

    <!-- <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="height: 500px; overflow-y: hidden"
        v-model="form.content"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
      />
    </div> -->
  </el-form>
</template>

<script setup>
import { htmlDecodeByRegExp } from '@/utils/utils'
import { rolesEnums, profitTypeEnums, profitTypeBEnums, saleStatusEnums, cycleTimeEnums} from "@/config/enums";
import { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from "vue";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  img: "",
  status: "",
  sort: "",
  class_id: "",
  invest: "",
  invest_scale: "",
  invest_limit: "",
  profit_type: "",
  profit_rate: "",
  profit_more: "",
  profit_cycle: "",
  profit_cycle_time: "",
  gift_points: "",
  gift_raffle: "",
  gift_bonus: "",
  content: "",
  desc: "",
  video_link: "",
  progress: "",
  progress_cycle: "",
  progress_rate: "",
  // progress_time: "",
  deduction: "",
  gift_coupon: "",
  coupon_limit: "",
});
const props = defineProps(["item"]);

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    props.item.content = htmlDecodeByRegExp(props.item.content)
    form.value = Object.assign(form.value, props.item);

  });
  getTYpesEnum()
});

const { proxy } = getCurrentInstance()

const typesEnum = ref([])

const getTYpesEnum = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: '/Item/getItemClassLists'
    })
    if (res.code == 0) {
        typesEnum.value = res.data.data
    }
}

const successUpload = (res) => {
  form.value.img = res.data.url;
};

const handleErr = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref("<p>hello</p>");
const mode = ref("default");

const toolbarConfig = {};
const editorConfig =  {
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024, // 10M
      server: proxy.BASE_API_URL + "index/uploadX",
      headers: {
        "Accept-Token": getTokenAUTH(),
      },
      customInsert(res, insertFn) {
        const url = proxy.IMG_BASE_URL + res.data.url;
        const alt = res.data.alt
        const href = res.data.href
        insertFn(url, alt, href);
      },
    },
  },
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}

.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/deep/ .el-radio-group {
  width: 220px;
}

.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}

/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
