<template>
	<view class="daoxu">
		<view v-for="(item,index) in pages" :key="index" :class="'index' + index">
			<view>
				<navigator :url="'/' + item.path" class="p-30">
					{{item.style.navigationBarTitleText}}
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	import pagesArray from '@/pages.json'
	export default {
		data() {
			return {
				pages:[]
			};
		},
		mounted() {
			this.pages = pagesArray.pages
		
		}
	}
</script>

<style lang="scss" scoped>
	
	.index0{
		display: none!important;
	}
	.daoxu{
		display: flex;
		flex-direction: column-reverse;
		
	}
</style>