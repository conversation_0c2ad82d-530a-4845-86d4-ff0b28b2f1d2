import { createStore } from "vuex";

export default createStore({
  state: {
    breakCum: [
      {
        name: "首页",
        index: "1",
        path: "/home",
      },
    ],
    currentUser: {
      username: ''
    },
  },
  getters: {},
  mutations: {
    updateCurrentUser(state, payload) {
      state.currentUser = payload;
    },
    updateBreakCum(state, payload) {
      state.breakCum = payload;
      // set(state.breakCum, payload) // 正确的方法
    },
  },
  actions: {},
  modules: {},
});
