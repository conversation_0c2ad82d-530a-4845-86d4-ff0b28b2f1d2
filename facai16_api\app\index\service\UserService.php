<?php
namespace app\index\service;

use app\common\define\Time;
use app\common\jobs\RegisterGiftJob;
use app\common\model\MoneyClass;
use app\common\repository\CouponRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\RaffleLogRepository;
use app\common\repository\RealNameRepository;
use app\common\repository\SmsCodeRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserBankRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\repository\UserWordsRepository;
use app\common\strategy\sfz\SfzService;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use Endroid\QrCode\Exception\GenerateImageException;
use Endroid\QrCode\Exception\ValidationException;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use think\facade\Db;
use think\facade\Request;

/**
 * 用户信息
 * Class UserService
 * @package app\home\service
 */
class UserService
{
    /**
     * 集字
     * @return array
     */
    public function words(): array
    {
        $UserRepo           = new UserRepository();
        $id                 = $UserRepo->userByHeader('id');

        $UserWordsRepo      = new UserWordsRepository();
        $data               = $UserWordsRepo->findByCondition(['uid' => $id]);

        return  Result::success($data);
    }

    /**
     * 编辑头像
     * @param $avatar
     * @return array
     */
    public function editAvatar($avatar): array
    {
        $UserRepo           = new UserRepository();
        $id                 = $UserRepo->userByHeader('id');

        $update = [
            'avatar'        => $avatar,
            'update_time'   => Request::time()
        ];

        $res                = $UserRepo->updateById($id, $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return  Result::success();
    }


    /**
     * 等级信息
     * @return array
     */
    public function level(): array
    {
        $LevelRepo      = new LevelRepository();
        $UserRepo       = new UserRepository();

        $user           = $UserRepo->userByHeader();
        $data           = $LevelRepo->findById($user['level']);

        return Result::success($data);
    }

    /**
     * 实名认证
     * @param string $name
     * @param string $idcard
     * @param string $bank
     * @param string $address
     * @param string $branch
     * @return array
     */
    public function realName(string $name, string $idcard, string $bank, string $address, string $branch): array
    {

        $UserRepo       = new UserRepository();
        $SfzService     = new SfzService();

        $user           = $UserRepo->userByHeader();


        $where          = [];
        $where[]        = ['sfz_name', '=', $name];
        $where[]        = ['sfz_number', '=', $idcard];

        $isUser         = $UserRepo->findByCondition($where);


        //身份证已被使用
        if($isUser)
        {
            return  Result::fail('身份证已被使用');
        }


        $SystemSetRepo = new SystemSetRepository();

        $status        = $SystemSetRepo->valueByCondition(['key' => 'real_name_status'],'val');

        if ($status)
        {
            //调用接口
            $data = [
                'idcard'    => $idcard,
                'name'      => $name,
            ];

            $result     = $SfzService->send($data);

            if ($result['code'])
            {
                return  Result::fail($result['msg']);
            }


            Db::startTrans();

            try {

                $update = [
                    'sfz_name'   => $name,
                    'sfz_number' => $idcard,
                ];


                $res = $UserRepo->updateById($user['id'], $update);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }

                $UserStateRepo = new UserStateRepository();
                $isTest        = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');


                $RealNameRepo = new RealNameRepository();

                $insert = [
                    'uid'           => $user['id'],
                    'username'      => $user['username'],
                    'phone'         => $user['phone'],
                    'is_test'       => $isTest,
                    'sfz_name'      => $name,
                    'sfz_number'    => $idcard,
                    'create_time'   => Request::time(),
                    'update_time'   => Request::time(),
                    'bank_name'     => $branch,
                    'bank_branch'   => $address,
                    'bank_account'  => $bank,
                    'status'        => 0
                ];

                $res = $RealNameRepo->inserts($insert);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }

                $update = [
                    'sfz_status'    => 0,
                    'update_time'   => Request::time(),
                ];

                $res = $UserStateRepo->updateByCondition(['uid' => $user['id']], $update);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }

                Db::commit();

                queue(RegisterGiftJob::class, ['uid' => $user['id']]);

                return Result::success();

            }catch (\Exception $e)
            {
                // 回滚事务
                Db::rollback();

                Record::exception('index', $e,'UserService->realName');

                return Result::success();
            }
        }
        else
        {
            Db::startTrans();

            try {

                $update = [
                    'sfz_name'   => $name,
                    'sfz_number' => $idcard,
                ];

                $res = $UserRepo->updateById($user['id'], $update);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }

                $UserStateRepo = new UserStateRepository();
                $isTest        = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

                $RealNameRepo  = new RealNameRepository();

                $insert = [
                    'uid'           => $user['id'],
                    'username'      => $user['username'],
                    'phone'         => $user['phone'],
                    'is_test'       => $isTest,
                    'sfz_name'      => $name,
                    'sfz_number'    => $idcard,
                    'create_time'   => Request::time(),
                    'update_time'   => Request::time(),
                    'bank_name'     => $branch,
                    'bank_branch'   => $address,
                    'bank_account'  => $bank,
                ];

                $res = $RealNameRepo->inserts($insert);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }

                $update = [
                    'sfz_status'    => 2,
                    'update_time'   => Request::time(),
                ];

                $res = $UserStateRepo->updateByCondition(['uid' => $user['id']], $update);

                if (!$res)
                {
                    Db::rollback();
                    return  Result::fail('保存失败');
                }


                Db::commit();

                return Result::success();

            }catch (\Exception $e)
            {
                // 回滚事务
                Db::rollback();

                Record::exception('index', $e,'UserService->realName');

                return Result::success();
            }
        }

    }

    /**
     * 用户信息
     * @return array
     */
    public function info(): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader('id,username,nickname,avatar,phone,sfz_name,sfz_number,level,level_name,invite,level_team,level_team_name');

        $MoneyRepo      = new MoneyRepository();
        $money          = $MoneyRepo->findByCondition(['uid' => $user['id']]);

        $UserInfoRepo   = new UserInfoRepository();
        $userInfo       = $UserInfoRepo->findByCondition(['uid' => $user['id']]);

        $info = [
            //可提余额
            'money'             => $money['money'],
            //冻结余额
            'frozen_money'      => $money['frozen_money'],
            //总余额
            'total_money'       => $money['money'] + $money['frozen_money'],
            //累积收益
            'income_money'      => $userInfo['income_money'],
            //待收收益
            'invest_not_finish' => $userInfo['invest_not_finish'] + $userInfo['invest_not_earn'],
            //余额宝收益
            'yuebao_earn'       => $userInfo['yuebao_earn'],
            //手机
            'username'          => $user['username'],
            //昵称
            'nickname'          => $user['nickname'],
            //头像
            'avatar'            => Uri::file($user['avatar']),
            //手机号
            'phone'             => $user['phone'],
            //手机号
            'phone2'            => substr($user['phone'],0,3).'****'.substr($user['phone'],strlen($user['phone'])-4,4),
            //姓名
            'sfz_name'          => $user['sfz_name'],
            //身份证号
            'sfz_number'        => $user['sfz_number'],
            //身份证号
            'level'             => $user['level'],
            //身份证号
            'level_name'        => $user['level_name'],
             //出勤天数
            'signin_num'        => $userInfo['signin_num'],
             //用户优惠券
            'user_points'       => $userInfo['user_points'],
            //邀请码
            'invite'            => $user['invite'],
            //团长等级
            'level_team'        => $user['level_team'],
            //团长等级名称
            'level_team_name'   => $user['level_team_name'],
        ];

        return Result::success($info);
    }

    /**
     * 用户详情
     * @return array
     */
    public function detail(): array
    {
        $UserRepo       = new UserRepository();
        $data           = $UserRepo->userInfoByHeader();
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader('id,username,nickname,avatar,phone,sfz_name,sfz_number,level,level_name,invite,level_team,level_team_name');

        $today          = strtotime(date('Y-m-d 00:00:00'));
        $RaffleLogRepo  = new RaffleLogRepository();

        $where   = [];
        $where[] = ['uid', '=', $data['uid']];
        $where[] = ['create_time', '>=', $today];

        $count   = $RaffleLogRepo->countByCondition($where);

        if ($user['level'] > 0)
        {
            if ($count > 2)
            {
                $data['raffle_num'] = 0;
            }
            else
            {
                $data['raffle_num'] = 2 - $count;
            }

        }
        else
        {
            if ($count > 0)
            {
                $data['raffle_num'] = 0;
            }
            else
            {
                $data['raffle_num'] = 1;
            }
        }



        return Result::success($data);
    }


    /**
     * 用户状态
     * @return array
     */
    public function state(): array
    {
        $UserRepo       = new UserRepository();
        $data           = $UserRepo->userStateByHeader();



        return Result::success($data);
    }



    /**
     * 资金管理
     * @return array
     */
    public function fund(): array
    {
        $UserRepo           = new UserRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $uid                = $UserRepo->userByHeader('id');

        $data               = $MoneyLogRepo->paginates(['uid' => $uid]);

        return  Result::success($data);
    }

    /**
     * 修改密码
     * @param $password
     * @param $confirmPassword
     * @param $code
     * @return array
     */
    public function editPass($password, $confirmPassword, $code): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
//        $SmsCodeRepo        = new SmsCodeRepository();

//        $where       = [];
//        $where[]     = ['phone', '=', $user['phone']];
//        $where[]     = ['code', '=', $code];
//        $smsCode     = $SmsCodeRepo->findByCondition($where,'*',['id'=>'desc']);
//
//        //验证码已过期，请重新发送验证码
//        if ($code != '888999')
//        {
//            //请输入正确的验证码
//            if(empty($smsCode))
//            {
//                return Result::fail('请输入正确的验证码');
//            }
//
//            if($smsCode['create_time'] < (time() - Time::FIVE_MINUTE))
//            {
//                return Result::fail('验证码已过期，请重新发送验证码');
//            }
//        }

        if ($confirmPassword != $password)
        {
            return Result::fail('验证密码不一致');
        }

        //请输入6~15位密码
        if(strlen($password) < 6 || strlen($password) > 15)
        {
            return Result::fail('请输入6~15位密码');
        }

        $update = [
            'password'      => $password,
            'update_time'   => time(),
        ];

        $res = $UserRepo->updateById($user['id'], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }


    /**
     * 修改资金密码
     * @param $password
     * @param $confirmPassword
     * @param $code
     * @return array
     */
    public function editPin($password, $confirmPassword, $code):array
    {
        $UserRepo    = new UserRepository();
        $user        = $UserRepo->userByHeader();

//        $SmsCodeRepo = new SmsCodeRepository();
//
//        $where       = [];
//        $where[]     = ['phone', '=', $user['phone']];
//        $where[]     = ['code', '=', $code];
//        $smsCode     = $SmsCodeRepo->findByCondition($where,'*',['id'=>'desc']);
//
//        //验证码已过期，请重新发送验证码
//        if ($code != '888999')
//        {
//            //请输入正确的验证码
//            if(empty($smsCode))
//            {
//                return Result::fail('请输入正确的验证码');
//            }
//
//            if($smsCode['create_time'] < (time() - Time::FIVE_MINUTE))
//            {
//                return Result::fail('验证码已过期，请重新发送验证码');
//            }
//        }


        //请输入6位数字密码
        if(strlen($password) != 6)
        {
            return Result::fail('请输入6位数字密码');
        }

        if ($confirmPassword != $password)
        {
            return Result::fail('验证密码不一致');
        }

        $update = [
            'pin'           => $password,
            'update_time'   => Request::Time(),
        ];


        $res = $UserRepo->updateById($user['id'], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }



    /**
     * 判断安全密码
     * @param string $password
     * @return array
     */
    public function verifyPin(string $password): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        //安全密码错误
        if($user['pin'] != $password)
        {
            return Result::fail('安全密码错误');
        }

        return Result::success();
    }


    /**
     * 修改用户名
     */
    public function editNickName(string $username): array
    {
        $UserRepo           = new UserRepository();
        $id                 = $UserRepo->userByHeader('id');


        $update = [
            'nickname'      => $username,
            'update_time'   => Request::time()
        ];

        $res                = $UserRepo->updateById($id, $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return  Result::success();
    }

    /**
     * 现金券
     * @return array
     */

    public function coupon(): array
    {
        $UserRepo             = new UserRepository();
        $user                 = $UserRepo->userByHeader();

        $CouponRepo           = new CouponRepository();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $data       = $CouponRepo->paginates($where);

        foreach ($data['data'] as &$item)
        {
            $item['expire_time'] = date('Y-m-d H:i:s',$item['expire_time']);
        }

        return  Result::success($data);
    }


    /**
     * 二维码
     * @return array
     * @throws GenerateImageException
     * @throws ValidationException
     */
    public function getQrcode(): array
    {

        $UserRepo             = new UserRepository();
        $user                 = $UserRepo->userByHeader();

        $SystemSetRepo = new SystemSetRepository();

        $url           = $SystemSetRepo->valueByCondition(['key' => 'yaoqing_url'],'val');

        // 生成带域名的注册链接（关键修改点）
        $data = $url . '#/pages/login/reg?inviteCode=' . $user['invite'];

        // 创建二维码对象（4.x 写法）
        $qrCode = new QrCode($data);
        $qrCode->setSize(300);
        $qrCode->setMargin(10);

        // 使用PngWriter输出（4.x 正确用法）
        $writer  = new PngWriter();
        $pngData = $writer->writeString($qrCode);
        // 关键修改：用 writeString() 而非 write()
        // 或者返回Base64
        $base64 = 'data:image/png;base64,' . base64_encode($pngData);

        return  Result::success(['qrcode' => $base64,'url' => $data]);
    }
}
