<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * SiHaiPay
 */
class SiHaiPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'SiHaiPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {

        $param = [
            'app_id'        => $this->config['app_id'],
            'product_id'    => $data['channel'],
            'out_trade_no'  => $data['orderNo'],
            'amount'        => $data['money'],
            'time'          => time(),
            'notify_url'    => CompleteRequest('/api/callback/' . self::CHANNEL),
        ];


        $param['sign']  = $this->signature($param);

        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $uri            = '';

        if(isset($result['code'])  && $result['code'] == 200)
        {
            $uri = $result['data']['url'] ?? '';
        }

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"trade_no":"9042117002780122","product_id":"1","app_id":"79624103f4f2e8c411679494","out_trade_no":"PAYVBHEFCBAIHHCDBG","trade_status":"1","amount":"500.00","real_amount":"500.00","desc":"","time":"1745211027","sign":"e65e57542c3eb13562f996d559016f46"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];
        $param     = array_filter($param);

        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['out_trade_no'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return ($sign);
    }

}