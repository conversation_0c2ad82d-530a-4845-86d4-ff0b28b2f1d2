<template>
  <adminTable
    :hideSeachButton="true"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editItem('add', {})"
        v-permission
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="标题" width="130" />
      <el-table-column prop="money" label="金额" width="130" />
      <el-table-column prop="words" label="集字" width="130" />
      <el-table-column label="类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.type, lottoryTypeBEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="chance" label="概率" width="130" />
      <el-table-column prop="rate" label="加息" width="130" />
      <el-table-column prop="img" label="图片" width="200">
        <template #default="scope">
          {{ scope.row.img ? "" : "--" }}
          <el-image
            v-if="scope.row.img"
            class="previewImg"
            :preview-teleported="true"
            :src="proxy.IMG_BASE_URL + scope.row.img"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[proxy.IMG_BASE_URL + scope.row.img]"
            show-progress
            :initial-index="0"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
      <el-table-column
        label="操作"
        fixed="right"
        width="200"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editItem('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="编辑/新增" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import editPop from "./components/lottory/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getLabelByVal, lottoryTypeBEnums } from "@/config/enums";

const dialogFlag = ref(false);
const editFormRef = ref(false);
const editRow = ref({});
const formType = ref("add");

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Raffle/getRaffleLists",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};

const editItem = (type, row) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url =
    formType.value == "add" ? "Raffle/addRaffle" : "Raffle/updateRaffle";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      title: data.title,
      money: data.money,
      words: data.words,
      type: data.type,
      chance: data.chance,
      rate: data.rate,
      img: data.img,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "Raffle/deleteRaffle?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};
</script>

<style lang="less" scoped></style>
