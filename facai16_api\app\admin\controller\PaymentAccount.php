<?php
namespace app\admin\controller;


use app\admin\service\PaymentAccountService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 支付账号
 */
class PaymentAccount
{
    /**
     * 获取支付账号
     * @return mixed
     */
    public function getPaymentAccountLists(): Json
    {
        $PaymentService = new PaymentAccountService();
        $data           = $PaymentService->getPaymentAccountLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取支付账号信息
     * @return Json
     */
    public function getPaymentAccountInfo(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentAccountService();
        $data           = $PaymentService->getPaymentAccountInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加支付账号信息
     * @return Json
     */
    public function addPaymentAccount(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentAccountService();
        $data           = $PaymentService->addPaymentAccount($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 编辑支付账号
     * @return Json
     */
    public function updatePaymentAccount(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentAccountService();
        $data           = $PaymentService->updatePaymentAccount($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除支付账号
     * @return Json
     */
    public function deletePaymentAccount(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentAccountService();
        $data           = $PaymentService->deletePaymentAccount($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}