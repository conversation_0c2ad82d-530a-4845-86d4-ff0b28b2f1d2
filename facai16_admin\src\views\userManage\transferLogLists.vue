<template>
  <adminTable
    :hideResetButton="true"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >

    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
    </template>
   
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="转账人ID" width="100" />
      <el-table-column prop="username" label="转账人" width="160" />
      <el-table-column prop="phone" label="转账人手机号" width="100" />
      <el-table-column prop="to_uid" label="接收人ID" width="160" />
      <el-table-column prop="to_username" label="接收人" width="200" />
      <el-table-column prop="to_phone" label="接收人手机号" width="200" />
      <el-table-column prop="money" label="金额" width="200" />
      <el-table-column prop="status" label="状态" width="200" >
        <template  #default="scope">
          {{ getLabelByVal(scope.row.status, transferStatusEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { transferStatusEnums, getLabelByVal } from "../../config/enums";
import { useRoute } from 'vue-router'

const route = useRoute()

const searchForm = ref({
  phone: ''
});

onMounted(() => {
  if (route.query && route.query.phone) {
    searchForm.value.phone = route.query.phone
  }
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);
const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Transfer/getTransferLists",
    params: {
      ...searchForm.value,
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
