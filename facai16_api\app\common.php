<?php

use think\facade\Request;

if (!function_exists('__'))
{
    /**
     * 语言
     * @param string $string
     * @param array $replace
     * @return string
     */
    function __(string $string, array $replace = []): string
    {
        $lang = lang(strtolower($string));

        foreach ($replace as $key => $val)
        {
            $lang = str_replace('{'  . $key . '}', $val, $lang);
        }

        return $lang;
    }

}


if (!function_exists('CompleteRequest'))
{
    /**
     * 语言
     * @param string $url
     * @return string
     */
    function CompleteRequest(string $url = ''): string
    {

        if (empty($url)) {
            return '';
        }

        if (Request::port() == 80 || Request::port() == 443)
        {
            return  Request::domain(true) . $url;
        }
        else
        {
            return Request::domain(true)  . ':' . Request::port() . $url;
        }
    }
}


function curlPost($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $output = curl_exec($ch);
    curl_close($ch);
    return $output;
}


function curl_post($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    // POST数据
    curl_setopt($ch, CURLOPT_POST, 1);

    // 把post的变量加上
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    // 设置请求头，指定 Content-Type 为 JSON
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',  // 请求类型为 JSON
        'Accept: application/json'  // 告诉服务器我们期望接收 JSON 响应
    ));

    $output = curl_exec($ch);

    curl_close($ch);
    return $output;
}
function httpPost($url, $paramStr){
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => $paramStr,
        CURLOPT_HTTPHEADER => array(
            "cache-control: no-cache",
            "content-type: application/x-www-form-urlencoded"
        ),
    ));
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    if ($err) {
        return $err;
    }
    return $response;
}