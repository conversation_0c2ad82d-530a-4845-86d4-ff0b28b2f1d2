<?php
namespace app\admin\controller;

use app\admin\service\UserLoginService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 登入日志
 */
class UserLogin
{

    /**
     * 登入日志
     * @return Json
     */
    public function getUserLoginLists(): Json
    {
        $UserLoginService  = new UserLoginService();
        $data              = $UserLoginService->getUserLoginLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取登入信息
     * @return Json
     */
    public function getUserLoginInfo(): Json
    {
        $id               = Request::param('id',0);
        $UserLoginService = new UserLoginService();
        $data             = $UserLoginService->getUserLoginInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加用户登入
     * @return Json
     */
    public function addUserLogin(): Json
    {
        $param            = Request::param();
        $UserLoginService = new UserLoginService();
        $data             = $UserLoginService->addUserLogin($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}