<template>
	<view class="page-bg flex flex-column">
		<uv-navbar bgColor="none" @leftClick="back" placeholder>
			<template #left>
				<view>
					<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
				</view>
			</template>
			<template #center>
				<view class="color-#fff">
					公司资质
				</view>
			</template>
		</uv-navbar>

		<view style="height: calc(100vh - 60px); margin-top: 60px;">
			<web-view :src="pdfUrl"></web-view>
		</view>

	</view>
</template>

<script>
	export default {

		data() {
			return {
				pdfUrl: "/hybrid/html/web/viewer.html?file=" +
					encodeURIComponent(
						"/static/nice-company.pdf"
					)
			};
		},
		mounted() {

		},

		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/about/aboutimg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>

<style>
	iframe {
		position: relative;
		padding-top: 50px !important;
		height: calc(100vh - 50px) !important;
	}
</style>