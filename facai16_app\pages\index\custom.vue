<template>
	<view class="page-bg flex flex-column">
		<uv-navbar bgColor="none" @leftClick="back" placeholder>
			<template #left>
				<view>
					<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
				</view>
			</template>
			<template #center>
				<view class="color-#fff">
					我的客服
				</view>
			</template>
		</uv-navbar>
		<view style="flex-1">
			<web-view :src="webUrl"></web-view>
		</view>

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				pdfUrl: "/hybrid/html/web/viewer.html?file=" +
					encodeURIComponent(
						"/static/nice-company.pdf"
					)
			};
		},
		computed: {
			...mapState(['userInfo', 'customUrl']),
			webUrl() {
				return `${this.customUrl}?channelId=qK8Dm5&customer={"名称":"' ${this.userInfo.nickname} '","手机":"' ${this.userInfo.phone} '"}'`
			}
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/about/aboutimg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>

<style>
	iframe {
		padding-top: 50px !important;
		height: calc(100vh - 50px) !important;
	}
</style>