<?php
namespace app\common\cache;

use think\facade\Cache;


/**
 * 集合数据
 * Class QueueSet
 * @package app\common\cache
 */
class QueueSet
{
    protected  $redis;

    public function __construct()
    {
        $this->redis =  Cache::store('redis')->handler();
    }

    /**
     * key值
     * @param string $key
     * @return string
     */
    public  function key(string $key = ''): string
    {
        return 'queue:' . $key;
    }


    /**
     * 添加数据
     */
    public  function sAdd(string $key = '', $value = ''): bool
    {
        return $this->redis->sAdd($this->key($key), $value);
    }


    /**
     * 查询数据是否在集合中
     */
    public function sIsMember(string $key = '', $value = ''): bool
    {
        return $this->redis->sIsMember($this->key($key), $value);
    }



    /**
     * 删除集合内某个元素
     */
    public function sRem(string $key = '', $value = ''): bool
    {
        return $this->redis->sRem($this->key($key), $value);
    }

    /**
     * 删除全部
     * @param string $key
     * @return mixed
     */
    public function del(string $key = '')
    {
        return $this->redis->del($this->key($key));
    }


    /**
     * 获取集合所有数据
     * @param string $key
     * @return mixed
     */
    public function sMembers(string $key = '')
    {
        return $this->redis->sMembers($this->key($key));
    }

    /**
     * 判断key是否存在
     * @param string $key
     * @return void
     */
    public function exists(string $key = '')
    {
        $res = $this->redis->exists($this->key($key));

        if (empty($res))
        {
            $this->sAdd($key,0);
        }

    }

    /**
     * 删除其他的key
     * @param string $key
     * @param $time
     */
    public function delOtherKey(string $key, $time)
    {
        // 使用通配符来匹配键
        $pattern = $this->key($key) . ':*';

        // 获取匹配模式的所有键
        $keys    = $this->redis->keys($pattern);

        // 过滤掉不需要删除的键
        $keysToDel = array_filter($keys, function($item) use($key,$time)
        {
            return $item !==  $this->key($key) . ':' . $time;
        });

        // 删除剩余的键
        foreach ($keysToDel as $key)
        {
            $this->redis->del($key);
        }
    }
}