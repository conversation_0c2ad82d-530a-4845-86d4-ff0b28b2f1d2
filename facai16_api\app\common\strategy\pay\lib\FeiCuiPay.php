<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * FeiCuiPay
 */
class FeiCuiPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'FeiCuiPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {

        $param = [
            'method'                => 'placeOrder',
            'timestamp'             => date('Y-m-d H:i:s', time()),
            'memberId'              => $this->config['mch_id'],
            'callerOrderId'         => $data['orderNo'],
            'amount'                => $data['money'] *  100,
            'channelCode'           => $data['channel'],
            'merchantCallbackUrl'   => CompleteRequest('/api/callback/' . self::CHANNEL)
        ];

        $param['sign']  = $this->signature($param);

        $result         = curl_post($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $message        = $result['data']['message'] ?? '';

        $uri            = $message['url'] ?? '';

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"memberId":"589255484","orderId":"C001743864599113242950005731","callerOrderId":"PAYVBHEDIGEFJIHCDF","orderStatus":"AP","orderAmount":"50000","actualAmount":"50000","sign":"25c906852de9acfd1059658dcc4e0a25"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];

        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['callerOrderId'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'OK';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        $params = array_filter($params);
        //参数排序
        ksort($params);

        $md5str = '';

        foreach ($params as $key => $val)
        {
            $md5str = $md5str . $key .  $val;
        }

        $md5str = $this->config['key'].$md5str.$this->config['key'];
        //获取sign
        return md5($md5str);
    }

}