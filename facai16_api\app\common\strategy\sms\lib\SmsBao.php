<?php

namespace app\common\strategy\sms\lib;

use app\common\strategy\sms\SmsInterface;
use app\common\utils\Result;
use think\facade\Config;

/**
 * 短信宝
 * Class SmsBao
 * @package app\common\strategy\sms
 */
class SmsBao implements SmsInterface
{

    /**
     * 发动短信
     * @param $phone
     * @param $smscode
     * @return array
     */
    public function send($phone, $smscode): array
    {
        $config    = Config::get('serve_sms.strategy.SmsBao');

        $statusStr = [
            "0"  => "短信发送成功",
            "-1" => "参数不全",
            "-2" => "服务器空间不支持,请确认支持curl或者fsocket，联系您的空间商解决或者更换空间！",
            "30" => "密码错误",
            "40" => "账号不存在",
            "41" => "余额不足",
            "42" => "帐户已过期",
            "43" => "IP地址限制",
            "50" => "内容含有敏感词"
        ];

        //地址
        $smsapi  = $config['url'];

        //短信平台帐号
        $user    = $config['account'];

        //短信平台密码
        $pass    = $config['password'];

        //要发送的短信内容  $content ="您的验证码是".$smscode.'。如非本人操作，请忽略本短信';
        $content = __('verification_code_operated_please_ignore_message', ['code' => $smscode]);

        $sendurl = $smsapi . "sms?u=" . $user . "&p=" . $pass . "&m=" . $phone . "&c=".urlencode($content);

        $result  = file_get_contents($sendurl) ;


        $return = [
            'code' => $result,
            'info' => $statusStr[$result] ?? '',
        ];

        return Result::success($return);
    }
}
