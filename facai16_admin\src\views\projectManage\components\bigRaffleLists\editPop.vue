<template>
    <el-form
      label-width="100px"
      :inline="true"
      :model="form"
      class="demo-form-inline"
    >
      <el-form-item label="标题" required>
        <el-input v-model="form.title" clearable />
      </el-form-item>
  
      <el-form-item label="金额" required>
        <el-input v-model="form.money" clearable />
      </el-form-item>
      <el-form-item label="概率" required>
        <el-input v-model="form.chance" clearable />
      </el-form-item>
      <el-form-item
        label="奖品图片"
        prop="img"
        :rules="[{ required: true, message: '请上传图片' }]"
      >
        <el-upload
          class="upload-demo"
          style="width: 114px"
          :show-file-list="false"
          drag
          :headers="headers"
          :action="`${proxy.BASE_API_URL}index/upload`"
          :on-success="successUpload"
          :on-error="handleErr"
          :multiple="false"
        >
         <img width="100" v-if="form.img" :src="proxy.IMG_BASE_URL + form.img" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
        ></el-upload>
      </el-form-item>
    </el-form>
  </template>
  
  <script setup>
  import { getCurrentInstance, nextTick, onMounted, ref } from "vue";
  import { rolesEnums } from "@/config/enums";
  import { getTokenAUTH } from "@/utils/auth";
  
  const form = ref({
    title: "",
    money: "",
    chance: "",
    img: "",
  });
  const props = defineProps(["item"]);
  
  const { proxy } = getCurrentInstance()
  
  const headers = ref({})
  onMounted(() => {
    headers.value['Accept-Token'] = getTokenAUTH()
    nextTick(() => {
      form.value = Object.assign(form, props.item);
    });
  });
  
  const successUpload = (res) => {
    form.value.img = res.data.url;
  };
  
  const handleErr = (err) => {
    if (err.status == 320) {
      form.value.img = JSON.parse(err.message).data.url;
    }
  }
  
  defineExpose({ form });
  </script>
  
  <style lang="less" scoped>
  .demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
  }
  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }
  
  .demo-form-inline .el-select {
    --el-select-width: 220px;
  }
  /deep/ .el-radio-group {
    width: 220px;
  }
  .form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
  }
  /deep/ .el-form-item {
    align-items: flex-start;
  }
  </style>
  