<?php
namespace app\admin\controller;

use app\admin\service\IndexService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\facade\Validate;
use think\response\Json;

class Index
{
    public function index(): string
    {
        return 'index';
    }

    /**
     * 统计数据
     * @return Json
     */
    public function welcome(): Json
    {
        $start        = Request::param('start', '');
        $end          = Request::param('end', '');
        $isTest       = Request::param('is_test', '');
        $phone        = Request::param('phone', '');
        $lv           = Request::param('lv', '');


        $IndexService   = new IndexService();
        $data           = $IndexService->welcome($start, $end, $isTest, $phone, $lv);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 配置接口
     * @return Json
     */
    public function setting(): J<PERSON>
    {
        $key            = Request::param('key', '');
        $IndexService   = new IndexService();
        $data           = $IndexService->setting($key);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 上传
     * @return Json
     */
    public function upload(): Json
    {
        $file    = Request::file('file');

        //上传图片文件
        $validate = Validate::rule([
            'image' => 'file|fileExt:jpg,png,gif,heic,heif,jpeg|fileSize:10485760'
        ]);

        //得到上传文件和规则比对
        $result = $validate->check(['image' => $file,]);

        if (!$result)
        {
            return Ajax::fail('上传失败');
        }

        $IndexService   = new IndexService();
        $data           = $IndexService->upload($file);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }



    /**
     * 上传x
     * @return Json
     */
    public function uploadX(): Json
    {
        $file    = Request::file('file');

        //上传图片文件
        $validate = Validate::rule([
            'image' => 'file|fileExt:jpg,png,gif|fileSize:10485760'
        ]);

        //得到上传文件和规则比对
        $result = $validate->check(['image' => $file,]);

        if (!$result)
        {
            return Ajax::fail('上传失败');
        }

        $IndexService   = new IndexService();
        $data           = $IndexService->uploadX($file);

        return Ajax::errorNo($data['data']['url'], '','');
    }

    /**
     *  谷歌二维码
     * @return Json
     */
    public function googleQrcode(): Json
    {
        $IndexService   = new IndexService();
        $data           = $IndexService->googleQrcode();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 谷歌修改
     * @return Json
     */
    public function googleEdit(): Json
    {
        $IndexService   = new IndexService();
        $data           = $IndexService->googleEdit();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 系统用户
     * @return Json
     */
    public function systemUser(): Json
    {
        $IndexService   = new IndexService();
        $data           = $IndexService->systemUser();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 提示信息
     * @return Json
     */
    public function notice(): Json
    {
        $IndexService   = new IndexService();
        $data           = $IndexService->notice();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }


    public function onlineUser(): Json
    {
        $IndexService   = new IndexService();
        $data           = $IndexService->onlineUser();

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }
}