<?php
namespace app\admin\service;


use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 用户等级
 */
class LevelService
{
    /**
     * 用户等级列表
     * @return array
     */
    public function getLevelLists(): array
    {
        $LevelRepo = new LevelRepository();
        $data      = $LevelRepo->paginates([],'*',10,['id'=> 'desc']);

        return Result::success($data);
    }


    /**
     * 获取信息
     * @param $id
     * @return array
     */
    public function getLevelInfo($id): array
    {
        $LevelRepo = new LevelRepository();
        $data      = $LevelRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加
     * @param $params
     * @return array
     */
    public function addLevel($params): array
    {
        $LevelRepo = new LevelRepository();
        $res       = $LevelRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 编辑
     * @param $params
     * @return array
     */
    public function updateLevel($params): array
    {
        $LevelRepo = new LevelRepository();

        $res       = $LevelRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除
     * @param $id
     * @return array
     */
    public function deleteLevel($id): array
    {
        $LevelRepo = new LevelRepository();

        $res       = $LevelRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户升级记录
     * @return array
     */
    public function getLevelLogLists(): array
    {
        $LevelRepo = new LevelLogRepository();
        $data      = $LevelRepo->paginates([],'*',10,['id'=> 'desc']);

        return Result::success($data);
    }


}
