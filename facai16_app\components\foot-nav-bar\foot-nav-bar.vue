<template>
	<view>
		<view class="footer-nav-height h-108" v-if="fixed"></view>
		<view class="btn-area" v-if="fixed"></view>
		<view class="footer-nav bg-#fff" :class="{fixed:fixed}">

			<view class="fc-bet t-20 lh-24 color-#999999">
				<view class="w-[25%] h-108 fcc-h" :class="{on:index  == 0}" @click="openUrl('/pages/index/index')">
					<view class="icon fcc mb-10">
						<image src="/static/index/foot-nav1-h.png" mode="aspectFit" block size-44 v-if="index == 0">
						</image>
						<image src="/static/index/foot-nav1.png" mode="aspectFit" block size-44 v-else></image>
					</view>
					<view class="p">
						首页
					</view>
				</view>
				<view class="w-[25%] h-108 fcc-h" :class="{on:index  == 1}" @click="openUrl('/pages/project/project')">
					<view class="icon fcc mb-10">
						<image src="/static/index/foot-nav2-h.png" mode="aspectFit" block size-44 v-if="index == 1">
						</image>
						<image src="/static/index/foot-nav2.png" mode="aspectFit" block size-44 v-else></image>
					</view>
					<view class="p">
						股权
					</view>
				</view>

				<view class="w-[25%] h-108 fcc-h" :class="{on:index  == 2}"
					@click="openUrl('/pages/shareholding/shareholding')">
					<view class="icon fcc mb-10">
						<image src="/static/index/foot-nav3-h.png" mode="aspectFit" block size-44 v-if="index == 2">
						</image>
						<image src="/static/index/foot-nav3.png" mode="aspectFit" block size-44 v-else></image>
					</view>
					<view class="p">
						投资
					</view>
				</view>
				<view class="w-[25%] h-108 fcc-h" :class="{on:index  == 3}" @click="openUrl('/pages/my/my')">
					<view class="icon fcc mb-10">
						<image src="/static/index/foot-nav4-h.png" mode="aspectFit" block size-44 v-if="index == 3">
						</image>
						<image src="/static/index/foot-nav4.png" mode="aspectFit" block size-44 v-else></image>
					</view>
					<view class="p">
						我的
					</view>
				</view>
			</view>

			<view class="btn-area"></view>
		</view>

	</view>
</template>

<script>
	export default {
		name: "foot-nav-bar",
		props: {
			index: {
				type: [String, Number],
				default: 0
			},
			fixed: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {

			};
		},
		methods: {
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.footer-nav {

		// border-radius: 12rpx 12rpx 0 0 ;
		box-shadow: 0px -6rpx 12rpx 0px rgba(240, 243, 248, 0.6);


		.on .p {
			color: #4A78FF;
		}


	}

	.logo {
		position: absolute;
		top: -38rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	.fixed {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 88;
	}
</style>