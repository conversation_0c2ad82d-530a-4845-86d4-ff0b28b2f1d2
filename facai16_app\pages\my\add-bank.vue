<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page scroll-view page-bg">
				<view class="flex-scroll-fixed bg-#407CEE bg-op-40  backdrop-blur-[29rpx] z-20">

					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								添加银行卡
							</view>
						</template>
					</uv-navbar>
				</view>



				<view class="flex1  color f-pr">
					<scroll-view scroll-y="true" class="scroll-view ">
						<view class="address-input-row  bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-20 flex">
							<view class="fc left color-#fff">
								姓名
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="请填写姓名" :disabled="true" v-model="userInfo.sfz_name"
									placeholder-style="color:#fff">
							</view>
						</view>
						<view class="address-input-row  bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-20 flex">
							<view class="fc left color-#fff">
								开户银行
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="请选择开户银行" v-model="bankForm.bank_name"
									placeholder-style="color:#fff">
							</view>
						</view>
						<view class="address-input-row  bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-20 flex">
							<view class="fc left color-#fff">
								银行卡卡号
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="请填写银行卡号" v-model="bankForm.bank_account"
									placeholder-style="color:#fff">
							</view>
						</view>
						<view class="address-input-row bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-20 flex">
							<view class="fc left color-#fff">
								支行名称
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="请输入支行名称" v-model="bankForm.bank_branch"
									placeholder-style="color:#fff">
							</view>
						</view>
						<!-- <view class="address-input-row bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-20 flex">
							<view class="fc left color-#fff">
								交易 密码
							</view>
							<view class="right flex1 fc">
								<input type="text" password placeholder="请输入交易密码" placeholder-style="color:#fff">
							</view>
						</view> -->
						<view class="address-input-row bg-#407CEE bg-op-40  backdrop-blur-[29rpx] px-32 py-36 flex">
							<view class="fc left color-#fff">
								设为默认地址
							</view>
							<view class=" fc ml-auto">
								<uv-switch v-model="bankForm.default" :active-value="1" :inactive-value="0"
									activeColor="#407CEE"></uv-switch>
							</view>
						</view>
					</scroll-view>
				</view>


				<view class=" px-40 py-10  bg-#fff">

					<view class="btn-full fcc" :class="{'op-20':!submitBtnShow}" @click="toAdd">
						立即添加
					</view>
					<view class="btn-area"></view>
				</view>

			</view>
		</view>


	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import * as UserApi from '@/api/user.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				bankForm: {
					bank_name: "",
					bank_account: "",
					bank_branch: '',
					default: 1
				},
			};
		},
		computed: {
			...mapState(['userInfo']),
			submitBtnShow() {
				return this.bankForm.bank_name && this.bankForm.bank_account && this.bankForm.bank_branch
			}
		},
		methods: {
			toAdd() {
				if (this.submitBtnShow) {
					uni.showLoading()
					UserApi.addBank(this.bankForm).then(res => {
						if (res.code == 0) {
							uni.showToast({
								title: '删除成功'
							})
							this.$store.dispatch('getBankList')
						}
					}).finally(() => {
						uni.hideLoading()
					})
				}
			},
			select(e) {
				this.bankForm.style = e
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}


	.address-input-row {

		position: relative;

		.left {
			min-width: 168rpx;
			font-weight: 500;
			font-size: 32rpx;

			line-height: 45rpx;
		}

		input {
			font-weight: 400;
			font-size: 30rpx;

			line-height: 80rpx;
			height: 80rpx;
			display: block;
			width: 98%;
			text-align: right;
			color: #fff;
		}

		&:after {
			content: "";
			display: block;
			left: 30rpx;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #fff;
			position: absolute;
			opacity: .4;
		}
	}



	.address-input-row:nth-last-of-type(1) {
		&:after {
			display: none;
		}
	}

	.box-drop {}
</style>