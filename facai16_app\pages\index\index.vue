<template>
	<view>
		<view class="page-bg ">
			<view class="px-32 py-20">
				<top-status-bar></top-status-bar>
				<view class="fc-bet ">
					<view class="flex1 h-90 fc">
						<image src="/static/index/logo.png" mode="aspectFit" class="w-100 h-72 block mr-20"></image>
						<text class="fm color-#407CEE t-48 lh-63">
							Mistral AI
						</text>
					</view>
					<navigator url="/pages/sign/sign">
						<view class="w-90 f-pr h-90 r-90 bg-#1E2A40 fcc ml-16">
							<image src="/static/index/date.png" size-44 mode="aspectFit"></image>

							<image src="/static/index/tag-youli.png" mode="aspectFit"
								class="f-pa w-48 h-26 right-f-16 top-f-5"></image>
						</view>
					</navigator>
				</view>
			</view>
			<view class=" f-pr">

				<view class="px-37 pb-37">
					<view class="index-swiper f-pr mb-24 overflow r-20">
						<swiper :current="index" @change="swiperChange" :autoplay="true" :interval="3000"
							:duration="1000" circular>
							<swiper-item v-for="(item,index) in activeList" :key="index">
								<view class="swiper-item">
									<image :src="item.img" mode="aspectFill" block r-20></image>
								</view>
							</swiper-item>

						</swiper>
						<view class=" fcc f-pa left-0 bottom-10 right-0">
							<view class="points fcc r-50">
								<view class="point" v-for="(item,idx) in activeList" :key="index"
									:class="{on:idx == index }"></view>
							</view>
						</view>
					</view>

					<view class="r-20 f-pr overflow bg-#000 mb-24">
						<video :src="videoUrl" class="w-676 h-320 r-20"></video>
					</view>
					<view class="flex flex-wrap justify-between">
						<view class="w-326 h-168 f-pr mb-24" v-if="hasToken" @click="recharge">
							<view class="f-pa inset bg-#407CEE bg-op-98 r-20" backdrop-filter="blur(29rpx)"></view>
							<view class="f-pa inset flex justify-between">
								<view class="pl-34 pt-24">
									<view class="t-30 color-#fff t-30lh-42" mb-8>
										我要充值
									</view>
									<view>
										<image src="/static/index/c-arr.png" mode="aspectFit" class="block size-40">
										</image>
									</view>
								</view>
								<view class="pt-16 pr-7">
									<image src="/static/index/01.png" mode="aspectFit" class="block size-140"></image>
								</view>
							</view>
						</view>
						<view class="w-326 h-168 f-pr mb-24" v-if="hasToken" @click="withdrawal">
							<view class="f-pa inset bg-#407CEE bg-op-98 r-20" backdrop-filter="blur(29rpx)"></view>
							<view class="f-pa inset flex justify-between">
								<view class="pl-34 pt-24">
									<view class="t-30 color-#fff t-30lh-42" mb-8>
										我要提现
									</view>
									<view>
										<image src="/static/index/c-arr.png" mode="aspectFit" class="block size-40">
										</image>
									</view>
								</view>
								<view class="pt-10 pr-27">
									<image src="/static/index/02.png" mode="aspectFit" class="block w-121 h-153">
									</image>
								</view>
							</view>
						</view>
						<view class="w-326 h-168 f-pr mb-24" @click="toNewsList">
							<view class="f-pa inset bg-#407CEE bg-op-98 r-20" backdrop-filter="blur(29rpx)"></view>
							<view class="f-pa inset flex justify-between">
								<view class="pl-34 pt-24">
									<view class="t-30 color-#fff t-30lh-42" mb-8>
										新闻资讯
									</view>
									<view>
										<image src="/static/index/c-arr.png" mode="aspectFit" class="block size-40">
										</image>
									</view>
								</view>
								<view class="pt-16 pr-7">
									<image src="/static/index/03.png" mode="aspectFit" class="block size-140"></image>
								</view>
							</view>
						</view>
						<view class="w-326 h-168 f-pr mb-24" @click="toGuide">
							<view class="f-pa inset bg-#407CEE bg-op-98 r-20" backdrop-filter="blur(29rpx)"></view>
							<view class="f-pa inset flex justify-between">
								<view class="pl-34 pt-24">
									<view class="t-30 color-#fff t-30lh-42" mb-8>
										新手指南
									</view>
									<view>
										<image src="/static/index/c-arr.png" mode="aspectFit" class="block size-40">
										</image>
									</view>
								</view>
								<view class="pt-13 pr-0 f-pa right-0 top-0">
									<image src="/static/index/04.png" mode="aspectFit" class="block w-170 h-152">
									</image>
								</view>
							</view>
						</view>
					</view>



					<box-box class="mb-24">
						<view class="fc-bet py-10">
							<view class="pl-5 pr-20">
								<image src="/static/index/hdgg.png" mode="aspectFit" class="block w-172 h-59"></image>
							</view>
							<view class="flex1 fc">
								<view class="w-450">
									<uv-notice-bar :icon="false" :text="text" bgColor="none" color="#fff"
										fontSize="26rpx"></uv-notice-bar>
								</view>
							</view>
						</view>
					</box-box>

					<box-box class="mb-24">
						<view class="fc-bet py-27 t-24 lh-44 color-#fff">
							<view class="flex1 fcc-h" @click="toPrice">
								<image src="/static/index/index-img1.png" mode="aspectFit" class="block w-84 h-81">
								</image>
								<view>
									幸运转盘
								</view>
							</view>
							<view class="flex1 fcc-h" @click="toshare">
								<image src="/static/index/index-img2.png" mode="aspectFit" class="block w-84 h-81">
								</image>
								<view>
									邀请好友
								</view>
							</view>
							<view class="flex1 fcc-h" @click="toNotice">
								<image src="/static/index/index-img3.png" mode="aspectFit" class="block w-84 h-81">
								</image>
								<view>
									通知公告
								</view>
							</view>
						</view>
					</box-box>


					<!-- news -->
					<view class="r-20 f-pr overflow border-1 border-solid border-color-#396ED2 mb-24">
						<image src="/static/index/newsbg.png" mode="widthFix" class="f-pa w-full block"></image>
						<view class="f-pr z-2">
							<view class="h-90 pl-24 fc t-30 color-#fff">
								新闻咨询
							</view>
							<NewsItem v-for="(item,index) in newList" :key="item.id" :item='item' />
						</view>
					</view>

					<!-- hot -->
					<view class="r-20 f-pr overflow border-1 border-solid border-color-#396ED2 mb-24">
						<image src="/static/index/hotbg.png" mode="widthFix" class="f-pa w-full block"></image>
						<view class="f-pr z-2">
							<view class="h-90 pl-24 fc t-30 color-#fff">
								热门项目
							</view>
							<view class="px-24">
								<ProjectItem v-for="item in projectList" :key="item.id" :item='item' />
							</view>
						</view>
					</view>
				</view>
				<uv-gap height="10rpx"></uv-gap>
			</view>
			<view>
				<foot-nav-bar :fixed="true"></foot-nav-bar>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		noticeData,
		homeVideoLink,
		getArticle
	} from '@/api/home.js'
	import ProjectItem from '@/pages/shareholding/project-item.vue'
	import * as JobApi from '@/api/job.js'
	import NewsItem from '@/pages/news/news-item.vue'
	export default {
		components: {
			ProjectItem,
			NewsItem
		},
		data() {
			return {
				hasToken: !!uni.getStorageSync("Accept-Token"),
				index: 0,
				swiperList: [],
				text: "",
				videoUrl: '',
				newList: [],
				activeList: [],
				projectList: []
			};
		},
		onShow() {
			this.getProjectList()
		},
		methods: {
			async getProjectList() {
				try {
					const resData = await JobApi.getProjects({
						type: 1,
						page: 1
					})
					if (resData.code == 0) {
						this.projectList = resData.data.data
					}
				} catch (e) {
					//
				}
			},
			swiperChange(e) {
				console.log(e)
				this.index = e.detail.current
			},
			async getNotice() {
				try {
					const resData = await noticeData()
					if (resData.data) {
						this.text = resData.data.notice
					}
				} catch (e) {}
			},
			async getHomeVideoLink() {
				try {
					const resData = await homeVideoLink()
					if (resData.data) {
						this.videoUrl = resData.data.val
					}
				} catch (e) {}
			},
			recharge() {
				uni.navigateTo({
					url: '/pages/cash/recharge2'
				})
			},
			withdrawal() {
				uni.navigateTo({
					url: '/pages/cash/withdrawal'
				})
			},
			toNewsList() {
				uni.navigateTo({
					url: '/pages/news/news'
				})
			},
			toGuide() {
				uni.navigateTo({
					url: '/pages/help/help'
				})
			},
			toPrice() {
				uni.navigateTo({
					url: '/pages/luck-draw/luck-draw'
				})
			},
			toshare() {
				uni.navigateTo({
					url: '/pages/invite/invite'
				})
			},
			toNotice() {
				uni.navigateTo({
					url: '/pages/notice/notice'
				})
			},
			async getNews() {
				try {
					const resData = await getArticle({
						class_id: 2,
					})
					if (resData.data) {
						this.newList = resData.data.data ?? []
					}
				} catch (e) {}
			},
			async getActive() {
				try {
					const resData = await getArticle({
						class_id: 8
					})
					if (resData.data) {
						this.activeList = resData.data.data ?? []
					}
				} catch (e) {}
			}
		},
		onLoad() {
			this.getNotice()
			this.getHomeVideoLink()
			this.getActive()
			this.getNews()
		}
	};
</script>
<style lang="scss" scoped>
	.page-bg {
		background: url(/static/index/bg.png) center 0 no-repeat #000511;
		background-size: 100% auto;
	}
</style>