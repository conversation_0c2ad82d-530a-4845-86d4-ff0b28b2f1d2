<?php
namespace app\admin\controller;

use app\admin\service\SmsCodeService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 短信
 */
class SmsCode
{

    /**
     * 短信列表
     * @return mixed
     */
    public function getSmsCodeLists(): Json
    {
        $SmsCodeService  = new SmsCodeService();
        $data            = $SmsCodeService->getSmsCodeLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取短信信息
     * @return Json
     */
    public function getSmsCodeInfo(): Json
    {
        $id             = Request::param('id',0);
        $SmsCodeService = new SmsCodeService();
        $data           = $SmsCodeService->getSmsCodeInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除短信
     * @return Json
     */
    public function deleteSmsCode(): Json
    {
        $id             = Request::param('id',0);
        $SmsCodeService = new SmsCodeService();
        $data           = $SmsCodeService->deleteSmsCode($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}