<?php
namespace app\common\utils;


class Interest
{
    public static function calculateInterest($principal, $rate, $days)
    {
        if ($days == 1)
        {
            return $principal * pow((1 + $rate), $days) - $principal;
        }
        elseif ($days > 1)
        {
            $first = $principal * pow((1 + $rate), $days -1) - $principal;
            $last  = $principal * pow((1 + $rate), $days ) - $principal;

            return $last - $first;
        }
        else
        {
            return  0;
        }
    }

    public static function calculateInterestAll($principal, $rate, $days)
    {
        return $principal * pow((1 + $rate), $days ) - $principal;
    }
}