<?php
namespace app\common\utils;


use Exception;
use think\facade\Log;

class Record
{


    /**
     * 错误日志
     * @param string $channel
     * @param Exception $e
     * @param string $more
     * @return string
     */
    public static  function exception(string $channel, Exception $e, string $more = ''): string
    {
        $msg = $more .'__'. $e->getFile() . '__' . $e->getLine() . '__' . $e->getCode() . '__' . $e->getMessage();

        Log::channel($channel)->error($msg);

        return $msg;
    }


    /**
     * 日志
     * @param string $channel
     * @param string $msg
     * @param string $type
     * @param array $data
     */
    public static  function log(string $channel, string $msg, array $data = [], string $type='info')
    {
        if ($data)
        {
            Log::channel($channel)->record($msg . "@@" . json_encode($data), $type);
        }
        else
        {
            Log::channel($channel)->record($msg, $type);
        }

    }
}