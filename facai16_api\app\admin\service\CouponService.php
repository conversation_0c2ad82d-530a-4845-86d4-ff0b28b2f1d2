<?php
namespace app\admin\service;

use app\common\repository\CouponRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;

/**
 * 优惠券
 */
class  CouponService
{
    /**
     * 优惠券列表
     * @param array $params
     * @return array
     */
    public function getCouponLists(array $params): array
    {
        $where      = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }


        $CouponRepo = new CouponRepository();
        $data       = $CouponRepo->paginates($where, '*', $params['limit']);
        foreach ($data['data'] as &$value)
        {
            $value['expire_time'] = date('Y-m-d H:i:s',$value['expire_time']);
            $value['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
            $value['update_time'] = date('Y-m-d H:i:s',$value['update_time']);
        }


        return Result::success($data);

    }


    /**
     * 添加优惠券
     * @param array $params
     * @return array
     */
    public function addCoupon(array $params): array
    {
        $UserRepo  = new UserRepository();

        $user      = $UserRepo->findByCondition([['phone', '=', $params['phone']]]);

        if (empty($user))
        {
            return Result::fail('用户信息不存在');
        }

        $insert = [
            'uid'           => $user['id'],
            'phone'         => $user['phone'],
            'username'      => $user['username'],
            'allow'         => $params['allow'],
            'is_test'       => $user['is_test'],
            'amount'        => $params['amount'],
            'expire_time'   => strtotime($params['expire_time']),
            'create_time'   => time(),
            'update_time'   => time()
        ];

        $CouponRepo = new CouponRepository();
        $res        = $CouponRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }


    /**
     * 更新优惠券
     * @param array $params
     * @return array
     */
    public function updateCoupon(array $params): array
    {
        $UserRepo  = new UserRepository();

        $user      = $UserRepo->findByCondition([['phone', '=', $params['phone']]]);

        if (empty($user))
        {
            return Result::fail('用户信息不存在');
        }

        $update = [
            'phone'         => $user['phone'],
            'username'      => $user['username'],
            'is_test'       => $user['is_test'],
            'allow'         => $params['allow'],
            'amount'        => $params['amount'],
            'status'        => $params['status'],
            'expire_time'   => strtotime($params['expire_time']),
            'update_time'   => time()
        ];

        $CouponRepo = new CouponRepository();
        $res        = $CouponRepo->updateById($params['id'], $update);

        if (!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }

    /**
     * 删除优惠券
     * @param int $id
     * @return array
     */
    public function deleteCoupon(int $id): array
    {
        $CouponRepo = new CouponRepository();
        $res        = $CouponRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail('删除失败');
        }

        return Result::success();
    }
}