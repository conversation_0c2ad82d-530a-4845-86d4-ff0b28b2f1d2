<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\utils\Ajax;
use app\common\utils\IpAddress;
use app\common\utils\Record;
use app\index\service\LoginService;
use think\facade\Request;
use think\response\Json;

/**
 * 登入
 * Class Login
 * @package app\api\controller
 */
class Login
{
    /**
     * 登入
     * @return Json
     */
    public function login(): Json
    {
        $params        = Request::only(['phone', 'password']);

        $LoginService  = new LoginService();

        $result        = $LoginService->login($params['phone'], $params['password']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 注册
     * @return Json
     */
    public function register(): Json
    {
        $params       = Request::only([
            'phone',
            'password',
            'invite',
            'code' => '',
            'withdraw',
            'username'
        ]);

        $ip            = IpAddress::realIP();
        $RedisLock     = new RedisLock();
        $status        = $RedisLock->lock('register:' . $ip);

        try {

            if (empty($status))
            {
                return Ajax::fail('请不要重复提交');
            }


            $LoginService  = new LoginService();

            $result        = $LoginService->register(
                $params['password'],
                $params['phone'],
                $params['code'],
                $params['invite'],
                $params['withdraw'],
                $params['username']
            );

            $RedisLock->unLock('register:' . $ip);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $e)
        {
            $RedisLock->unLock('register:' . $ip);
            return Ajax::fail(Record::exception('http', $e));
        }
    }

    /**
     * 找回密码
     * @return Json
     */
    public function reset(): Json
    {
        $params        = Request::only([
            'phone'     => '',
            'password'  => '',
            'code'      => ''
        ]);

        $LoginService  = new LoginService();

        $result        = $LoginService->reset($params['phone'], $params['password'], $params['code']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


}
