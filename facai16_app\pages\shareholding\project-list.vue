<template>
	<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true" :refresher-triggered="refresherTriggered"
		@refresherrefresh='onRefresh' scroll-y="true" refresher-background='#ffffff00'
		:style="`height: calc(100vh - ${this.ph ? this.ph: '180px'});`">
		<view class="p-32 !pt-16">
			<ProjectItem v-for="(item,index) in list" :key="item.id" :item='item' />
			<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
				@loadmore="scrollHandle" />
		</view>
		<view class="h-20"></view>
		<view class="btn-area"></view>
	</scroll-view>
</template>

<script>
	import * as JobApi from '@/api/job.js'
	import ProjectItem from "./project-item.vue"
	export default {
		props: ['type', 'ph'],
		components: {
			ProjectItem
		},
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		onShow() {
			console.log('onShow')
		},
		methods: {
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await JobApi.getProjects({
						type: this.type,
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
			toCopy(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					},

					fail: () => {
						uni.showToast({
							title: "复制失败"
						})
					}

				})
			}
		},
	}
</script>