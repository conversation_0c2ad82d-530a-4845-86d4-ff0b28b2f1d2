<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;
use <PERSON>zhichao\IpLocationZh\Ip;

/**
 * 默认模块
 */
class Index
{

    /**
     * 测试
     * @return void
     */
    public function index() : <PERSON><PERSON>
    {
        return Ajax::message(0,'hello world');
    }


    /**
     * ping
     * @return Json
     */
    public function ping(): Json
    {
        $data  = [
            'ip'    => Request::ip(),
            'local' => join(',', array_filter(Ip::find(Request::ip()))),
        ];

        return Ajax::message(0, __('success'), $data);
    }
}
