<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">
					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								{{userInfo.nickname}}的团队
							</view>
						</template>
					</uv-navbar>
				</view>
				<view class="flex1 f-pr color-#fff">
					<scroll-view scroll-y="true" class="scroll-view" @scrolltolower="scrollHandle"
						:refresher-enabled="true" :refresher-triggered="refresherTriggered"
						@refresherrefresh='onRefresh' refresher-background='#ffffff00'>
						<TeamItem v-for="item in list" :key="item.id" :item="item" />
						<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
						<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
							@loadmore="scrollHandle" />
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>
				</view>
			</view>
		</view>



	</view>
</template>

<script>
	import TeamItem from './components/team-item.vue'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import * as UserApi from '@/api/user.js'
	import {
		mapState
	} from 'vuex'
	export default {
		mixins: [needAuth],
		components: {
			TeamItem
		},
		computed: {
			...mapState(['userInfo']),
		},
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false
			};
		},
		onShow() {
			this.onRefresh()
		},
		methods: {
			toTeamChild() {
				uni.navigateTo({
					url: '/pages/team/child'
				})
			},
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getTeamList()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getTeamList(1)
			},
			async getTeamList(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await UserApi.getTeamList({
						lv: 2,
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>