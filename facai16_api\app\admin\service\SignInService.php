<?php
namespace app\admin\service;

use app\common\repository\LevelLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\SignInGiftLogRepository;
use app\common\repository\SignInGiftRepository;
use app\common\repository\SignInRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 签到记录
 */
class SignInService
{
    /**
     * 用户签到记录
     * @return array
     */
    public function getSignInLists(): array
    {
        $SignInRepo = new SignInRepository();
        $data       = $SignInRepo->paginates([],'*',10,['id'=> 'desc']);

        return Result::success($data);
    }

    /**
     * 添加签到记录
     * @param $params
     * @return array
     */
    public function addSignIn($params): array
    {
        $SignInRepo = new SignInRepository();
        $res        = $SignInRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除签到记录
     * @param $id
     * @return array
     */
    public function deleteSignIn($id): array
    {
        $SignInRepo = new SignInRepository();

        $res        = $SignInRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 签到礼品领取记录
     * @return array
     */
    public function getSignInGiftLogLists(): array
    {
        $RaffleRepo = new SignInGiftLogRepository();
        $data       = $RaffleRepo->paginates();
        return Result::success($data);
    }


    /**
     * 签到礼品表
     * @return array
     */
    public function getSignInGiftLists(): array
    {
        $RaffleRepo = new SignInGiftRepository();
        $data       = $RaffleRepo->paginates();
        return Result::success($data);
    }


    /**
     * 添加签到礼品
     * @param $params
     * @return array
     */
    public function addSignInGift($params): array
    {
        $RaffleRepo = new SignInGiftRepository();
        $data       = $RaffleRepo->inserts($params);


        if (!$data)
        {
            return Result::fail();
        }

        return Result::success();

    }


    /**
     * 更新签到礼品
     * @param $params
     * @return array
     */
    public function updateSignInGift($params): array
    {

        $SignInGiftRepo = new SignInGiftRepository();

        $res            = $SignInGiftRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }




    /**
     * 删除签到礼品
     * @param $id
     * @return array
     */
    public function deleteSignInGift($id): array
    {
        $SignInRepo = new SignInGiftRepository();

        $res        = $SignInRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

}
