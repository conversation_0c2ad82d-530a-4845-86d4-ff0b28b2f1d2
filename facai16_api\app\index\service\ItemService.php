<?php
namespace app\index\service;

use app\common\jobs\ItemGiftJob;
use app\common\jobs\ItemRebateJob;
use app\common\jobs\LevelUpJob;
use app\common\model\MoneyClass;
use app\common\repository\CouponRepository;
use app\common\repository\GiveProductLogRepository;
use app\common\repository\ItemClassRepository;
use app\common\repository\ItemOrderRepository;
use app\common\repository\ItemRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserAddressRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\Arrays;
use app\common\utils\BCalculator;
use app\common\utils\Numeral;
use app\common\utils\Order;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Db;
use think\facade\Request;


/**
 * 商品模块
 * Class ItemService
 * @package app\home\service
 */
class ItemService
{
    /**
     * 类型
     * @return array
     */
    public function classes(): array
    {
        $ItemClassRepo = new ItemClassRepository();
        $data          = $ItemClassRepo->selectByCondition();


        foreach ($data as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }



        return Result::success($data);
    }


    /**
     * 商品列表
     * @param int $classId
     * @return array
     */
    public function lists(int $classId): array
    {
        $UserRepo  = new UserRepository();
        $user      = $UserRepo->userByHeader();

        $LevelRepo = new LevelRepository();
        $level     = $LevelRepo->findById($user['level']);

        if(empty($level))
        {
            $extra = 0;
        }
        else
        {
            $extra = $level['extra'];
        }


        $where      = [];
        $where[]    = ['status', 'in', '0,2'];

        if($classId)
        {
            $where[] = ['class_id', '=', $classId];
        }

        $order       = [
            'status'    => 'asc',
            'sort'      => 'desc',
        ];

        $ItemRepo = new ItemRepository();
        $data     = $ItemRepo->paginates($where,'*',10, $order);

        foreach($data['data'] as &$item)
        {
            if ($item['level_income'] && $item['profit_cycle_time'] > 86400)
            {
                $fold                = $item['profit_cycle_time'] / 86400;
                $extra               = $extra * $fold;
            }

            $item['profit_extra'] = $extra;
            $item['img']          = Uri::file($item['img']);
        }

        return Result::success($data);
    }

    /**
     * 商品详情
     * @param int $id
     * @return array
     */
    public function info(int $id): array
    {
        $UserRepo = new UserRepository();
        $user     = $UserRepo->userByHeader();

        $ItemRepo = new ItemRepository();
        $item     = $ItemRepo->findById($id);

        if(empty($item))
        {
            return Result::fail('查无数据');
        }

        $LevelRepo = new LevelRepository();
        $level     = $LevelRepo->findById($user['level']);

        if(empty($level))
        {
            $extra = 0;
        }
        else
        {
            $extra = $level['extra'];
        }

        if ($item['level_income'] && $item['profit_cycle_time'] > 86400)
        {
            $fold                = $item['profit_cycle_time'] / 86400;
            $extra               = $extra * $fold;
        }

        $item['profit_extra'] = $extra;
        $item['img']          = Uri::file($item['img']);

        return Result::success($item);
    }

    /**
     * 相关购买
     * @param int $id
     * @param string $coupon
     * @param string $pin
     * @return array
     */
    public function pay(int $id, string $coupon, string $pin): array
    {
        $time            = Request::time();
        $ItemRepo        = new ItemRepository();
        $UserRepo        = new UserRepository();
        $LevelRepo       = new LevelRepository();
        $ItemOrderRepo   = new ItemOrderRepository();
        $MoneyLogRepo    = new MoneyLogRepository();
        $UserInfoRepo    = new UserInfoRepository();
        $CouponRepo      = new CouponRepository();
        $GiveProductLogRepo = new GiveProductLogRepository();
        $UserAddressRepo = new UserAddressRepository();

        //用户信息
        $user     = $UserRepo->userByHeader();

        $where    = [];
        $where[]  = ['id', '=', $id];
        $item     = $ItemRepo->findByCondition($where);

        //无数据
        if(empty($item))
        {
            return Result::fail('查无数据');
        }

        if($item['status'] != 0)
        {
            $msg = $item['status'] != 2 ? '前产品关闭' : '前产品售罄';

            return Result::fail($msg);
        }

        if ($pin != $user['pin'])
        {
            return Result::fail('支付密码错误');
        }

        //无数据
        if($item['progress'] >= 100)
        {
            $update = [
                'status'      => 2,
                'update_time' => $time,
            ];

            $ItemRepo->updateById($id, $update);
            return Result::fail('查无数据');
        }

        //判断限购次数
        $where           = [];
        $where[]         = ['uid', '=', $user['id']];
        $where[]         = ['item_id', '=', $item['id']];
//        $where[]         = ['item_status', 'in', '0,2'];

        $itemRecordCount = $ItemOrderRepo->countByCondition($where);

        if($itemRecordCount >= $item['invest_limit'])
        {
            return Result::fail('当前产品最大持有' . $item['invest_limit']);
        }

        if ($user['level'] < $item['level'])
        {
            return Result::fail('等级不够,vip会员才可以购买');
        }

        //额外收益
        $level         = $LevelRepo->findById($user['level']);

        $extra         = $level['extra'] ?? 0;
        $invest        = $item['invest'];

        if ($coupon)
        {
            $coupons       = $CouponRepo->findById($coupon);

            if ($coupons['expire_time'] < time() || $coupons['status'] == 1)
            {
                return Result::fail('优惠券已经过期');
            }

        }

        if ($item['level_income'] && $item['profit_cycle_time'] > 86400)
        {
            $fold                = $item['profit_cycle_time'] / 86400;
            $extra               = $extra * $fold;
        }

        //周期
        $cycle        = $item['profit_cycle'];

        //周期时间
        $cycleTime    = $item['profit_cycle_time'];

        //结束时间
        $endTime      = $time + $cycle * $cycleTime;

        //结算时间
        $nextTime     = $item['profit_type'] == 1 ?  $endTime : $time + $cycleTime;

        //结束时间
        $endTime      = date('Y-m-d H:i:s', $endTime);

        //全部收益
        $profitTotal  = $cycle * $invest * ($item['profit_rate'] + $extra) * $item['profit_more'] / 100;

        //订单号
        $orderNo      = Order::encode(time() . rand(1000, 9999));


        Db::startTrans();

        try {

            if ($coupon)
            {
                $update =[
                    'status'         => 1,
                    'update_time'    => time(),
                ];

                $CouponRepo->updateById($coupons['id'], $update);
            }

            $profitTotal = Numeral::decimalPlacesDownwards($profitTotal);

            $insert = [
                //用户id
                'uid'               => $user['id'],
                //用户名
                'username'          => $user['username'],
                //手机号
                'phone'             => $user['phone'],
                //测试
                'is_test'           => $user['is_test'],
                //结束时间
                'end_time'          => $endTime,
                //预计总收益金额（元）
                'profit_total'      => $profitTotal,
                //额外收益
                'profit_more'       => $item['profit_more'],
                //收益率
                'profit_rate'       => $item['profit_rate'],
                //收益率额外
                'profit_extra'      => $extra,
                //订单号
                'order_no'          => $orderNo,
                //周期时间
                'cycle_time'        => $cycleTime,
                //创建时间
                'create_time'       => $time,
                //更新时间
                'update_time'       => $time,
                //周期
                'cycle_end'         => $cycle,
                //项目类型
                'item_type'         => $item['profit_type'],
                //投资金额
                'amount'            => $invest,
                //项目分类id
                'item_class_id'     => $item['class_id'],
                //项目分类名称
                'item_class_name'   => $item['class_name'],
                //项目id
                'item_id'           => $item['id'],
                //项目名称
                'item_name'         => $item['title'],
                //是否是优惠券购买
                'is_coupon'         => 0,
                //下次结算时间
                'next_time'         => $nextTime,
                //项目补贴
                'gift_bonus'        => $item['gift_bonus'],
                //项目积分
                'gift_points'       => $item['gift_points'],
                //项目抽奖
                'gift_raffle'       => $item['gift_raffle'],
                //奖励执行卡
                'gift_coupon'       => $item['gift_coupon'],
                //本金
                'principal'         => $invest,
                //vip收益
                'level_income'      => $item['level_income'],
                //赠送积分
                'give_points'       => $item['give_points'],
                //赠送现金
                'give_cash'         => $item['give_cash'],
                //赠送实物
                'give_product'      => $item['give_product'],
            ];

            $orderId = $ItemOrderRepo->insertsGetId($insert);

            if (!$orderId)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }
            $phone         = substr($user['phone'],0,3).'****'.substr($user['phone'],strlen($user['phone'])-4,4);
            $_txt          = '您的'.$phone.'账号';
            $res    = $MoneyLogRepo->fund($user['id'], $invest,MoneyClass::BUY, $orderNo,$_txt .'申购:' . $item['title'] . '项目-' . $invest . '元' );

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $res    = $UserInfoRepo->statistic($user['id'],['invest_money' => $invest, 'invest_not_finish' => $invest, 'invest_not_earn' => $profitTotal]);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            };

            $points = floor($invest / 100);

            $res    = $MoneyLogRepo->point($user['id'], $points,MoneyClass::POINTS_ADD, $orderNo,'申购:' . $item['title'] . '项目赠送积分+' . $points  );


            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            };

            if($item['give_points'] > 0){

                $res    = $MoneyLogRepo->point($user['id'], $item['give_points'],MoneyClass::POINTS_ADD, $orderNo,'申购:' . $item['title'] . '项目赠送积分+' . $item['give_points']  );

                if ($res['code'])
                {
                    Db::rollback();
                 return Result::fail($res['msg']);
                };
            }


            if($item['give_cash'] > 0){

                $res    = $MoneyLogRepo->fund($user['id'], $item['give_cash'],MoneyClass::BUY_ITEM_GIVE_CASH, $orderNo,'申购:' . $item['title'] . '项目赠送现金+' . $item['give_cash']  );

                if ($res['code'])
                {
                    Db::rollback();
                    return Result::fail($res['msg']);
                };
            }
            if(!empty($item['give_product'])){
                $user_address           = $UserAddressRepo->where('uid',$user['id'])->find();
                if(!$user_address){
                    return Result::fail('没有收货地址');
                }
                $give_product_log_insert = [
                    //用户id
                    'uid'               => $user['id'],
                    //用户名
                    'username'          => $user['username'],
                    //手机号
                    'phone'             => $user['phone'],
                    //测试
                    'is_test'           => $user['is_test'],
                    'goods_title'       => $item['give_product'],
                    'order_no'          => $orderNo,
                    'address_name'      => $user_address['address_name'],
                    'address_city'      => $user_address['address_city'],
                    'address_phone'     => $user_address['address_phone'],
                    'address_place'     => $user_address['address_place'],
                    'item_id'           => $item['id'],
                    //项目名称
                    'item_name'         => $item['title'],

                ];
                $insert_id = $GiveProductLogRepo->insertGetId($give_product_log_insert);
                if (!$insert_id)
                {
                    Db::rollback();
                    return Result::fail($res['赠送实物保存失败']);
                };
            }

            // 提交事务
            Db::commit();

            //投资礼品
            queue(ItemGiftJob::class, $orderId);

            //
            queue(ItemRebateJob::class, [
                'uid'           => $user['id'],
                'money'         => $invest,
                'order_id'      => $orderId
            ]);

            queue(LevelUpJob::class, $user['id']);


            //购买成功
            return Result::success();

        } catch (\Exception $exception)
        {
            // 回滚事务
            Db::rollback();
            //错误日志
            Record::exception('index', $exception,'ItemService->pay');
            //购买失败
            return Result::fail('购买失败');
        }

    }


    /**
     * 购买记录
     * @param $tab
     * @return array
     */
    public function record($tab): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();
        $where       = [];
        $where[]     = ['uid', '=', $user['id']];
        $where[]     = ['item_status', 'in', '0,2'];
        !empty($tab) && $where[] = ['item_class_id', '=', $tab];

        $finish    = [];
        $finish[]  = ['uid', '=', $user['id']];
        $finish[]  = ['item_status', '=', 1];
        $finish[]  = ['end_time', '>', date('Y-m-d H:i:s',strtotime('- 7 day'))];
        !empty($tab) && $finish[] = ['item_class_id', '=', $tab];

        $ItemOrderRepo = new ItemOrderRepository();
        $condition = [
            $where,
            $finish
        ];

        $data          = $ItemOrderRepo->paginatesByWhereOr($condition);
        $itemIds       = array_unique(array_column($data['data'] ?? [], 'item_id'));
        $ItemRepo      = new ItemRepository();

        $where         = [];
        $where[]       = ['id', 'in', $itemIds];
        $itemInfo      = $ItemRepo->selectByCondition($where);
        $itemInfo      = Arrays::groupBy($itemInfo, 'id');

        foreach ($data['data'] as &$item)
        {
            $i                            = $item['item_id'];

            $item['img']                  = Uri::file($itemInfo[$i]['img'] ?? '') ;
            $item['profit_cycle_time']    = $itemInfo[$i]['profit_cycle_time'] ?? '';
            $item['profit_cycle']         = $itemInfo[$i]['profit_cycle'] ?? '';
            $item['profit_type']          = $itemInfo[$i]['profit_type'] ?? '';
            $item['video_link']           = $itemInfo[$i]['video_link'] ?? '';
            $item['item_invest_scale']    = sprintf("%.2f", $itemInfo[$i]['invest_scale'] ?? '');
            $item['signature_time']       = empty($item['signature_time'] ?? '') ? '' : date('Y-m-d H:i:s', $item['signature_time']);

            $item['last_time']   = date('Y-m-d H:i:s', $item['last_time'] ?: time());
            $item['end_time2']   = strtotime($item['end_time']);

            if ($item['item_status'] == 2)
            {
                $item['next_time']   = $item['end_time'];
            }
            else
            {
                $item['next_time']   = date('Y-m-d H:i:s', $item['next_time'] ?: time());
            }

            $cycle                   = $item['cycle_start'];
            $profitRateExtra         = BCalculator::calc($item['profit_rate'] + $item['profit_extra'])->mul($item['profit_more'])->div(100)->result();
            $item['profit_fake']     = BCalculator::calc($item['amount'])->mul($profitRateExtra)->mul($cycle)->result();
        }

        return Result::success($data);
    }

    /**
     * 签名
     * @param array $params
     * @return array
     */
    public function signature(array $params): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();


        $update = [
            'signature'      => $params['signature'] ?? '',
            'signature_time' => time(),
            'update_time'    => time()
        ];

        $where       = [];
        $where[]     = ['uid', '=', $user['id']];
        $where[]     = ['id', '=', $params['id']];

        $ItemOrderRepo = new ItemOrderRepository();
        $res = $ItemOrderRepo->updateByCondition($where, $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }
}