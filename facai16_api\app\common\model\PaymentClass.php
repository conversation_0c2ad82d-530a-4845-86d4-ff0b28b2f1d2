<?php
namespace app\common\model;
use think\Model;


class PaymentClass extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $name = 'payment_class';

//    use BaseRepository;

    //微信
    const WeChat                = 1;

    //支付宝
    const Alipay                = 2;

    //银行卡
    const BANK_CARD             = 3;

    //USDT
    const USDT                  = 4;

    //网银
    const ONLINE_BANK           = 5;


    //银联快捷
    const UNIONPAY_EXPRESS      = 6;


    //扫码存款
    const SCAN_QR_CODE_DEPOSIT  = 7;


    //框架
    const FRAMES                 = 0;

    //跳转
    const JUMP                   = 1;

    //账号
    const ACCOUNTS               = 2;
}