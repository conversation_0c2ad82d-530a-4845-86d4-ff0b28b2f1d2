<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * BingTongPay
 */
class BingTongPay2 implements PayInterface
{
    //支付类型
    const CHANNEL = 'BingTongPay2';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'merchantId'      => $this->config['mcd_id'],
            'orderId'         => $data['orderNo'],
            'orderAmount'     => $data['money'],
            'channelType'     => $this->config['channel'],
            'returnUrl'       => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
            'notifyUrl'       => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
        ];

        $param['sign']  = $this->signature($param);
        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $uri            = '';

        if(isset($result['code'])  && $result['code'] == 200)
        {
            $uri = $result['data']['payUrl'] ?? '';
        }

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"status":"2","code":"drenjie-zfb","orderno":"17402125841410","amount":"500.00","sign":"d24e591f6c68a22a5b0a3c4a68c10965"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];
        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['orderId'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //去空
        $data = array_filter($params);
        //排序
        ksort($data);
        //进行键值对排列  a=1&b=2&c=3
        $tmp_string = http_build_query($data);
        //参数无需进行urlencode ,上一步进行了urlencode,这里还原一下

        $tmp_string = urldecode($tmp_string);

        //签名生成
        return md5( $tmp_string .'&key='. $this->config['sign'] );

    }

}