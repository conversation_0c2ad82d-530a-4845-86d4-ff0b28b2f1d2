<?php
namespace app\common\repository;
use app\common\utils\Record;
use Exception;


/**
 * 基础的查询类
 * Class BaseRepository
 * @package app\common\repository
 */
trait BaseRepository
{

    /**
     * 查询一条 by id
     * @param int $id
     * @param string $field
     * @param array $order
     * @param bool $master
     * @param bool $lock
     * @return array
     */
    public function findById(int $id, string $field='*', array $order = [], bool $master = false, bool $lock = false): array
    {
        try
        {

            $info = $this->master($master)->lock($lock)->where($this->getPk(),'=', $id)->order($order)->field($field)->find();

            if (empty($info))
            {
                return [];
            }

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->findById');
            return [];
        }
    }


    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param array $order
     * @param bool $master
     * @return array
     */
    public function findByWhereOr(array $condition = [], string $field='*', array $order = [], bool $master = false): array
    {
        try
        {
            $info = $this->master($master)->whereOr($condition)->order($order)->field($field)->find();

            if (empty($info))
            {
                return [];
            }

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->findByWhereOr');
            return [];
        }
    }

    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param array $order
     * @param bool $master
     * @return array
     */
    public function selectByWhereOr(array $condition = [], string $field='*', array $order = [], bool $master = false): array
    {
        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];

            $info = $this->master($master)->whereOr($condition)->order($order)->field($field)->select();

            if ($info->isEmpty())
            {
                return [];
            }

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->selectByWhereOr');
            return [];
        }
    }

    /**
     * 分页
     * @param array $where
     * @param string $field
     * @param int $limit
     * @param array $order
     * @param bool $master
     * @return array
     */
    public function paginatesByWhereOr(array $where = [], string $field = '*', int $limit = 10, array $order = [], bool $master = false): array
    {

        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];


            $data = $this->master($master)->whereOr($where)->order($order)->field($field)->paginate($limit);

            return  $data->toArray();

        }catch (\Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->paginates');
            return [];
        }
    }
    

    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param array $order
     * @param bool $master
     * @param bool $lock
     * @return array
     */
    public function findByCondition(array $condition, string $field='*', array $order = [], bool $master = false, bool $lock = false): array
    {
        try
        {
            $info = $this->master($master)->lock($lock)->where($condition)->order($order)->field($field)->find();

            if (empty($info))
            {
                return [];
            }

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->findByCondition');
            return [];
        }
    }

    /**
     * select数据
     * @param array $condition
     * @param string $field
     * @param array $order
     * @param bool $master
     * @return array
     */
    public function selectByCondition(array $condition = [], string $field='*', array $order = [], bool $master = false): array
    {

        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];

            $info = $this->master($master)->where($condition)->field($field)->order($order)->select();

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->select');
            return [];
        }

    }


    /**
     * select数据
     * @param array $condition
     * @param string $field
     * @param array $order
     * @param int $offset
     * @param int $limit
     * @param bool $master
     * @return array
     */
    public function limits(array $condition = [], string $field='*', array $order = [], int $offset = 0, int $limit = 10, bool $master = false): array
    {

        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];

            $info = $this->master($master)->where($condition)->field($field)->order($order)->limit($offset, $limit)->select();

            if (empty($info))
            {
                return [];
            }

            return $info->toArray();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->limit');
            return [];
        }

    }

    /**
     * 更新数据 updateById
     */
    public function updateById(int $id, array $data): int
    {
        try
        {
            $res = $this->where($this->getPk(),'=',$id)->update($data);

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            dd($exception->getMessage());
            Record::exception('repository', $exception,'BaseRepository->updateById');
            return false;
        }
    }

    /**
     * 更新数据
     */
    public function updateByCondition($condition, array $data): int
    {
        try
        {
            $res = $this->where($condition)->update($data);

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->deleteById');
            return false;
        }
    }


    /**
     * 删除数据
     * @param  $id
     * @return int
     */
    public function deleteById($id): int
    {
        try
        {
            $res = $this->where($this->getPk(),'=',$id)->delete();

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->deleteById');
            return false;
        }
    }


    /**
     * 删除数据
     * @param array $condition
     * @return int
     */
    public function deleteByCondition(array $condition): int
    {
        try
        {
            $res = $this->where($condition)->delete();

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->deleteByCondition');
            return false;
        }
    }

    /**
     * 分页
     * @param array $where
     * @param string $field
     * @param int $limit
     * @param array $order
     * @param bool $master
     * @return array
     */
    public function paginates(array $where = [], string $field = '*', int $limit = 10, array $order = [], bool $master = false): array
    {

        try
        {
            empty($order) &&  $order = [ $this->getPk() => 'desc'];


            $data = $this->master($master)->where($where)->order($order)->field($field)->paginate($limit);

            return  $data->toArray();

        }catch (\Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->paginates');
            return [];
        }
    }

    /**
     * 新增数据
     * @param array $data
     * @return int
     */
    public function inserts(array $data): int
    {
        try
        {
            $res = $this->insert($data);

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            dd($exception->getMessage());
            Record::exception('repository', $exception,'BaseRepository->insert');
            return false;
        }
    }


    /**
     * 新增数据
     * @param array $data
     * @return false|int
     */
    public function insertsGetId(array $data)
    {
        try
        {
            $res = $this->insertGetId($data);

            if (empty($res))
            {
                return false;
            }

            return $res;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->insertGetId');
            return false;
        }
    }




    /**
     * 查询一条 by id
     * @param int $id
     * @param string $field
     * @param bool $master
     * @return string|mixed
     */
    public function valueById(int $id, string $field='', bool $master = false): string
    {
        try
        {
            $info = $this->master($master)->where($this->getPk(), '=', $id)->value($field);

            if (empty($info))
            {
                return '';
            }

            return $info;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->valueById');
            return '';
        }
    }

    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param bool $master
     * @return mixed
     */
    public function valueByCondition(array $condition, string $field='', bool $master = false)
    {
        try
        {
            return $this->master($master)->where($condition)->value($field);

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->valueByCondition');
            return '';
        }
    }


    /**
     * 查询一条 by key
     * @param array $condition
     * @param bool $master
     * @return int
     */
    public function  countByCondition(array $condition, bool $master = false): int
    {
        try
        {
            return $this->master($master)->where($condition)->count();

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->countByCondition');
            return 0;
        }
    }


    /**
     * 查询一条 by key
     * @param array $data
     * @return int
     */
    public function store(array $data): int
    {
        try
        {
            $res = $this->save($data);

            if ($res)
            {
                return true;
            }

            return false;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->save');
            return false;
        }
    }


    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param bool $master
     * @return float
     */
    public function sumByCondition(array $condition = [], string $field = '', bool $master = false): float
    {
        try
        {
            return $this->master($master)->where($condition)->sum($field);

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->sumByCondition');
            return 0.00;
        }
    }


    /**
     * 新增数据
     * @param array $data
     * @return bool
     */
    public function insertsAll(array $data): bool
    {
        try
        {
            $res = $this->insertAll($data);

            if (empty($res))
            {
                return false;
            }

            return true;

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->insertAll');
            return false;
        }
    }


    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param bool $master
     * @return mixed
     */
    public function columnByCondition(array $condition, string $field = '', bool $master = false)
    {
        try
        {
            return $this->master($master)->where($condition)->column($field);

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->columnByCondition');
            return [];
        }
    }

    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param bool $master
     * @return mixed
     */
    public function groupColumnByCondition(array $condition, string $field = '', bool $master = false)
    {
        try
        {
            return $this->master($master)->where($condition)->group($field)->column($field);

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->columnByCondition');
            return [];
        }
    }


    /**
     * 查询一条 by key
     * @param array $condition
     * @param string $field
     * @param bool $master
     * @return mixed
     */
    public function distinctColumnByCondition(array $condition, string $field = '', bool $master = false)
    {
        try
        {
            return $this->master($master)->where($condition)->distinct(true)->column($field);

        }catch (Exception $exception)
        {
            Record::exception('repository', $exception,'BaseRepository->columnByCondition');
            return [];
        }
    }




}
