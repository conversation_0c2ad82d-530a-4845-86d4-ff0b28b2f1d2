<template>
	<view class="p-24 bg-#fff r-20 mb-32">
		<view class="flex">
			<view class="flex1 flex flex-col justify-between">
				<view>
					<view class="fc-bet">
						<view class="w-300 ddd t-28 lh-40 c2">
							{{item.item_name}}
						</view>
						<uv-tags text="已完成" type="success" v-if="item.item_status==2"></uv-tags>
						<uv-tags text="进行中" type="warning" v-else></uv-tags>
					</view>
				</view>
				<view class="fc-bet m-15">
					<view class="flex1 fcc">
						<view>
							<view class="t-22 lh-30 c9">
								认购金额
							</view>
							<view class="mt-9  c2 t-32 c3 lh-45 DIN text-center">
								<text class="">{{Number(item.amount)}}</text>
							</view>
						</view>
					</view>
					<view class="w-1 h-26 bg-#999999 rotate-30 "></view>
					<view class="flex1 fcc">
						<view>
							<view class="t-22 lh-30 c9">
								总收益
							</view>
							<view class="mt-9  c2 t-32 c3 lh-45 DIN text-center">
								<text class="">{{Number(item.profit_fake)}}</text>
							</view>
						</view>
					</view>
					<view class="w-1 h-26 bg-#999999 rotate-30 "></view>
					<view class="flex1 fcc">
						<view>
							<view class="t-22 lh-30 c9">
								今日收益
							</view>
							<view class="mt-9  c2 t-32 c3 lh-45 DIN text-center">
								<text class="">{{Number(item.profit_now)}}</text>
							</view>
						</view>
					</view>
				</view>
				<view>
					<view class="fc t-22 c9 lh-30 flex justify-between">
						<view class="">
							<text>
								项目进度
							</text>
							<text class="c2 fw-700 ml-10">
								{{item.cycle_start}}天 / {{item.cycle_end}}天
							</text>
						</view>
						<text class="c2 fw-700 ml-10 text-right">
							执行时间：{{item.last_time}}
						</text>
					</view>
					<view class="mt-14 r-20 h-12 f-pr overflow">
						<uv-line-progress :percentage="item.cycle_start/item.cycle_end*100" height="12rpx"
							:showText="false" activeColor="#407CEE" inactiveColor="#D9E5FC"></uv-line-progress>
					</view>
				</view>
				<view class="button-group mt-9">
					<button type="warn" class="flex-1" @click="onClickB">
						<text class="t-28">获得兑换券</text>
					</button>
					<button class="flex-1" v-if="item.gift_bonus && tab!=1">
						<text class="t-28"> 补贴
							{{Number(item.gift_bonus)}}</text>
					</button>
					<button type="primary" class="flex-1" @click="onClickA">
						<text class="t-28">立即执行</text>
					</button>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	import * as JobApi from '@/api/job.js'
	export default {
		props: ['item', 'tab'],
		methods: {
			onClickB() {
				uni.navigateTo({
					url: '/pages/invite/invite'
				})
			},
			onClickA() {
				uni.showLoading({
					title: '执行中'
				})
				JobApi.jobPush({
					id: this.item.id
				}).then((res) => {
					if (res.code == 0) {
						this.$emit('refresh');
						uni.showToast({
							title: '执行成功'
						})
					}
				}).finally(() => {
					uni.hideLoading()
				})
			}
		}
	}
</script>

<style>
	.button-group {
		display: flex;
		justify-content: space-around;
		padding: 10rpx;
	}

	.button-group button+button {
		margin-left: 4px;
	}
</style>