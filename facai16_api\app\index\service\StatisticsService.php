<?php

namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 统计类别
 */
class StatisticsService
{
    /**
     * 在签约中
     * @return array
     */
    public function itemNotFinishedSum(): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();

        $ItemOrderRepo = new ItemOrderRepository();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['item_status', 'in', '0,2'];

        $amount     = $ItemOrderRepo->sumByCondition($where,'amount');

        return  Result::success(['amount' => $amount]);
    }


    /**
     * 今天收益
     * @return array
     */
    public function todayIncomeSum(): array
    {

        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();

        $time           = strtotime(date('Y-m-d 00:00:00', Request::time()));

        $MoneyLogRepo   = new MoneyLogRepository();

        $classes      = [
            MoneyClass::SIGNIN,
            MoneyClass::RAFFLE,
            MoneyClass::INCOME,
            MoneyClass::ALLOWANCE
        ];

        $where          = [];
        $where[]        = ['uid', '=', $user['id']];
        $where[]        = ['create_time', '>', $time];
        $where[]        = ['class_id', 'in', $classes];

        $amount         =  $MoneyLogRepo->sumByCondition($where, 'amount');

        return  Result::success(['amount' => $amount]);

    }


    /**
     * @return array
     */
    public function incomeTotalSum(): array
    {
        $UserRepo = new UserRepository();
        $info     = $UserRepo->userInfoByHeader();

        $array   = [
            $info['team_invite'],
            $info['income_money'],
            $info['raffle_money'],
            $info['signin_money'],
            $info['bonus_money'],
        ];

        return  Result::success(['amount' => array_sum($array)]);
    }


    /**
     *
     * @return array
     */
    public function todayTeamTotalSum(): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();

        $time           = strtotime(date('Y-m-d 00:00:00', Request::time()));

        $MoneyLogRepo   = new MoneyLogRepository();

        $classes      = [
            MoneyClass::REBATE_V1,
            MoneyClass::REBATE_V2,
        ];


        $where          = [];
        $where[]        = ['uid', '=', $user['id']];
        $where[]        = ['create_time', '>', $time];
        $where[]        = ['class_id', 'in', $classes];

        $amount         =  $MoneyLogRepo->sumByCondition($where, 'amount');

        return  Result::success(['amount' => $amount]);

    }
}