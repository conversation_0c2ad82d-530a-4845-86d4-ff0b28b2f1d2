<?php
namespace app\admin\controller;

use app\admin\service\PaymentUpperService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 支付上游产品
 */
class PaymentUpper
{

    /**
     * 获取支付上游产品
     * @return mixed
     */
    public function getPaymentUpperLists(): Json
    {
        $PaymentService = new PaymentUpperService();
        $data           = $PaymentService->getPaymentUpperLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取支付上游产品信息
     * @return Json
     */
    public function getPaymentUpperInfo(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentUpperService();
        $data           = $PaymentService->getPaymentUpperInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加支付上游产品信息
     * @return Json
     */
    public function addPaymentUpper(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentUpperService();
        $data           = $PaymentService->addPaymentUpper($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新支付上游产品
     * @return Json
     */
    public function updatePaymentUpper(): Json
    {
        $param          = Request::param();
        $PaymentService = new PaymentUpperService();
        $data           = $PaymentService->updatePaymentUpper($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除支付上游产品
     * @return Json
     */
    public function deletePaymentUpper(): Json
    {
        $id             = Request::param('id',0);
        $PaymentService = new PaymentUpperService();
        $data           = $PaymentService->deletePaymentUpper($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}