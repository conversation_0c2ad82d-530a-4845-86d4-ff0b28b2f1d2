// 引用网络请求中间件
import request from "../utils/request/request"

export function getPaymentMethods(data) {
	return request({
		url: '/recharge/method',
		method: 'POST',
		data
	})
}

export function recharge(data) {
	return request({
		url: '/recharge/recharge',
		method: 'POST',
		data
	})
}

export function rechargeRecord(data) {
	return request({
		url: '/recharge/record',
		method: 'POST',
		data
	})
}

export function withdrawRecord(data) {
	return request({
		url: '/withdraw/record',
		method: 'POST',
		data
	})
}

export function getMoneyLog(data) {
	return request({
		url: '/Money/getMoneyLog',
		method: 'POST',
		data
	})
}