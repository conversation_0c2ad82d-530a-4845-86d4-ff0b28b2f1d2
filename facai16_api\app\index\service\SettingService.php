<?php
namespace app\index\service;


use app\common\repository\SystemUserLoginRepository;
use app\common\utils\Result;


/**
 * 配置服务
 */
class SettingService
{

    /**
     * 一个配置
     */
    public function getOne(string $key): array
    {
        $SystemSetRepo  = new SystemUserLoginRepository();

        $data['value']  = $SystemSetRepo->value($key);

        return Result::success($data);
    }


    /**
     * 更多
     * @param array $keys
     * @return array
     */
    public function getMore(array $keys): array
    {

        $SystemSetRepo  = new SystemUserLoginRepository();

        $data           = [];

        foreach ($keys as $key)
        {
            $data[$key]  = $SystemSetRepo->value($key);
        }

        return Result::success($data);
    }

}