<?php
namespace app\index\service\traits;

use app\common\define\Raffle;
use app\common\repository\RaffleLogRepository;
use app\common\repository\UserRepository;

/**
 * 抽奖逻辑
 */
trait RaffleTrait
{
    /**
     * 抽奖逻辑
     * @return int
     */
    public function _draw(): int
    {

        $Prize = function ($prize,$max)
        {
            $UserRepo           = new UserRepository();
            $RaffleLogRepo      = new RaffleLogRepository();
            $user               = $UserRepo->userByHeader();

            $where      = [];
            $where[]    = ['uid', '=', $user['id']];
            $where[]    = ['create_time', '>', strtotime('- 60 days')];
            $where[]    = ['raffle_id', '=', $prize];

            $firstPrize = $RaffleLogRepo->findByCondition($where,'*',['id'=>'desc']);

            $where      = [];
            $where[]    = ['uid', '=', $user['id']];

            if (empty($firstPrize))
            {
                $where[]    = ['create_time', '>', strtotime('- 60 days')];
            }
            else
            {
                $where[]    = ['create_time', '>', $firstPrize['create_time']];
            }

            $prizes = $RaffleLogRepo->countByCondition($where);


            if ($prizes >= $max)
            {
                return $prize;
            }
            else
            {
                return 0;
            }
        };

        $prizes = [
            Raffle::FIRST_PRIZE  => 85,
            Raffle::SECOND_PRIZE => 55,
            Raffle::THIRD_PRIZE  => 25,
        ];


        foreach ($prizes as $key => $value)
        {
            $prs = $Prize($key, $value);

            if ($prs)
            {
                return $prs;
            }
        }

        return  Raffle::PARTICIPATION_AWARD;
    }
}