{"name": "backend-manage-project", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --node-env development", "dev:qs": "vue-cli-service serve --node-env qiangsheng", "dev-mock": "vue-cli-service serve --node-env mock", "serve": "vue-cli-service serve", "build:facai": "vue-cli-service build --node-env facai", "build": "vue-cli-service build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.8.1", "core-js": "^3.8.3", "element-plus": "^2.9.4", "mockjs": "^1.1.0", "moment": "^2.30.1", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "less": "^4.0.0", "less-loader": "^8.0.0"}}