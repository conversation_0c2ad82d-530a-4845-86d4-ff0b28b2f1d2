<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use think\facade\Db;
use think\facade\Log;
use think\queue\Job;


/**
 * 升级
 * Class TimingTaskJob
 * @package app\job
 */
class LevelUpJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $uid;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->uid        = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }

    }


    /**
     * 执行
     */
    protected function exec()
    {
        $uid                = $this->uid;

        $UserRepo           = new UserRepository();

        $userInfo           = $UserRepo->findById($uid);

        if (empty($userInfo))
        {
            return;
        }

        $today              = strtotime(date('Y-m-d 00:00:00'));

        $LevelRepo          = new LevelRepository();

        $where              = [];
        $where[]            = ['id', '>', $userInfo['level']];
        $level              = $LevelRepo->findByCondition($where,'*', ['id' => 'asc']);

        if (empty($level))
        {
            return;
        }

        $PaymentRepo = new PaymentRepository();
        $where   = [];
        $where[] = ['status', '=', 0];
        $where[] = ['create_time', '>=', $today];
        $where[] = ['uid', '=', $uid];

        $recharge = $PaymentRepo->sumByCondition($where, 'amount_real');


        $where   = [];
        $where[] = ['create_time', '>=', $today];
        $where[] = ['uid', '=', $uid];

        $ItemOrderRepo = new ItemOrderRepository();

        $invest        = $ItemOrderRepo->sumByCondition($where, 'amount');

        $where   = [];
        $where[] = ['status', '=', 0];
        $where[] = ['create_time', '>=', $today];
        $where[] = ['uid', '=', $uid];

        $recharge2    = $PaymentRepo->findByCondition($where,'*', ['id' => 'desc']);
        $lastRecharge = $recharge2['amount_real'] ?? 0;

        $MoneyLogRepo = new MoneyLogRepository();

        $where   = [];
        $where[] = ['create_time', '>=', $today];
        $where[] = ['uid', '=', $uid];
        $where[] = ['class_id', '=', MoneyClass::REINVESTMENT_TRANSFER_IN];
        $fuTou   = $MoneyLogRepo->sumByCondition($where, 'amount');

        $where   = [];
        $where[] = ['create_time', '>=', $today];
        $where[] = ['uid', '=', $uid];

        $invest2    = $ItemOrderRepo->findByCondition($where,'*', ['id' => 'desc']);

        $lastInvest = $invest2['amount'] ?? 0;

        Log::channel('job')->info($userInfo['phone'] . '----' . $userInfo['username']);
        Log::channel('job')->info('[' . $fuTou . ']----[' . $recharge . ']---[' . $lastRecharge . ']');

        if ($recharge + $fuTou >= $level['recharge_total'] || $invest >= $level['invest_total'] || $lastRecharge + $fuTou >= $level['recharge'] || $lastInvest >= $level['invest'])
        {

            Db::startTrans();

            try {

                $update = [
                    'level'         => $level['id'],
                    'level_name'    => $level['title'],
                    'update_time'   => time()
                ];

                $res          = $UserRepo->updateById($uid, $update);

                if (!$res)
                {
                    Db::rollback();
                    return;
                }


                $LevelLogRepo = new LevelLogRepository();


                $insert  = [
                    'uid'           => $uid,
                    'username'      => $userInfo['username'],
                    'phone'         => $userInfo['phone'],
                    'is_test'       => $userInfo['is_test'],
                    'lv_id'         => $level['id'],
                    'lv_name'       => $level['title'],
                    'desc'          => '从' . $userInfo['level_name'] . '升级到' . $level['title'],
                    'create_time'   => time(),
                    'update_time'   => time()
                ];


                $res = $LevelLogRepo->inserts($insert);

                if (!$res)
                {
                    Db::rollback();
                    return;
                }


                // 提交事务
                Db::commit();

            } catch (\Exception $exception)
            {
                // 回滚事务
                Db::rollback();

                Record::exception('job', $exception);
            }
        }

    }


    public function failed(array $data)
    {
        // ...任务达到最大重试次数后，失败了
    }

    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('LevelUp:' . $this->uid,20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('LevelUp:' . $this->uid);
    }


}

