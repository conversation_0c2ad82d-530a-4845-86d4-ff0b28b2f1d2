<?php
namespace app\index\service;

use app\common\repository\SharesLogRepository;
use app\common\repository\SharesRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use app\common\utils\Uri;

/**
 * 股权
 */
class SharesService
{
    /**
     * 我的股权
     * @return array
     */
    public function myShare(): array
    {
        $UserRepo     = new UserRepository();
        $user         = $UserRepo->userByHeader();
        $where   = [];
        $where[] = ['uid', '=', $user['id']];
        $Shares  = new SharesLogRepository();
        $data    = $Shares->paginates($where);

        return Result::success($data);
    }


    public function amount($id): array
    {

        $where   = [];
        $where[] = ['class_id', '=', $id];
        $where[] = ['status', '=', 0];
        $Shares  = new SharesRepository();
        $info    = $Shares->sumByCondition($where);

        $data = [
          'amount' => $info,
        ];

        return Result::success($data);
    }

    /**
     * 股权
     * @return array
     */
    public function shares(): array
    {
        $SharesLogRepo = new SharesRepository();
        $data = $SharesLogRepo->paginates();


        foreach ($data['data'] as &$v)
        {
            $v['img'] = Uri::file($v['img']);
        }

        return Result::success($data);
    }

    /**
     * @param $id
     * @return array
     */
    public function info($id):array
    {
        $SharesLogRepo  = new SharesRepository();

        $data           = $SharesLogRepo->findById($id);

        return Result::success($data);
    }


    /**
     * 默认
     * @return array
     */
    public function default(): array
    {
        $SharesLogRepo  = new SharesRepository();
        $data           = $SharesLogRepo->findById(1);

        return Result::success($data);
    }

    /**
     * 证书
     * @return array
     */
    public function certificate(): array
    {
        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();

        $where          = [];
        $where[]        = ['uid', '=', $user['id']];
        $SharesLogRepo  = new SharesLogRepository();
        $data           = $SharesLogRepo->findByCondition($where);

        return Result::success($data);
    }

}
