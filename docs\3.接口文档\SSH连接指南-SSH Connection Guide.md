# SSH连接开发服务器指南

## 服务器信息
- **服务器IP**: *************
- **宝塔面板**: http://*************:33565/site/php
- **默认SSH端口**: 22 (可能被修改)

## 1. 通过宝塔面板获取SSH信息

### 1.1 登录宝塔面板
访问: http://*************:33565
使用管理员账号登录

### 1.2 查看SSH配置
1. 进入 **安全** 菜单
2. 查看 **SSH安全** 设置
3. 记录SSH端口号（默认22，可能被修改为其他端口）

### 1.3 查看系统用户
1. 进入 **文件** 管理
2. 查看 `/etc/passwd` 了解系统用户
3. 或在 **终端** 中执行 `cat /etc/passwd`

## 2. SSH连接方式

### 2.1 使用root用户连接（推荐）
```bash
# 默认端口22
ssh root@*************

# 如果SSH端口被修改（例如改为2022）
ssh -p 2022 root@*************
```

### 2.2 使用其他用户连接
```bash
# 如果有其他用户（如www、ubuntu等）
ssh username@*************
ssh -p 端口号 username@*************
```

### 2.3 Windows用户连接方式
```powershell
# 使用Windows内置SSH客户端
ssh root@*************

# 或使用PuTTY
# Host: *************
# Port: 22 (或其他端口)
# Connection Type: SSH
```

## 3. 常见SSH端口
宝塔面板常用的SSH端口：
- 22 (默认)
- 2022
- 8022
- 39000-40000 (随机端口)

## 4. 如果不知道SSH密码

### 4.1 通过宝塔面板重置
1. 登录宝塔面板
2. 进入 **安全** -> **SSH安全**
3. 修改SSH密码

### 4.2 创建SSH密钥对（推荐）
1. 在宝塔面板终端中执行：
```bash
# 生成密钥对
ssh-keygen -t rsa -b 4096

# 查看公钥
cat ~/.ssh/id_rsa.pub
```

2. 将公钥添加到本地机器的 `~/.ssh/authorized_keys`

## 5. 测试连接
```bash
# 测试SSH连接
ssh -v root@*************

# 测试特定端口
ssh -v -p 2022 root@*************
```
