<?php
namespace app\admin\controller;

use app\admin\service\RaffleBigService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;


/**
 * 抽大奖
 */
class RaffleBig
{
    /**
     * 奖品列表
     * @return Json
     */
    public function getRaffleLists(): Json
    {
        $RaffleService  = new RaffleBigService();
        $data           = $RaffleService->getRaffleLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取奖品信息
     * @return Json
     */
    public function getRaffleInfo(): Json
    {
        $id            = Request::param('id',0);
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->getRaffleInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加奖品
     * @return Json
     */
    public function addRaffle(): Json
    {
        $param         = Request::param();
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->addRaffle($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新奖品
     * @return Json
     */
    public function updateRaffle(): Json
    {
        $param         = Request::param();
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->updateRaffle($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除奖品
     * @return Json
     */
    public function deleteRaffle(): Json
    {
        $id            = Request::param('id',0);
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->deleteRaffle($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 用户抽奖记录
     * @return Json
     */
    public function getRaffleLogLists(): Json
    {
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->getRaffleLogLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 用户抽奖记录
     * @return Json
     */
    public function deleteRaffleLogLists(): Json
    {
        $id            = Request::param('id',0);
        $RaffleService = new RaffleBigService();
        $data          = $RaffleService->deleteRaffleLogLists($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}