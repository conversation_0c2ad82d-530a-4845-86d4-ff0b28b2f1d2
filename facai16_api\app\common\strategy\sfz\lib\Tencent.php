<?php

namespace app\common\strategy\sfz\lib;

use app\common\strategy\sfz\SfzInterface;
use app\common\utils\Result;
use think\facade\Config;
use think\facade\Request;


class Tencent implements SfzInterface
{
    protected $config;

    public function __construct()
    {
        $this->config    = Config::get('serve_sfz.strategy.Tencent');
    }

    /**
     * 国际腾讯云实名验证  https://cloud.tencent.com/document/product/1007/33188
     * @param array $data
     * @return array
     */
    public function send(array $data): array
    {
        $data = [
            'IdCard' => $data['idcard'],
            'Name'   => $data['name']
        ];

        $header           = [
            'Authorization: ' . $this->shiming($data),
            'Content-Type: application/json',
            'Host: faceid.tencentcloudapi.com',
            'X-TC-Action: IdCardVerification',
            'X-TC-Version: 2018-03-01',
            'X-TC-Timestamp: ' . Request::time(),
        ];


        $result = $this->request($data, $header);

        //"{"Response":{"Description":"非法姓名（长度、格式等不正确）","RequestId":"7b208d1a-4013-43eb-a2fe-d88e60265310","Result":"-3"}}"

        $result     = json_decode($result,true);

        $response   = $result['Response'] ?? '';


        if (isset($response['Result']) && $response['Result'] != 0)
        {
            return  Result::fail($response['Description']);
        }

        return Result::success();

    }

    /**
     * 签名 https://cloud.tencent.com/document/api/1007/31324
     * @param array $data
     * @return string
     */
    protected function shiming(array $data)
    {
        $secretId   = $this->config['secret_id'];
        $secretKey  = $this->config['secret_key'];
        $host       = $this->config['host'];
        $service    = $this->config['service'];
        $action     = $this->config['action'];
        $timestamp  = Request::time();
        $algorithm  = $this->config['algorithm'];

        // step 1: build canonical request string
        $httpRequestMethod      = "POST";
        $canonicalUri           = "/";
        $canonicalQueryString   = "";

        $canonicalHeaders = implode("\n", [
            "content-type:application/json",
            "host:" . $host,
            "x-tc-action:" . strtolower($action),
            ""
        ]);


        $signedHeaders = implode(";", [
            "content-type",
            "host",
            "x-tc-action",
        ]);

        $payload                = json_encode($data,256);
        $hashedRequestPayload   = hash("SHA256", $payload);

        $canonicalRequest = $httpRequestMethod."\n"
            .$canonicalUri . "\n"
            .$canonicalQueryString . "\n"
            .$canonicalHeaders . "\n"
            .$signedHeaders . "\n"
            .$hashedRequestPayload;

        // step 2: build string to sign
        $date = gmdate("Y-m-d", $timestamp);

        $credentialScope            = $date . "/" . $service . "/tc3_request";
        $hashedCanonicalRequest     = hash("SHA256", $canonicalRequest);
        $stringToSign = $algorithm . "\n"
            .$timestamp . "\n"
            .$credentialScope . "\n"
            .$hashedCanonicalRequest;

        // step 3: sign string
        $secretDate     = hash_hmac("SHA256", $date, "TC3".$secretKey, true);
        $secretService  = hash_hmac("SHA256", $service, $secretDate, true);
        $secretSigning  = hash_hmac("SHA256", "tc3_request", $secretService, true);
        $signature      = hash_hmac("SHA256", $stringToSign, $secretSigning);

        // step 4: build authorization
        return $algorithm
            ." Credential=".$secretId."/".$credentialScope
            .", SignedHeaders=".$signedHeaders.", Signature=".$signature;

    }

    /**
     * 请求
     * @param array $data
     * @param array $head
     * @return bool|string
     */
    public function request(array $data = [], array $head = [])
    {
        $url         =$this->config['url'];

        $data_string = json_encode($data,JSON_UNESCAPED_UNICODE);
        $curl_con    = curl_init();
        curl_setopt($curl_con, CURLOPT_URL,$url);
        curl_setopt($curl_con, CURLOPT_HEADER, false);
        curl_setopt($curl_con, CURLOPT_POST, true);
        curl_setopt($curl_con, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl_con, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($curl_con, CURLOPT_HTTPHEADER,$head);
        curl_setopt($curl_con, CURLOPT_POSTFIELDS, $data_string);
        $res = curl_exec($curl_con);
        $status = curl_getinfo($curl_con);
        curl_close($curl_con);

        if (isset($status['http_code']) && $status['http_code'] == 200)
        {
            return $res;
        } else
        {
            return false;
        }
    }


}
