<?php
namespace app\admin\controller;


use app\admin\service\LoginService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 登入
 */
class Login
{

    /**
     * 登入
     * @return Json
     */
    public function login(): <PERSON><PERSON>
    {
        $params = Request::only([
            'username' => '',
            'password' => '',
            'vscode'   => ''
        ]);

        $LoginService   = new LoginService();
        $data           = $LoginService->login($params['username'], $params['password'], $params['vscode']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);

    }



}