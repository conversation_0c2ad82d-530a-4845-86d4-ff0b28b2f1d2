<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use app\common\utils\Uri;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * QiuTianPay
 */
class QiuTianPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'QiuTianPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'pay_memberid'     => $this->config['mch_id'],
            'pay_orderid'      => $data['orderNo'],
            'pay_applydate'    => date('Y-m-d H:i:s'),
            'pay_bankcode'     => $data['channel'],
            'pay_amount'       => $data['money'],
            'pay_notifyurl'    => Uri::file('/api/notify?ZhiFuTongDao=' . self::CHANNEL),
            'pay_callbackurl'  => Uri::file('/api/notify?ZhiFuTongDao=' . self::CHANNEL),
        ];


        $param['pay_md5sign']  = $this->signature($param);
        $result                = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);


        $uri            = '';

        if(isset($result['code'])  && $result['code'] == 200)
        {
            $uri = $result['data']['jumpurl'] ?? '';
        }

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

        //$param = '{"memberid":"9440","orderid":"PAYVBHECCJGBDDBIFI","status":"success","amount":"500","sign":"337B550B9FC72EBE601772D170E841C9"}';
//        $param     = json_decode($param,true);

        $sign      = $param['sign'];

        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['orderid'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value . '&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return strtoupper($sign);
    }

}