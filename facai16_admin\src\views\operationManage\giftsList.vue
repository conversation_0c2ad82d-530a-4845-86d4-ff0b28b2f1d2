<template>
  <adminTable
    :hideSeachButton="true"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="用户ID" width="130" />
      <el-table-column prop="username" label="用户名" width="160" />
      <el-table-column prop="gift_id" label="礼品id" width="130" />
      <el-table-column prop="title" label="标题" width="130" />
      <el-table-column prop="amount" label="金额" width="130" />
      <el-table-column prop="create_at" label="登录时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"))
const searchForm = ref({
  ip: "",
});

onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/SignIn/getSignInGiftLogLists",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
