<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use think\facade\Db;
use think\queue\Job;

/**
 * 项目奖励
 * Class ItemGiftJob
 * @package app\job
 */
class ItemGiftJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $id;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;


    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->id         = $data;


        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Record::exception('job', $exception);
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec()
    {
        $ItemOrderRepo  = new ItemOrderRepository();
        $order          = $ItemOrderRepo->findById($this->id);

        //无数据
        if (empty($order))
        {
            return;
        }

        //数据已经派发 || $order['is_coupon']
        if ($order['item_status'] != 0 )
        {
            return;
        }


        $UserRepo = new UserRepository();
        $user     = $UserRepo->findById($order['uid']);

        //无数据
        if(!$user)
        {
            return;
        }



        Db::startTrans();

        try {

            //账变
            $MoneyLogRepo = new MoneyLogRepository();

            //数据统计
            $UserInfoRepo = new UserInfoRepository();

            if ($order['gift_bonus'] > 0)
            {
                $res  = $MoneyLogRepo->fund($user['id'], $order['gift_bonus'],MoneyClass::ALLOWANCE,(string) $order['id'],'项目购买奖励:+' . $order['gift_bonus'] .'元');

                if ($res['code'])
                {
                    Record::log('job','项目够买失败', $order);
                    Db::rollback();
                    return;
                }

                $res  = $UserInfoRepo->statistic($user['id'], ['bonus_money' => $order['gift_bonus'], 'bonus_num' => 1]);

                if ($res['code'])
                {
                    Record::log('job','项目够买奖励累积失败', $order);
                    Db::rollback();
                    return;
                }

            }

            if ($order['gift_points'] > 0)
            {

                $res  = $MoneyLogRepo->point($user['id'], $order['gift_points'],MoneyClass::POINTS_ADD, (string) $order['id'],'项目购买积分奖励:+' . $order['gift_points']);

                if ($res['code'])
                {
                    Record::log('job','项目够买积分奖励');
                    Db::rollback();
                    return;
                }
            }


            // 提交事务
            Db::commit();

        } catch (\Exception $exception)
        {
            // 回滚事务
            Db::rollback();
            Record::exception('job', $exception);
        }
    }




    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('ItemMoneyJob:' . $this->id,10);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('ItemMoneyJob:' . $this->id);
    }

}

