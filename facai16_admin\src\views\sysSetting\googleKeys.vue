<template>
  <div class="pages">
    <el-form ref="formRef" class="form" label-width="150px" :model="form">
      <el-form-item label="谷歌密钥" prop="imgUrl">
        <el-input style="width: 60%" v-model="form.googleKey" disabled />
      </el-form-item>
      <el-form-item label="二维码">
        <img :src="form.QRcode" alt="" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          v-loading="loading"
          @click="submitForm(formRef)"
        >
          <template #loading>
            <div class="custom-loading">
              <svg class="circular" viewBox="-10, -10, 50, 50">
                <path
                  class="path"
                  d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          "
                  style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"
                />
              </svg>
            </div>
          </template>
          刷新密钥</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, onMounted } from "vue";
import { UploadFilled } from "@element-plus/icons-vue";
const formRef = ref();

const form = ref({
  QRcode: "",
  googleKey: "",
});

const { proxy } = getCurrentInstance();

onMounted(() => {
  // submitForm();
  getgoogleQrcode()
});

const getCode = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/index/setting?key=google",
  });
  
  if (res.code == 0) {
    form.value.googleKey = res.data.val;
  }
};

const getgoogleQrcode = async () => {
  const res = await proxy.$http({
    type: "get",
    url: "/index/googleQrcode",
  });
  
  if (res.code == 0) {
    form.value.QRcode = res.data.val;
  }
};

const loading = ref(false);

const submitForm = async () => {
  loading.value = true;

  const res = proxy.$http({
      method: 'post',
      url: 'index/googleEdit',
      data: {
          key: form.value.googleKey
      }
  })
 

  await getCode();
  await getgoogleQrcode();
  loading.value = false;

  console.log(res)
};
</script>

<style lang="less" scoped>
.form {
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: flex-start;
  width: 100%;
}
.upload-demo {
  width: 60%;
}
::v-deep {
  .el-form-item {
    width: 100%;
  }
}

.el-button .custom-loading .circular {
  margin-right: 6px;
  width: 18px;
  height: 18px;
  animation: loading-rotate 2s linear infinite;
}
.el-button .custom-loading .circular .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-button-text-color);
  stroke-linecap: round;
}
</style>
