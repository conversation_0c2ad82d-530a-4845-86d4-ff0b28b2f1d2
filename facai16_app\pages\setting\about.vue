<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="  scroll-view">
				<view class=" ">

					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								关于我们
							</view>
						</template>
					</uv-navbar>

				</view>


				<view class="f-pa left-34 right-34 bottom-137 color-#fff">
					<box-box :blue="true">
						<view class="flex py-48 px-20">
							<view class="flex1 fcc-h" @click="toRouter('/pages/setting/qualifications')">
								<image src="/static/about/1.png" mode="aspectFit" class="block size-90"></image>
								<view class="t-30 lh-42 mt-15">
									公司资质
								</view>
							</view>
							<view class="flex1 fcc-h" @click="toRouter('/pages/setting/introduction')">
								<image src="/static/about/2.png" mode="aspectFit" class="block size-90"></image>
								<view class="t-30 lh-42 mt-15">
									公司介绍
								</view>
							</view>
							<view class="flex1 fcc-h" @click="open">
								<image src="/static/about/3.png" mode="aspectFit" class="block size-90"></image>
								<view class="t-30 lh-42 mt-15">
									商业企划书
								</view>
							</view>
						</view>
					</box-box>
				</view>
				<uv-modal ref="modal" title="商业企划书" :show-confirm-button="false" show-cancel-button="true"
					cancelText='关闭'>
					<view class="slot-content">
						<view class="text-center">
							<navigator url="/pages/setting/plan1">
								<uv-text type="primary" text="<<工业物联网白皮书>>"></uv-text>
							</navigator>
							<navigator url="/pages/setting/plan2">
								<uv-text type="primary" text="<<云联聚科技有限公司白皮书>>"></uv-text>
							</navigator>
						</view>
					</view>
				</uv-modal>
			</view>
		</view>



	</view>
</template>

<script>
	export default {

		data() {
			return {

			};
		},
		onHide() {
			this.$refs.modal.close()
		},

		methods: {
			open() {
				this.$refs.modal.open();
			},
			toRouter(url) {
				uni.navigateTo({
					url: url
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/about/aboutimg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}
</style>