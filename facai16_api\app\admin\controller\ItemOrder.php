<?php
namespace app\admin\controller;

use app\admin\service\ItemOrderService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 项目订单
 */
class ItemOrder
{

    /**
     * 获取订单列表
     * @return Json
     */
    public function getItemOrderLists(): Json
    {
        $params             = Request::only([
            'username' =>'',
            'phone' => '',
            'order_no' => '',
            'item_name' => '',
            'item_status' => '',
            'starttime' => '',
            'endtime' => '',
            'is_test' => ''
        ]);

        $ItemOrderService    = new ItemOrderService();
        $data                = $ItemOrderService->getItemOrderLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}