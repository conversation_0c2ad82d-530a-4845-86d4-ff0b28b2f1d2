<?php

namespace app\admin\service;

use app\common\repository\SystemSetRepository;
use app\common\repository\SystemUserLoginRepository;
use app\common\repository\SystemUserRepository;
use app\common\utils\Record;
use app\common\utils\Result;
use PHPGangsta_GoogleAuthenticator;
use think\facade\Db;
use think\facade\Request;
use Zhuzhichao\IpLocationZh\Ip;

/**
 * 登入
 */
class LoginService
{

    /**
     * 登入
     * @param $username
     * @param $password
     * @param $vscode
     * @return array
     */
    public function login($username, $password, $vscode):array
    {

        $time           = Request::time();

        $SystemUserRepo = new SystemUserRepository();

        $where      = [];
        $where[]    = ['username', '=', $username];
        $where[]    = ['password', '=', $password];
        $user       = $SystemUserRepo->findByCondition($where);

        if (!$user)
        {
            return Result::fail('账号或者密码错误');
        }

        $SystemSetRepo       = new SystemSetRepository();

        $google              = $SystemSetRepo->valueByCondition(['key' => 'google'],'val');
        $GoogleAuthenticator = new PHPGangsta_GoogleAuthenticator();

        $googleCheck         = $GoogleAuthenticator->verifyCode($google, $vscode);
        $env                 = env('APP_ENV');

        if ($env == 'prod' && !$googleCheck && env('GOOGLE_AUTH',false))
        {
            return Result::fail('谷歌验证码错误');
        }

        $ip             = Request::ip();
        $ipAddress      = join(',',  array_filter(Ip::find($ip)));
        $SystemUserRepo = new SystemUserRepository();

        Db::startTrans();

        try {

            $update = [
                'login_time'  => $time,
                'login_ip'    => Request::ip(),
                'ip_address'  => $ipAddress,
                'login_agent' => $_SERVER['HTTP_USER_AGENT'],
                'token'       => md5($time . $user['id'])
            ];

            $res = $SystemUserRepo->updateById($user['id'], $update);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('登入失败');
            }


            $SystemUserLoginRepo = new SystemUserLoginRepository();

            $insert = [
                'uid'         => $user['id'],
                'username'    => $user['username'],
                'login_ip'    => Request::ip(),
                'login_agent' => $_SERVER['HTTP_USER_AGENT'],
                'ip_address'  => $ipAddress
            ];

            $res = $SystemUserLoginRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('登入失败');
            }


            Db::commit();

            return Result::success($update,'登录成功');
        }
        catch (\Exception $e)
        {
            Db::rollback();
            Record::exception('admin', $e);
            return Result::fail('登入失败');
        }

    }

}