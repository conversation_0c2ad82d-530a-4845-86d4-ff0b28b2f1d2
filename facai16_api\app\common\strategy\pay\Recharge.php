<?php
namespace app\common\strategy\pay;

use app\common\cache\RedisLock;
use app\common\jobs\LevelUpJob;
use app\common\model\MoneyClass;
use app\common\model\Payment;
use app\common\model\UserState;
use app\common\repository\MoneyLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Record;
use think\facade\Db;
use think\facade\Request;

/**
 * 充值
 */
class Recharge
{

    /**
     * @param string $orderId
     * @return bool
     */
    public function handle(string $orderId): bool
    {

        $RedisLock       = new RedisLock();
        $status          = $RedisLock->lock('Recharge:' . $orderId);

        if (empty($status))
        {
            return false;
        }


        $PaymentRepo = new PaymentRepository();
        $order       = $PaymentRepo->findByCondition(['order_no' => $orderId]);

        if (!$order)
        {
            $RedisLock->unLock('Recharge:' . $orderId);
            return false;
        }

        if ($order['status'] != Payment::STATUS['REVIEW'])
        {
            $RedisLock->unLock('Recharge:' . $orderId);
            return true;
        }

        $UserInfoRepo       = new UserInfoRepository();
        $UserStateRepo      = new UserStateRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $UserRepo           = new UserRepository();
        $userInfo           = $UserInfoRepo->findByCondition(['uid' => $order['uid']]);
        $topId              = $UserRepo->valueByCondition(['id'=> $order['uid']], 'v1_id');

        $SystemSetRepo      = new SystemSetRepository();

        Db::startTrans();

        try {


            $update = [
                'status'        => Payment::STATUS['SUCCESS'],
                'update_time'   => Request::time(),
            ];

            $res    = $PaymentRepo->updateById($order['id'], $update);

            if (!$res)
            {

                Db::rollback();
                $RedisLock->unLock('Recharge:' . $orderId);
                return false;
            }

            //首存奖励
            if ($userInfo['recharge_money'] == 0)
            {
//                if ($topId)
//                {
//                    $raffle  = $SystemSetRepo->valueByCondition(['key' => 'invite_raffle'], 'val');
//
//                    if ($raffle)
//                    {
//                        $res = $MoneyLogRepo->raffle($topId, $raffle,MoneyClass::RAFFLE_ADD, $orderId,"直聘奖励:获得抽奖{$raffle}次");
//
//                        if ($res['code'])
//                        {
//                            Db::rollback();
//                            return false;
//                        }
//                    }
//
//
//                    $points = $SystemSetRepo->valueByCondition(['key' => 'invite_points'], 'val');
//
//                    if ($points)
//                    {
//                        $res  = $MoneyLogRepo->point($topId, $points,MoneyClass::POINTS_ADD, $orderId,'直聘奖励:获得兑换券' . $points);
//
//                        if ($res['code'])
//                        {
//                            Db::rollback();
//                            return false;
//                        }
//                    }
//
//                    $moneys = $SystemSetRepo->valueByCondition(['key' => 'invite_moneys'], 'val');
//
//                    if ($moneys)
//                    {
//                        $res  = $MoneyLogRepo->fund($topId, $moneys,MoneyClass::INVITE_MEMBER_BONUS, $orderId,'直聘奖励:获得作业额度' . $moneys);
//
//                        if ($res['code'])
//                        {
//                            Db::rollback();
//                            return false;
//                        }
//                    }
//                }


                $bonus = $SystemSetRepo->valueByCondition(['key' => 'first_deposit_bonus'], 'val');

                if ($bonus)
                {
                    $res  = $MoneyLogRepo->fund($order['uid'], $bonus,MoneyClass::FIRST_DEPOSIT_BONUS, $orderId,'首存奖励:获得' . $bonus);

                    if ($res['code'])
                    {
                        Db::rollback();
                        return false;
                    }
                }
            }



            $money  = $order['amount_real'];


            $update = [
                'recharge_money' => $money,
                'recharge_num'   => 1,
                'update_time'    => Request::time(),
            ];

            $res    = $UserInfoRepo->statistic($order['uid'], $update);

            if (!$res)
            {

                Db::rollback();
                $RedisLock->unLock('Recharge:' . $orderId);
                return false;
            }

            $update = [
                'is_valid_user'   => UserState::IS_VALID_USER['VALID_ACCOUNT'],
                'update_time'     => Request::time(),
            ];

            $res = $UserStateRepo->updateByCondition(['uid' => $order['uid']], $update);

            if (!$res)
            {

                Db::rollback();
                $RedisLock->unLock('Recharge:' . $orderId);
                return false;
            }

            $txt = $order['class_id'] == 4 ? 'USDT' : '元';
            //写入日志
            $res = $MoneyLogRepo->fund($order['uid'], $money,MoneyClass::RECHARGE, $orderId, "存款：获得" . $money . $txt);

            if ($res['code'])
            {
                Db::rollback();
                $RedisLock->unLock('Recharge:' . $orderId);
                return false;
            }

            // 提交事务
            Db::commit();

            queue(LevelUpJob::class,$order['uid']);

            //首充返利
//            if($user['recharge_money'] == 0)
//            {
//                queue(FirstRechargeGifJob::class, $push);
//                queue(FirstRechargeJob::class, $push);
//            }


            return true;

        } catch (\Exception $e)
        {
            $RedisLock->unLock('Recharge:' . $orderId);

            Record::exception('recharge', $e);
            // 回滚事务
            Db::rollback();
            return false;
        }
    }
}