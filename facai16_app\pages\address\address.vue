<template>
	<view>
		<view class="scroll-view page-1">
			<view class="flex-page bg scroll-view">
				<view class="flex-scroll-fixed bg-fff">


					<uv-navbar title="地址管理" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back-g.png" mode="aspectFit" size-48></image>
							</view>
						</template>
						<template #right>
							<view>
								<image src="/static/my/kefu.png" mode="aspectFit" size-48></image>
							</view>
						</template>
					</uv-navbar>

				</view>
				<view class="flex1 f-pr" v-if="!addressInfo">
					<scroll-view scroll-y="true" class="scroll-view fcc-h">
						<view class="py-100">
							<no-data>暂无收货地址</no-data>
						</view>
					</scroll-view>
				</view>


				<view class="flex1 f-pr" v-else>
					<scroll-view scroll-y="true" class="scroll-view ">
						<view class="address-list">
							<uni-swipe-action>
								<uni-swipe-action-item v-if="addressInfo">
									<view class="address-item px-30 py-40 flex">
										<!-- <view @click="checkedIndex = index">
											<view class="check" :class="{on:checkedIndex == index}"></view>
										</view> -->
										<view @click="checkedIndex = index" class="flex1">
											<view class="fc">
												<view class="name">
													{{userInfo.sfz_name}}
												</view>
												<view class="tel">
													{{userInfo.phone}}
												</view>
												<view class="default" v-if="addressInfo.default">
													默认
												</view>
											</view>
											<view class="p">
												{{addressInfo.address_place}}
											</view>
										</view>
										<view @click.stop=''>
											<view class="edit" @click="toEdit"></view>
										</view>
									</view>
									<!-- <template v-slot:right>
										<view @click="click(item.id)" style="height: 100%;" class="fcc px-35 bg-#f00">
											<uv-icon name="trash-fill" size="20" color="#fff"></uv-icon>
										</view>
									</template> -->
								</uni-swipe-action-item>
							</uni-swipe-action>
						</view>
					</scroll-view>
				</view>
				<view class="bg-fff py-10 px-40" v-if="!addressInfo">
					<view class="btn-full fcc" @click="openUrl('/pages/address/add')">
						新增收货地址
					</view>
					<view class="btn-area"></view>
				</view>

			</view>
		</view>
		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				checkedIndex: 0,
			};
		},
		computed: {
			...mapState(['userInfo', 'addressInfo']),
		},
		methods: {
			toEdit(item) {
				uni.navigateTo({
					url: `/pages/address/edit?address_id=${item.id}`,
				})
			},
			click() {
				uni.showModal({
					title: '删除地址',
					content: '确定要删除该地址吗？',
					cancelColor: "#999",
					confirmColor: "#222",
					success: res => {
						if (res.confirm) {
							uni.showLoading()
							uni.showToast({
								title: '删除成功'
							})
						}

					},
					fail: res => {
						uni.showToast({
							title: '取消'
						})
					},
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.bg {
		background: #F7F8FE;
	}



	.address-item {
		background: #fff;
		position: relative;

		&:after {
			content: "";
			display: block;
			left: 30rpx;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #E6E7EB;
			position: absolute;
		}

		.check {
			width: 40rpx;
			height: 40rpx;
			background: url(/static/check-1-g.png) center no-repeat;
			background-size: contain;
			margin-right: 8px;

			&.on {
				background-image: url(/static/check1-on.png);
			}
		}

		.edit {
			margin-left: 15px;
			width: 40rpx;
			height: 40rpx;
			background: url(/static/pen.png) center no-repeat;
			background-size: contain;
		}

		.p {
			font-weight: 400;
			font-size: 26rpx;
			color: #222222;
			line-height: 37rpx;

			margin-top: 8rpx;
		}

		.name {
			font-weight: 600;
			font-size: 30rpx;
			color: #222222;
			line-height: 42rpx;
			margin-right: 24rpx;
		}

		.tel {
			font-weight: 400;
			font-size: 30rpx;
			color: #222222;
			line-height: 42rpx;
			margin-right: 10rpx;
		}

		.default {
			padding: 0 8rpx;
			height: 38rpx;
			background: #407CEE;
			border-radius: 4rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #FFFFFF;
			line-height: 38rpx;
		}
	}



	.address-input-row {
		background: #fff;
		position: relative;

		.left {
			min-width: 168rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #222222;
			line-height: 45rpx;
		}

		input {
			font-weight: 400;
			font-size: 30rpx;
			color: #222;
			line-height: 80rpx;
			height: 80rpx;
			display: block;
			width: 95%;
		}

		&:after {
			content: "";
			display: block;
			left: 30rpx;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #E6E7EB;
			position: absolute;
		}
	}



	.address-input-row:nth-last-of-type(1) {
		&:after {
			display: none;
		}
	}
</style>