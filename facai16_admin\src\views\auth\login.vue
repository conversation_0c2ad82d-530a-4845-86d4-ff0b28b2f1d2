<template>
  <div class="pages">
    <el-form ref="formRef" class="login-form" :model="form">
      <h2>WELCOME<br />后台管理系统</h2>
      <el-form-item
        label-width="100px"
        label="登录账号"
        :rules="[
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ]"
        prop="username"
      >
        <el-input v-model="form.username" />
      </el-form-item>
      <el-form-item
        :rules="[
          {
            required: true,
            message: '请输入登录密码',
            trigger: 'blur',
          },
        ]"
        label-width="100px"
        label="登录密码"
        prop="password"
      >
        <el-input v-model="form.password" type="password" autocomplete="off" />
      </el-form-item>
      <el-form-item
        :rules="[
          {
            required: true,
            message: '请输入谷歌密钥',
            trigger: 'blur',
          },
        ]"
        label-width="100px"
        label="谷歌密钥"
        prop="vscode"
      >
        <el-input @keyup.enter="submitForm()" v-model="form.vscode" type="text" autocomplete="off" />
      </el-form-item>

      <el-form-item class="login-wrap">
        <el-button type="primary" @click="submitForm()"> 登录 </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";

import { ElNotification } from "element-plus";
import { useRouter } from "vue-router";
import store from "@/store";

const form = ref({
  username: "",
  password: "",
  vscode: "",
});

const router = useRouter();

const formRef = ref(null);

const { proxy } = getCurrentInstance();
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const res = await proxy.$http({
        method: "post",
        url: "login/login",
        data: form.value,
      });

      if (res.code == 0) {
        sessionStorage.setItem("__TOKEN", res.data.token);
        const user = await proxy.$http({
          type: "get",
          url: "/SystemUser/getSystemUserLists?username=" + form.value.username,
          params: {
            username: form.value.username,
          },
        });
        if (user.code == 0) {
            sessionStorage.setItem('currentUser', JSON.stringify(user.data.data[0]))
            store.commit("updateCurrentUser", user.data.data[0]);
        }

        setTimeout(() => {
          router.push("/userList");
        }, 200);
      } else {
        ElNotification({
          title: "Error",
          message: res.msg || "登录失败!",
          type: "error",
        });
      }
    }
  });
};
</script>

<style lang="less" scoped>
.pages {
  background-image: url(../../assets/img/login-bg.png);
  background-size: 100% 100%;
  min-width: 100%;
  min-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  flex-direction: column;
  h2 {
    width: 100%;
    font-size: 24px;
    // text-align: left;
    margin-bottom: 40px;
    color: #79bbff;
  }
  p {
    width: 100%;
    font-size: 22px;
  }
  .login-form {
    background-color: #fff;
    padding: 40px 20px 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    width: 400px;
    /deep/ .el-input {
      --el-input-width: 240px;
    }
  }
  .login-wrap {
    padding-left: 100px;
    button {
      width: 240px;
    }
  }
}
</style>
