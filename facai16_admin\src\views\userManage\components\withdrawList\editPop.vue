<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="用户名" required >
            <el-input v-model="form.username" disabled  />
        </el-form-item>
        <el-form-item label="余额" required >
            <el-input v-model="form.money" disabled />
        </el-form-item>
        <el-form-item label="类型" required>
            <el-radio-group v-model="form.status">
                <el-radio value="1">通过</el-radio>
                <el-radio value="2">拒绝</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="充值方式"  required>
            <el-input :value="getLabelByVal(form.way, withdrawTypeEnums)" disabled />
        </el-form-item>
        <el-form-item label="充值金额"  required>
            <el-input v-model="form.money" disabled />
        </el-form-item>
        <el-form-item label="汇率"  required>
            <el-input v-model="form.huilv" disabled />
        </el-form-item>
        <el-form-item label="实际金额"  required>
            <el-input v-model="form.money2"  disabled />
        </el-form-item>
        <template v-if="form.type == 1">
            <el-form-item label="收款人姓名"  required>
                <el-input v-model="form.name"  clearable />
            </el-form-item>
            <el-form-item label="银行名称"  required>
                <el-input v-model="form.bank_name"  clearable />
            </el-form-item>
            <el-form-item label="银行支行"  required>
                <el-input v-model="form.bank_branch"  clearable />
            </el-form-item>
            <el-form-item label="银行账号"  required>
                <el-input v-model="form.bank_account"  clearable />
            </el-form-item>
        </template>
        <template v-if="form.type == 2">
            <el-form-item label="USDT账号"  required>
                <el-input v-model="form.coin_account"  clearable />
            </el-form-item>
            <el-form-item label="区块链"  required>
                <el-input v-model="form.coin_blockchain"  clearable />
            </el-form-item>
        </template>
        <template v-if="form.type == 3">
            <el-form-item label="账号"  required>
                <el-input v-model="form.alipay_account"  clearable />
            </el-form-item>
            <br>
            <el-form-item label="收款码"  required>
                <img :src="form.alipay_img" alt="">
            </el-form-item>
        </template>

        <el-form-item label="备注" required>
            <el-input v-model="form.remark" clearable />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import {withdrawTypeEnums, getLabelByVal} from '@/config/enums'

const form = ref({
    username: '',
    money: '',
    status: '',
    money: '',
    way: '',
    huilv: '',
    money2: '',
    remark: '',
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>