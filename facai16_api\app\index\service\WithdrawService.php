<?php
namespace app\index\service;


use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserBankRepository;
use app\common\repository\UserRepository;
use app\common\repository\WithdrawRepository;
use app\common\utils\BCalculator;
use app\common\utils\Order;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;
use think\facade\Request;

class WithdrawService
{
    /**
     * 提现
     * @param array $params
     * @return array
     */
    public function withdraw(array $params): array
    {
        $money      = $params['money'];
        $id         = $params['id'];

        if($money <= 0 || empty($id))
        {
            return Result::fail('参数错误');
        }

        $SystemSetRepo    = new SystemSetRepository();
        $UserBankRepo     = new UserBankRepository();
        $WithdrawRepo     = new WithdrawRepository();

        $UserRepo       = new UserRepository();
        $user           = $UserRepo->userByHeader();
        $state          = $UserRepo->userStateByHeader();
        $MoneyRepo      = new MoneyRepository();
        $handlingFee    = $SystemSetRepo->valueByCondition([['key' ,'=', 'handling_fee']],'val');

        $ItemOrderRepo  = new ItemOrderRepository();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $_count     = $ItemOrderRepo->findByCondition($where);

        if($_count == 0)
        {
            return Result::fail( '产品激活账户才能提现');
        }

        $today  = date("w");

        switch ($today)
        {
            case 0:
                $day = "周日";
                break;
            case 1:
                $day = "周一";
                break;
            case 2:
                $day = "周二";
                break;
            case 3:
                $day = "周三";
                break;
            case 4:
                $day = "周四";
                break;
            case 5:
                $day = "周五";
                break;
            case 6:
                $day = "周六";
                break;
            default:
                $day = "";
                break;
        }


        $days  = explode(',', $SystemSetRepo->valueByCondition(['key'=> 'withdraw_day'],'val'));

        if (!in_array($day,$days))
        {
            return Result::fail('可取款日期为:'. join(',',$days));
        }


        if ($params['pin'] != $user['pin'])
        {
            return Result::fail( '支付密码错误！');
        }

        $period = $SystemSetRepo->valueByCondition(['key'=> 'withdraw_time'],'val');

        if($period)
        {
            $periods = explode('~', $period);

            if(strtotime(date('H:i')) < strtotime($periods[0]) || strtotime(date('H:i')) > strtotime($periods[1]))
            {
                return Result::fail('可取款时间为' . $period);
            }
        }

        //提现已被冻结，请联系生鲜管家
        if($state['ban_withdraw'])
        {
            return Result::fail('账号限制取款，请联系客服！');
        }

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['id', '=', $id];
        $bank       = $UserBankRepo->findByCondition($where);

        //请先绑定账户
        if(empty($bank))
        {
            return Result::fail('请先绑定账户');
        }

        $userMoney     = $MoneyRepo->valueByCondition(['uid' => $user['id']], 'money');
        $minWithdraw   = $SystemSetRepo->valueByCondition([['key' ,'=', 'min_withdraw']],'val');


        if ($bank['type'] == 1)
        {
            //usdt 汇率
            $huiLv      = $SystemSetRepo->valueByCondition(['key'=> 'usdt_huilv'],'val');

            //真实金额
            $money2     = BCalculator::calc($money)->mul($huiLv)->result();

            //手续费
            $fee        = BCalculator::calc($money2)->mul($handlingFee)->div(100)->result();

            //真实金额
            $amountReal = BCalculator::calc($money2)->sub($fee)->result();

            $huilv2     = BCalculator::calc($minWithdraw)->div($huiLv)->result();

            if($money < $huilv2)
            {
                return Result::fail('最少取款' . $huilv2 .'U');
            }

        }
        else
        {
            //手续费
            $fee        = BCalculator::calc($money)->mul($handlingFee)->div(100)->result();

            //真实金额
            $amountReal = BCalculator::calc($money)->sub($fee)->result();

            //真实金额
            $money2     = $money;

            if($money2 < $minWithdraw)
            {
                return Result::fail('最少取款' . $minWithdraw);
            }

            $huiLv      = 1;
        }
        

        if($money2 > $userMoney)
        {
            return Result::fail('金额不足');
        }

        $where      = [];
        $where[]    = ['uid','=', $user['id']];
        $where[]    = ['status','=', 1];

        //已有提现正在审核中
        if($WithdrawRepo->findByCondition($where))
        {
            return Result::fail('已有取款正在审核中');
        }

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['status', '=', 0];

        $last       = $WithdrawRepo->findByCondition($where,'*',['id'=> 'desc']);

        //两次提现时间不得小于24小时
        if($last && ($last['create_time'] + 24 * 3600) > time())
        {
            return Result::fail('两次取款时间不得小于24小时');
        }



        Db::startTrans();

        try {

            $remark = $bank['type'] == 1  ? '您的账户转出USDT ' . $money  : '您的账户转出RMB ' . $money . '元';

            $insert = [
                'username'          => $user['username'],
                'phone'             => $user['phone'],
                'is_test'           => $user['is_test'],
                'order_no'          => Order::uniqueNo('Draw'),
                'uid'               => $user['id'],
                'type'              => $bank['type'],
                'amount'            => $money,
                'amount_real'       => $amountReal,
                'name'              => $bank['name'],
                'bank_name'         => $bank['bank_name'],
                'bank_branch'       => $bank['bank_branch'],
                'bank_account'      => $bank['bank_account'],
                'coin_name'         => $bank['coin_name'],
                'coin_blockchain'   => $bank['coin_blockchain'],
                'coin_account'      => $bank['coin_account'],
                'alipay_account'    => $bank['alipay_account'],
                'alipay_img'        => $bank['alipay_img'],
                'wx_img'            => $bank['wx_img'],
                'create_time'       => Request::time(),
                'update_time'       => Request::time(),
                'handling_rate'     => $handlingFee,
                'handling_fee'      => $fee,
                'remark'            => $remark,
                'rate'              => $huiLv,
            ];

            $formId        = $WithdrawRepo->insertGetId($insert);
            $MoneyLogRepo  = new MoneyLogRepository();
            $phone         = substr($user['phone'],0,3).'****'.substr($user['phone'],strlen($user['phone'])-4,4);
            $_txt          = '您的'.$phone.'账号';
            $txt           = $bank['type'] == 1 ? $_txt.'账户提款 -'. $money .'USDT,手续费扣除 '. $handlingFee.'% 实际到账' . $amountReal.'元' : $_txt.'账户提款-'. $money .'元,手续费扣除 '. $handlingFee.'% 实际到账' . $amountReal.'元';
            $res           = $MoneyLogRepo->fund($user['id'], $money2,MoneyClass::WITHDRAW, $formId, $txt);

            // 6：投资明细和充值明细里面的信息提示要改为  您的131*****0000账户提款RMB***元或者多少usdt，充值明细里面也这样提示。

            //操作失败
            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }


            // 提交事务
            Db::commit();

            return Result::success();

        } catch (\Exception $e)
        {
            // 回滚事务
            Db::rollback();
            return Result::fail(Record::exception('index', $e,'WithdrawService->withdraw'));
        }
    }


    /**
     * 提现记录
     * @return array
     */
    public function record(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
        $WithdrawRepo       = new WithdrawRepository();

        $where              = [];
        $where[]            = ['uid', '=',$user['id']];
        $data               = $WithdrawRepo->paginates($where);

        return Result::success($data);
    }

}