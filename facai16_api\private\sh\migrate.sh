#!/bin/bash

# 进入项目目录
cd /path/to/your/project || exit

# 检查数据库迁移状态
MIGRATION_STATUS=$(php think migrate:status)

# 判断是否有待迁移的文件
if echo "$MIGRATION_STATUS" | grep -q "up"; then
    echo "Running migrations..."
    php think migrate
    echo "Migrations completed."
else
    echo "No migrations needed."
fi

#chmod +x run_migrations.sh
#设置代码更新后自动运行：您可以在版本控制系统的钩子（例如 Git 的 post-merge 钩子）中调用该脚本。

#创建或编辑 .git/hooks/post-merge 文件：

#bash
#复制代码
#nano .git/hooks/post-merge
#添加以下内容：

#bash
#复制代码
#!/bin/bash
#/path/to/your/run_migrations.sh
#赋予钩子执行权限：

#bash
#复制代码
#chmod +x .git/hooks/post-merge
#测试脚本：可以手动运行脚本以确保其正常工作：

#bash
#复制代码
#./run_migrations.sh
#通过这种方式，每次在执行 git pull 或 git merge 后，post-merge 钩子都会触发，自动检查数据库迁移的状态并运行需要的迁移。确保您在运行迁移时具有相应的数据库权限。