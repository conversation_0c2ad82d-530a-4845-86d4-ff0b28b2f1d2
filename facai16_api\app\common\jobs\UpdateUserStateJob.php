<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\model\ItemOrder;
use app\common\model\MoneyClass;
use app\common\repository\GoodsLogRepository;
use app\common\repository\ItemLogRepository;
use app\common\repository\ItemOrderRepository;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\RaffleLogRepository;
use app\common\repository\SignInGiftLogRepository;
use app\common\repository\SignInRepository;
use app\common\repository\SmsCodeRepository;
use app\common\repository\UserBankRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserLoginRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\repository\WithdrawRepository;
use app\common\utils\Numeral;
use app\common\utils\Record;
use think\facade\Log;
use think\queue\Job;

/**
 * 更新用状态
 * Class TeamRebateJob
 * @package app\job
 */
class UpdateUserStateJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job,  $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec()
    {
        $uid            = $this->info['uid'];

        $UserStateRepo  = new UserStateRepository();

        $info           = $UserStateRepo->findById($uid);

        if (empty($info))
        {
            return;
        }

        $WithdrawRepo   = new WithdrawRepository();

        $where          = [];
        $where[]        = ['uid', '=', $uid];

        $update =  [
            'is_test'       => $info['is_test'],
            'update_time'   => time(),
        ];

        $res        = $WithdrawRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改提现订单状态');
        }

        $PaymentRepo    = new PaymentRepository();

        $where          = [];
        $where[]        = ['uid', '=', $uid];
        $res            = $PaymentRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改充值订单状态');
        }


        $MoneyLogRepo = new MoneyLogRepository();
        $res          = $MoneyLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改充值订单状态');
        }

        $GoodsLogRepo = new GoodsLogRepository();
        $res          = $GoodsLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改商品订单状态');
        }

        $ItemLogRepo = new ItemLogRepository();
        $res         = $ItemLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改项目订单记录状态');
        }

        $ItemOrderRepo = new ItemOrderRepository();
        $res           = $ItemOrderRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改项目订单状态');
        }


        $LevelLogRepo  = new LevelLogRepository();
        $res           = $LevelLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }


        $MoneyLogRepo  = new MoneyLogRepository();

        $res           = $MoneyLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }

        $RaffleLogRepo  = new RaffleLogRepository();
        $res            = $RaffleLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }


        $SignInRepo     = new SignInRepository();
        $res            = $SignInRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }


        $SignInGiftLogRepo  = new SignInGiftLogRepository();
        $res                = $SignInGiftLogRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }


        $SmsCodeRepo = new SmsCodeRepository();
        $res         = $SmsCodeRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }


        $UserBankRepo = new UserBankRepository();
        $res          = $UserBankRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }

        $UserLoginRepo  = new UserLoginRepository();
        $res            = $UserLoginRepo->updateByCondition($where, $update);

        if (!$res)
        {
            Record::log('job','1修改升级记录状态');
        }



    }



    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('UpdateUserState:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('UpdateUserState:' . $this->info['uid']);
    }

}

