// 引用网络请求中间件
import request from "../utils/request/request"

export function getProjects(data) {
	return request({
		url: '/item/lists',
		method: 'GET',
		data
	})
}

export function buyProject(data) {
	request({
		url: '/item/pay',
		method: 'POST',
		data
	})
}

export function getProjectInfo(data) {
	return request({
		url: '/item/info',
		method: 'GET',
		data
	})
}

export function getLevelInfo() {
	return request({
		url: '/api/levels',
		method: 'GET',
	})
}

export function getRecord(data) {
	return request({
		url: '/item/record',
		method: 'GET',
		data
	})
}

export function jobPush(data) {
	return request({
		url: '/item/push',
		method: 'POST',
		data
	})
}