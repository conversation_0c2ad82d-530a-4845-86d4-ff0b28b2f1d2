<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column label="用户名" width="150">
        <template #default="scope">
          {{ scope.row.username }}({{ scope.row.uname || "--" }})
        </template>
      </el-table-column>
      <el-table-column label="转给人" width="160">
        <template #default="scope">
          {{ scope.row.username2 }}({{ scope.row.uname2 || "--" }})
        </template>
      </el-table-column>
      <el-table-column prop="money" label="金额" width="160" />
      <el-table-column prop="addtime" label="添加时间" width="130" />
      <el-table-column label="状态" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.status, rechargeStatusEnums) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        fixed="right"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Check"
            @click="statusAction('pass', scope.row)"
            v-permission
            >同意</el-button
          >
          <el-button
            type="danger"
            icon="Close"
            @click="statusAction('reject', scope.row)"
            v-permission
            >拒绝</el-button
          >
        </template>
      </el-table-column>
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { rechargeStatusEnums, getLabelByVal } from "@/config/enums";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  username: "",
  starttime: "",
  endtime: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);

onMounted(() => {
  getList();
});

const submitForm = () => {};

const statusAction = (type, row) => {
  ElMessageBox.confirm(`确认${type == "pass" ? "同意" : "拒绝"}?`, "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ElMessage({
        type: "success",
        message: `${type == "pass" ? "同意" : "拒绝"}成功`,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: `取消${type == "pass" ? "同意" : "拒绝"}`,
      });
    });
};

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const getList = async () => {
  const res = await proxy.$http({
    type: "get",
    url: "/user/usertransfer",
  });
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
