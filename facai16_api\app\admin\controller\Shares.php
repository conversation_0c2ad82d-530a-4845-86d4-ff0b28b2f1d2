<?php
namespace app\admin\controller;

use app\admin\service\SharesService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 股权
 */
class Shares
{

    /**
     * 股权列表
     * @return Json
     */
    public function getSharesLists(): Json
    {
        $SharesService  = new SharesService();
        $data           = $SharesService->getSharesLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取股权信息
     * @return Json
     */
    public function getSharesInfo(): Json
    {
        $id            = Request::param('id',0);
        $SharesService = new SharesService();
        $data          = $SharesService->getSharesInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加股权
     * @return Json
     */
    public function addShares(): Json
    {
        $param         = Request::param();
        $SharesService = new SharesService();
        $data          = $SharesService->addShares($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新股权
     * @return Json
     */
    public function updateShares(): Json
    {
        $param         = Request::param();
        $SharesService = new SharesService();
        $data          = $SharesService->updateShares($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除股权
     * @return Json
     */
    public function deleteShares(): Json
    {
        $id            = Request::param('id',0);
        $SharesService = new SharesService();
        $data          = $SharesService->deleteShares($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 用户股权记录
     * @return Json
     */
    public function getSharesLogLists(): Json
    {
        $SharesService = new SharesService();
        $data          = $SharesService->getSharesLogLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 用户股权记录
     * @return Json
     */
    public function deleteSharesLogLists(): Json
    {
        $id            = Request::param('id',0);
        $SharesService = new SharesService();
        $data          = $SharesService->deleteSharesLogLists($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



}