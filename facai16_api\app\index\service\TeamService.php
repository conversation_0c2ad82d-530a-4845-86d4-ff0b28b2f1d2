<?php

namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Request;

class TeamService
{
    /**
     * 团队
     * @param int $lv
     * @return array
     */
    public function lists(int $lv): array
    {
        $UserRepo    = new UserRepository();

        $user       = $UserRepo->userByHeader();

        $where      = [];
        $where[]    = ['v' . $lv . '_id', '=', $user['id']];

        $data       = $UserRepo->paginates($where,'id,phone,username,nickname,level_name');


        foreach ($data['data'] as &$item)
        {

            $i        = $item['id'];

            $where    = [];
            $where[]  = ['top_id', '=', $i];
            $where[]  = ['level', '<=', 3];

            $UserRelationRepo = new UserRelationRepository();
            $members          = $UserRelationRepo->countByCondition($where,'uid');

            $UserInfoRepo     = new UserInfoRepository();
            $userInfo         = $UserInfoRepo->findById($i);

            //1总充值
            $item['total_recharge']          = $userInfo['recharge_money'];

            //2总提现
            $item['total_withdraw']          = $userInfo['withdraw_money'];

            //3总投资金额
            $item['total_invest']            = $userInfo['invest_money'];
            //团队人数
            $item['total_user']              = $members;

        }

        return Result::success($data);
    }

    /**
     * 下级
     * @param int $lv
     * @param int $blow
     * @return array
     */
    public function blow(int $lv, int $blow): array
    {
        $UserRepo  = new UserRepository();

        $where    = [];
        $where[]  = ['top_id', '=', $blow];
        $where[]  = ['level', '=', 1];


        $UserRelationRepo = new UserRelationRepository();
        $members          = $UserRelationRepo->selectByCondition($where,'uid');
        $members          = array_column($members, 'uid');

        $where      = [];
        $where[]    = ['id', 'in', $members];

        $data       = $UserRepo->paginates($where,'id,phone,username,nickname,level_name');

        $data['lv'] = $lv;

        foreach ($data['data'] as &$item)
        {
            $i        = $item['id'];

            $where    = [];
            $where[]  = ['top_id', '=', $i];
            $where[]  = ['level', '<=', 3];

            $UserRelationRepo = new UserRelationRepository();
            $members          = $UserRelationRepo->countByCondition($where,'uid');

            $UserInfoRepo     = new UserInfoRepository();
            $userInfo         = $UserInfoRepo->findById($i);

            //1总充值
            $item['total_recharge']          = $userInfo['recharge_money'];

            //2总提现
            $item['total_withdraw']          = $userInfo['withdraw_money'];

            //3总投资金额
            $item['total_invest']            = $userInfo['invest_money'];
            //团队人数
            $item['total_user']              = $members;

        }

        return Result::success($data);
    }


    /**
     * 信息
     * @return array
     */
    public function info(): array
    {
        $UserRepo   = new UserRepository();
        $user       = $UserRepo->userByHeader();
        $userInfo   = $UserRepo->userInfoByHeader();

        $where      = [];
        $where[]    = ['v1_id', '=', $user['id']];
        $v1         = $UserRepo->countByCondition($where);


        $where    = [];
        $where[]  = ['top_id', '=', $user['id']];
        $where[]  = ['level', '<=', 99];

        $UserRelationRepo = new UserRelationRepository();
        $members          = $UserRelationRepo->selectByCondition($where,'uid');

        $members          = array_column($members, 'uid');

        $history          = strtotime(date('Y-m-d 00:00:00', 1));
        $today            = strtotime(date('Y-m-d 00:00:00'));
        $month            = strtotime(date('Y-m-1 00:00:00'));
        $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day', Request::time())));


        $MoneyLogRepo       = new MoneyLogRepository();
        $UserInfoRepo       = new UserInfoRepository();


        $data =[
            'v0'            => count($members),
            'v1'            => $v1,
        ];

        //1总充值
        $data['total_recharge']          = empty($members) ? 0 :$UserInfoRepo->totalRecharge($history, $tomorrow,'', $members);

        //2总提现
        $data['total_withdraw']          = empty($members) ? 0 :$UserInfoRepo->totalWithdraw($history, $tomorrow,'', $members);

        //3总订阅金额
        $data['total_invest']            = empty($members) ? 0 :$UserInfoRepo->totalItemMoney($history,$tomorrow,'', $members);

        //4今日充值金额
        $data['today_recharge']          = empty($members) ? 0 :$MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::RECHARGE,], '', $members);

        //5当月充值金额
        $data['month_recharge']          = empty($members) ? 0 :$MoneyLogRepo->statistics($month, $tomorrow, [MoneyClass::RECHARGE,], '', $members);

        //6总订阅金额
        $data['total_earn']              = $userInfo['team_invite'] + $userInfo['team_item'];

        return Result::success($data);
    }



}