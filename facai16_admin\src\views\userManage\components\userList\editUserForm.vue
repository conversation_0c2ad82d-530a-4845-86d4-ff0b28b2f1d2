<template>
    <el-form label-width="120px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="用户名" required>
            <el-input v-model="form.username" clearable />
        </el-form-item>
        <el-form-item label="手机号" required>
            <el-input v-model="form.phone" clearable />
        </el-form-item>
        <el-form-item label="邀请码" required>
            <el-input v-model="form.invite" clearable />
        </el-form-item>
        <!-- <el-form-item label="会员等级" required>
            <el-select v-model="form.member" placeholder="" clearable>
                <el-option v-for="item in memberLevelList" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item> -->
        <el-form-item label="登陆密码" required>
            <el-input v-model="form.password" clearable />
        </el-form-item>
        <!-- <el-form-item label="支付密码" required>
            <el-input v-model="form.pay_password" clearable />
        </el-form-item> -->
        <!-- <el-form-item label="登入限制" required>
            <el-select v-model="form.is_login" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="抽奖次数" required>
            <el-input v-model="form.draw_num" clearable />
        </el-form-item>
        <el-form-item label="积分" required>
            <el-input v-model="form.jifen" clearable />
        </el-form-item>
        <el-form-item label="优惠券" required>
            <el-input v-model="form.juan" clearable />
        </el-form-item>
        <el-form-item label="断网" required>
            <el-select v-model="form.no_internet" placeholder="" clearable>
                <el-option v-for="item in booleanEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="账户冻结" required>
            <el-select v-model="form.clock" placeholder="" clearable>
                <el-option v-for="item in clockEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="提款冻结" required>
            <el-select v-model="form.withdraw_status" placeholder="" clearable>
                <el-option v-for="item in clockEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <h4 class="form-title">身份验证</h4>
        <el-form-item label="姓名" required>
            <el-input v-model="form.name" clearable />
        </el-form-item>
        <el-form-item label="证件号" required>
            <el-input v-model="form.sfz_number" clearable />
        </el-form-item>
        <el-form-item label="实名状态" required>
            <el-select v-model="form.sfz_status" placeholder="" clearable>
                <el-option v-for="item in sfzStatusEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item> -->
    </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import {limitEnums, booleanEnums, openEnums, memberLevelList, sfzStatusEnums, clockEnums} from '@/config/enums'

const form = reactive({
    username: '',
    phone: '',
    invite: '',
    // member: '',
    password: '',
    // pay_password: '',
    // is_login: '',
    // draw_num: '',
    // jifen: '',
    // juan: '',
    // no_internet: '',
    // clock: '',
    // withdraw_status: '',
    // name: '',
    // sfz_number: '',
    // sfz_status: '',

    
})




defineExpose({form})
</script>

<style lang="less" scoped>
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
</style>