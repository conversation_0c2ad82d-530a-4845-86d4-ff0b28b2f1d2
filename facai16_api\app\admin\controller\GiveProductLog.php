<?php

namespace app\admin\controller;

use app\common\utils\Ajax;
use think\facade\Request;
use app\admin\service\GiveProductLogService;
use think\response\Json;

/**
 * 商品购买记录
 */
class GiveProductLog
{
    public function records():Json
    {

        $params = Request::only([
            'phone'=>'',
            'username'=>'',
            'order_no'=>'',
            'item_name'=>'',
            'item_id'=>'',
            'status'=>'',
            'is_test'=>''
        ]);
        $GiveProductLogService = new GiveProductLogService();
        $data = $GiveProductLogService->records($params);
        return Ajax::message($data['code'], $data['msg'], $data['data']);

    }

    public function update():Json
    {

        $params = Request::only([
            'id',
            'phone'=>'',
            'username'=>'',
            'status'=>'',
            'address_place'=>'',
            'address_city'=>'',
            'address_name'=>'',
            'address_phone'=>'',
            'deliver_title' => '',
            'deliver_order_no' => '',
            'deliver_time' => '',
        ]);
        $GiveProductLogService = new GiveProductLogService();
        $data = $GiveProductLogService->update($params);
        return Ajax::message($data['code'], $data['msg'], $data['data']);

    }

    public function del():Json
    {
        $id = Request::param('id',0);
        $GiveProductLogService = new GiveProductLogService();
        $data = $GiveProductLogService->del($id);
        return Ajax::message($data['code'], $data['msg'], $data['data']);

    }

}