<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\repository\MessageRepository;
use app\common\utils\Record;
use think\facade\Log;
use think\queue\Job;

/**
 * 站内信
 * Class MessageJob
 * @package app\job
 */
class MessageJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job,  $data)
    {
        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }
    }


    /**
     * 执行
     */
    public function exec()
    {
        $insert    = [
            'uid'         => $this->info['uid'],
            'username'    => $this->info['username'],
            'phone'       => $this->info['phone'],
            'is_test'     => $this->info['is_test'],
            'title'       => $this->info['title'],
            'content'     => $this->info['content'],
            'create_time' => time(),
            'update_time' => time(),
        ];


        $MessageRepo = new MessageRepository();
        $res         = $MessageRepo->inserts($insert);

        if (!$res)
        {
           Record::log('job','站内信发送', $insert);
        }
    }



    /**
     * 记录失败
     * @param  $info
     */
    public function failed($info)
    {

    }



    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('MessageJob:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('MessageJob:' . $this->info['uid']);
    }

}

