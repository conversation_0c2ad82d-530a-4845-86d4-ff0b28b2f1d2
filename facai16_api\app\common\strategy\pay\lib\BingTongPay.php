<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * BingTongPay
 */
class BingTongPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'BingTongPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {

        $param = [
            'userName'        => $this->config['mch_id'],
            'amount'          => $data['money'],
//            'payType'         => $this->config['channel'],
            'outOrderId'      => $data['orderNo'],
            'returnUrl'       => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
            'frontReturnUrl'  => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
        ];

        $param['sign']  = $this->signature($param);
        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $uri            = '';

        if(isset($result['responseCode'])  && $result['responseCode'] == 200)
        {
            $uri = $result['url'];
        }

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"status":"2","code":"drenjie-zfb","orderno":"17402125841410","amount":"500.00","sign":"d24e591f6c68a22a5b0a3c4a68c10965"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];
        $param     = Arrays::withOut($param,['sign','status']);
        $signature = md5($param['amount'] . $param['code'] . $param['orderno'] . $this->config['sign']);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['orderno'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        // 按照ASCII码对参数名进行排序
//        ksort($params);

        // 拼接待签名字符串
        $stringToSign = '';

        foreach ($params as $key => $value)
        {
            $stringToSign .= $key . '=' . $value . '&';
        }

        $stringToSign .= 'access_token=' . $this->config['sign'];
        var_dump($stringToSign);
        // 使用MD5算法生成签名
        return strtoupper(md5($stringToSign));
    }

}