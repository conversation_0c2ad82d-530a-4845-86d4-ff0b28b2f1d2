<?php
namespace app\admin\service;

use app\common\repository\PaymentAccountRepository;
use app\common\repository\PaymentChannelRepository;
use app\common\repository\PaymentClassRepository;
use app\common\repository\SystemSetRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 系统设置
 */
class SystemSetService
{
    /**
     * 获取系统设置列表
     * @param array $params
     * @return array
     */
    public function getSystemSetLists(array $params): array
    {
         $where = [];

         if (!empty($params['desc']))
         {
             $where[] = ['key', '=', $params['desc']];
         }

         $SystemSetRepo = new SystemSetRepository();
         $data          = $SystemSetRepo->paginates($where,'*', $params['limit']);

         return Result::success($data);
    }

    /**
     * 更新系统设置
     * @param $key
     * @param $value
     * @param $desc
     * @return array
     */
    public function updateSystemSet($key, $value, $desc): array
    {



        $update = [
            'key'           => $key,
            'val'           => $value,
            'desc'          => $desc,
            'update_time'   => Request::time()
        ];

        $SystemSetRepo = new SystemSetRepository();
        $res           = $SystemSetRepo->updateByCondition(['key' => $key], $update);

        if (!$res)
        {
            return Result::fail('修改失败');
        }

        if ($key == 'usdt_huilv')
        {
            $PaymentClassRepo = new PaymentClassRepository();

            $update = [
                'rate'        => $value,
                'update_time' => time(),
            ];

            $PaymentClassRepo->updateById(4, $update);

            $update = [
                'class_rate' => $value,
                'update_time' => time(),
            ];

            $where              = [];
            $where[]            = ['class_id', '=',4];
            $PaymentChannelRepo = new PaymentChannelRepository();
            $PaymentChannelRepo->updateByCondition($where, $update);

            $update = [
                'rate'        => $value,
                'update_time' => time(),
            ];

            $where   = [];
            $where[] = ['type', '=',1];
            $PaymentAccountRepo = new PaymentAccountRepository();
            $PaymentAccountRepo->updateByCondition($where, $update);
        }

        return Result::success();
    }


    /**
     * 更新系统设置
     * @param $key
     * @param $value
     * @param $desc
     * @return array
     */
    public function addSystemSet($key, $value, $desc): array
    {
        $insert = [
            'key'           => $key,
            'val'           => $value,
            'desc'          => $desc,
            'update_time'   => time(),
            'create_time'   => time()
        ];

        $SystemSetRepo = new SystemSetRepository();
        $res           = $SystemSetRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }

    /**
     * 删除系统配置
     * @param $id
     * @return array
     */
    public function deleteSystemSet($id): array
    {

        $SystemSetRepo = new SystemSetRepository();
        $res           = $SystemSetRepo->deleteById($id);

        if (!$res)
        {
            return Result::error();
        }

        return Result::success();
    }
}