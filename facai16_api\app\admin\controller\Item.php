<?php
namespace app\admin\controller;

use app\admin\service\ItemService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 项目
 */
class Item
{

    /**
     * 项目列表
     * @return json
     */
    public function getItemLists(): Json
    {
        $params        = Request::only([
            'status'   => 0,
            'title'    => '',
            'class_id' => ''
        ]);


        $ItemService    = new ItemService();
        $data           = $ItemService->getItemLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 项目详情
     * @return Json
     */
    public function getItemInfo(): Json
    {
        $id           = Request::param('id',0);
        $ItemService  = new ItemService();
        $data         = $ItemService->getItemInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 添加项目
     * @return Json
     */
    public function addItem(): Json
    {
        $param        = Request::param();
        $ItemService  = new ItemService();
        $data         = $ItemService->addItem($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新项目
     * @return Json
     */
    public function updateItem(): Json
    {
        $param        = Request::param();
        $ItemService  = new ItemService();
        $data         = $ItemService->updateItem($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除项目
     * @return Json
     */
    public function deleteItem(): Json
    {
        $id           = Request::param('id',0);
        $ItemService  = new ItemService();
        $data         = $ItemService->deleteItem($id);
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 项目分类
     * @return mixed
     */
    public function getItemClassLists(): Json
    {
        $param        = Request::only(['title']);

        $ItemService  = new ItemService();
        $data         = $ItemService->getItemClassLists($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 项目分类信息
     * @return Json
     */
    public function getItemClassInfo(): Json
    {
        $id           = Request::param('id',0);
        $ItemService  = new ItemService();
        $data         = $ItemService->getItemClassInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加分类
     * @return Json
     */
    public function addItemClass(): Json
    {
        $param        = Request::param();
        $ItemService  = new ItemService();
        $data         = $ItemService->addItemClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新分类
     * @return Json
     */
    public function updateItemClass(): Json
    {
        $param        = Request::param();
        $ItemService  = new ItemService();
        $data         = $ItemService->updateItemClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除分类
     * @return Json
     */
    public function deleteItemClass(): Json
    {
        $id           = Request::param('id',0);
        $ItemService  = new ItemService();
        $data         = $ItemService->deleteItemClass($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 项目结算记录
     * @return mixed
     */
    public function getItemLogLists(): Json
    {
        $params        = Request::only([
            'username'      => '',
            'phone'         => '',
            'starttime'     => '',
            'endtime'       => '',
            'order_no'      => '',
            'page'          => 1,
            'limit'         => 10,
        ]);

        $ItemService  = new ItemService();
        $data         = $ItemService->getItemLogLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}