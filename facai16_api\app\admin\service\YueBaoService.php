<?php
namespace app\admin\service;


use app\common\repository\YueBaoRepository;
use app\common\utils\Result;


/**
 * 余额宝记录
 */
class YueBaoService
{

    /**
     * 记录
     * @param $params
     * @return array
     */
    public function yueBaoLog($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        $YueBaoRepo = new YueBaoRepository();
        $data       = $YueBaoRepo->paginates($where);

        return Result::success($data);
    }
}