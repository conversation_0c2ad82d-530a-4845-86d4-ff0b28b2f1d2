<?php

namespace app\index\service;

use app\common\repository\UserAddressRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 地址服务
 */
class AddressService
{

    /**
     * 地址信息
     * @return array
     */
    public function info(): array
    {
        $UserRepo           = new UserRepository();
        $UserAddressRepo    = new UserAddressRepository();

        $user           = $UserRepo->userByHeader();
        $where          = [];
        $where[]        = ['uid', '=', $user['id']];
        $data           = $UserAddressRepo->findByCondition($where, '*', ['id' => 'desc']);

        return Result::success($data);
    }

    /**
     * 用户地址列表
     * @return array
     */
    public function lists(): array
    {
        $UserRepo           = new UserRepository();
        $UserAddressRepo    = new UserAddressRepository();

        $user           = $UserRepo->userByHeader();
        $where          = [];
        $where[]        = ['uid', '=', $user['id']];
        $data           = $UserAddressRepo->selectByCondition($where,'*',['id' => 'asc']);

        return Result::success($data);

    }

    /**
     * 用户地址列表
     * @param array $params
     * @return array
     */
    public function edit(array $params): array
    {
        $UserRepo           = new UserRepository();
        $UserAddressRepo    = new UserAddressRepository();
        $user               = $UserRepo->userByHeader();

        $update = [
            'address_name'    => $params['address_name'],
            'address_phone'   => $params['address_phone'],
            'address_city'    => $params['address_city'],
            'address_place'   => $params['address_place'],
            'update_time'     => Request::time(),
        ];

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['id', '=', $params['id']];

        $res        = $UserAddressRepo->updateByCondition($where, $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }


    /**
     * 用户地址管理
     * @param array $params
     * @return array
     */
    public function add(array $params):array
    {
        $UserRepo           = new UserRepository();
        $UserAddressRepo    = new UserAddressRepository();
        $user               = $UserRepo->userByHeader();

        $insert = [
            'uid'             => $user['id'],
            'phone'           => $user['phone'],
            'username'        => $user['username'],
            'is_test'         => $user['is_test'],
            'address_name'    => $params['address_name'],
            'address_phone'   => $params['address_phone'],
            'address_city'    => $params['address_city'],
            'address_place'   => $params['address_place'],
            'update_time'     => Request::time(),
            'create_time'     => Request::time()
        ];

        $res        = $UserAddressRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }


    /**
     * 删除地址
     * @param int $id
     * @return array
     */
    public function delete(int $id): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $UserAddressRepo    = new UserAddressRepository();

        $where      = [];
        $where[]    = ['id', '=', $id];
        $where[]    = ['uid', '=', $user['id']];

        $res = $UserAddressRepo->deleteByCondition($where);

        if (!empty($res))
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }

}