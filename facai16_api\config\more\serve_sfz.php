<?php
return [

    // 默认缓存驱动
    'default' => env('strategy.sfz', 'Tencent'),

    // 状态
    'status'  => false,

    // 服务策略
    'strategy' => [

        // 腾讯
        'Tencent' => [
            'secret_id'     => 'AKIDoeOwHQN7f09Rkz1rTsZ8cw2vnW4Tjnkf',
            'secret_key'    => 'nNlyNZehZEwZ0EIApGXdZaQ2Ss9GBwVt',
            'host'          => 'faceid.tencentcloudapi.com',
            'service'       => 'faceid',
            'action'        => 'IdCardVerification',
            'algorithm'     => 'TC3-HMAC-SHA256',
            'url'           => 'https://faceid.tencentcloudapi.com/',
        ],

        // 阿里云
        'AliYun' => [
            'url'           => 'https://slybank234.market.alicloudapi.com/bankcard234/check',
            'appcode'       => 'da72797b179a44b3acb9ac0623a584ec'
        ],

        // 聚合
        'JuHe'  => [
            'url'       => 'http://op.juhe.cn/idcard/query',
            'key'       => '0914a2200fc33abe9e83211e2a505b35',
        ],


    ],

];