<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">


					<uv-navbar  bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								钱包地址
							</view>
						</template>
					</uv-navbar>
					
				</view>
				

				<view class="flex1 f-pr color-#fff" >
					<scroll-view scroll-y="true" class="scroll-view ">
						
						
						<view class="mx-40 py-20 t-28 lh-48 mb-0" v-for="(item,index) in bankList" :key="index">
							
							<view class="f-pr">
								<image src="/static/count/bank-bg.png" mode="widthFix" class="w-full block r-20"></image>
								<view class="f-pa inset fc">
									<view class="pl-40 flex1 pr-30">
										<view class="fc-bet  mb-12">
											<view class=" t-32 lh-45 mr-15 fw-600">
												{{item.name}}
											</view>
											<view class="fc">
												<image src="/static/choujiang/edit.png" mode="aspectFit" class="block size-40" ></image>
												<image src="/static/icon-del.png" mode="aspectFit" class="block size-40 ml-40"></image>
											</view>
										</view>
										<view class=" t-28 lh-40 fw-600">
											<text>{{item.bankid}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
						
						<no-data v-if="bankList.length<1">
							<text class="color-#fff" >
								暂无地址
							</text>
						</no-data>
						
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>
					
					
					
				</view>
				
				<view class="p-10 px-40 bg-#fff" v-if="bankList.length < 1">
					<view class="btn-full fcc" >
						添加银行卡
					</view>
				
					<view class="btn-area"></view>
				</view>

			</view>
		</view>



	</view>
</template>

<script>
	export default {

		data() {
			return {
				
				
				bankList:[
					{
						name:"黄小琥",
						
						bankid:"USDTaghjk12345678",
						
					}
				]
			};
		},
		mounted() {

		},

		methods: {
			
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}



</style>