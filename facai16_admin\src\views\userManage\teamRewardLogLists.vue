<template>
  <adminTable
    :hideResetButton="true"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="用户ID" width="130" />
      <el-table-column prop="username" label="用户名" width="160" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="team_id" label="团队ID" width="160" />
      <el-table-column prop="team_name" label="团队名称" width="160" />
      <el-table-column label="账号类型" width="130">
        <template #default="scope">
          {{ scope.row.is_test == 0 ? '正常' : '测试' }}账号
        </template>
      </el-table-column>
      <el-table-column prop="gift_time" label="团队礼品发放时间" width="160" />
      <el-table-column prop="bonus" label="团队奖金" width="160" />
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { userLevelTypeEnums, getLabelByVal } from "../../config/enums";

const searchForm = ref({
  ip: "",
});

onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);
const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Team/getTeamGiftLists",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
