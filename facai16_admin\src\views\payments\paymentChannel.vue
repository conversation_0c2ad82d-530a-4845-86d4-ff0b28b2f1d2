<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input v-model="searchForm.title" placeholder="搜索标题" clearable />
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editItem('add', {})"
        v-permission
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="支付名称" width="150" />
      <el-table-column prop="class_name" label="充值类型" width="150" />
      <!-- <el-table-column prop="class_status" label="充值类型状态" width="150">
        <template #default="scope">
          {{ getLabelByVal(scope.row.class_status, openEnums) }}
          <el-switch
            v-permission
            :loading="scope.row.typeLoading"
            @change="typeSwitchChange(scope.row)"
            :active-value="0"
            :inactive-value="1"
            v-model="scope.row.class_status"
          />
        </template>
      </el-table-column> -->
      <el-table-column prop="class_img" label="充值类型图标" width="150">
        <template #default="scope">
          {{ scope.row.class_img ? "" : "--" }}
          <el-image
            v-if="scope.row.class_img"
            class="previewImg"
            :preview-teleported="true"
            :src="proxy.IMG_BASE_URL + scope.row.class_img"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[proxy.IMG_BASE_URL + scope.row.class_img]"
            show-progress
            :initial-index="0"
            fit="cover"
          />
        </template>
      </el-table-column>

      <el-table-column prop="class_rate" label="充值类型汇率" width="150" />
      <el-table-column prop="account_name" label="支付账号" width="150" />
      <el-table-column prop="code" label="支付编码" width="150" />
      <el-table-column prop="max" label="最大充值金额" width="150" />
      <el-table-column prop="min" label="最小充值金额" width="150" />
      <el-table-column prop="status" label="状态" width="150">
        <template #default="scope">
          {{ getLabelByVal(scope.row.status, openEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="upper_name" label="支付上游" width="150" />
      <!-- <el-table-column prop="upper_status" label="支付上游状态" width="150">
        <template #default="scope">
          {{ getLabelByVal(scope.row.upper_status, openEnums) }}
          <el-switch
            v-permission
            :loading="scope.row.upperLoading"
            @change="upperSwitchChange(scope.row)"
            :active-value="0"
            :inactive-value="1"
            v-model="scope.row.upper_status"
          />
        </template>
      </el-table-column> -->
      <el-table-column prop="style" label="支付方式" width="150">
        <template #default="scope">
          {{ getLabelByVal(scope.row.style, payChannelStyleEnums) }}
        </template>
      </el-table-column>

      <el-table-column prop="create_at" label="添加时间" width="160" />
      <el-table-column prop="update_at" label="更新时间" width="160" />

      <el-table-column
        label="操作"
        fixed="right"
        width="200"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editItem('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="新增/编辑" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { openEnums, payChannelStyleEnums, getLabelByVal } from "@/config/enums";
import EditPop from "./components/paymentChannel/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  title: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);
const formType = ref("add");

onMounted(() => {
  getList();
});

const typeSwitchChange = async (row) => {
  row.typeLoading = true;
  const res = await proxy.$http({
    method: "post",
    url: "PaymentClass/updatePaymentClass",
    data: {
      status: row.class_status,
      id: row.class_id,
    },
  });
  row.typeLoading = false;
  getList()
  if (res.code != 0) {
    row.class_status == 1 ? (row.class_status = 0) : (row.class_status = 1);
  }
};

const upperSwitchChange = async (row) => {
  row.upperLoading = true;
  const res = await proxy.$http({
    method: "post",
    url: "PaymentUpper/updatePaymentUpper",
    data: {
      status: row.upper_status,
      id: row.upper_id,
    },
  });
  row.upperLoading = false;
  getList()
  if (res.code != 0) {
    row.upper_status == 1 ? (row.upper_status = 0) : (row.upper_status = 1);
  }
};

const editItem = (type, row = {}) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url =
    formType.value == "add"
      ? "PaymentChannel/addPaymentChannel"
      : "PaymentChannel/updatePaymentChannel";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      code: data.code,
      status: data.status,
      upper_id: data.upper_id,
      class_id: data.class_id,
      min: data.min,
      max: data.max,
      style: data.style,
      accounts: data.accounts || null,
      title: data.title,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "PaymentChannel/deletePaymentChannel?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/PaymentChannel/getPaymentChannelLists",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    tableData.value.map((item) => {
      item.typeLoading = false;
      item.upperLoading = false;
    });
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
