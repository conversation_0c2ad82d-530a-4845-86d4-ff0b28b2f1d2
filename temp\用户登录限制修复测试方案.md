# 用户登录限制修复测试方案

## 测试目标

验证用户登录限制功能的完整性和正确性，确保：
1. 被限制用户无法登录
2. 已登录用户被限制后立即失效
3. 正常用户不受影响
4. 错误提示信息正确
5. 状态恢复功能正常

## 测试环境准备

### 1. 测试数据准备

#### 创建测试用户
```sql
-- 测试用户1：正常用户
INSERT INTO user (id, username, phone, password, create_time) 
VALUES (9001, 'testuser1', '13800138001', 'test123', UNIX_TIMESTAMP());

INSERT INTO user_state (uid, ban_login, create_time) 
VALUES (9001, 0, UNIX_TIMESTAMP());

-- 测试用户2：被限制用户
INSERT INTO user (id, username, phone, password, create_time) 
VALUES (9002, 'testuser2', '13800138002', 'test123', UNIX_TIMESTAMP());

INSERT INTO user_state (uid, ban_login, create_time) 
VALUES (9002, 1, UNIX_TIMESTAMP());

-- 测试用户3：用于动态测试
INSERT INTO user (id, username, phone, password, create_time) 
VALUES (9003, 'testuser3', '13800138003', 'test123', UNIX_TIMESTAMP());

INSERT INTO user_state (uid, ban_login, create_time) 
VALUES (9003, 0, UNIX_TIMESTAMP());
```

### 2. 测试工具准备
- API测试工具（Postman/curl）
- 数据库客户端
- 浏览器开发者工具
- 管理后台访问权限

## 详细测试用例

### 测试用例1：正常用户登录验证

**目的**: 确保正常用户不受影响
**前置条件**: testuser1的ban_login=0

#### 步骤1.1：正常登录测试
```bash
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138001",
    "password": "test123"
  }'
```

**预期结果**:
- 返回状态码200
- 返回成功信息和token
- 用户可以正常登录

#### 步骤1.2：登录后API访问测试
```bash
curl -X GET "http://your-domain/api/user/info" \
  -H "Accept-Token: [从步骤1.1获取的token]"
```

**预期结果**:
- 返回状态码200
- 正常返回用户信息
- 中间件检查通过

### 测试用例2：被限制用户登录验证

**目的**: 验证被限制用户无法登录
**前置条件**: testuser2的ban_login=1

#### 步骤2.1：限制用户登录测试
```bash
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138002",
    "password": "test123"
  }'
```

**预期结果**:
- 返回错误信息
- 错误消息："账号已被限制登录，请联系客服"
- 不返回token
- 登录被阻止

### 测试用例3：动态限制测试

**目的**: 验证已登录用户被限制后立即失效
**前置条件**: testuser3的ban_login=0

#### 步骤3.1：正常登录
```bash
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138003",
    "password": "test123"
  }'
```

**预期结果**: 登录成功，获取token

#### 步骤3.2：验证登录状态
```bash
curl -X GET "http://your-domain/api/user/info" \
  -H "Accept-Token: [步骤3.1获取的token]"
```

**预期结果**: 正常返回用户信息

#### 步骤3.3：管理后台设置限制
通过管理后台或直接SQL设置用户限制：
```sql
UPDATE user_state SET ban_login = 1 WHERE uid = 9003;
```

#### 步骤3.4：验证强制退出
再次使用相同token访问API：
```bash
curl -X GET "http://your-domain/api/user/info" \
  -H "Accept-Token: [步骤3.1获取的token]"
```

**预期结果**:
- 返回错误码445
- 错误消息："账号已被限制登录，请联系客服"
- 用户被强制退出

#### 步骤3.5：验证无法重新登录
```bash
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138003",
    "password": "test123"
  }'
```

**预期结果**: 登录被阻止，返回限制提示

### 测试用例4：状态恢复测试

**目的**: 验证状态恢复功能正常
**前置条件**: testuser3的ban_login=1

#### 步骤4.1：恢复用户状态
```sql
UPDATE user_state SET ban_login = 0 WHERE uid = 9003;
```

#### 步骤4.2：验证可以重新登录
```bash
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138003",
    "password": "test123"
  }'
```

**预期结果**:
- 登录成功
- 获取新的token
- 可以正常访问API

### 测试用例5：注册自动登录测试

**目的**: 验证注册时的登录限制检查
**前置条件**: 准备一个新的手机号用于注册

#### 步骤5.1：正常注册测试
```bash
curl -X POST "http://your-domain/api/register" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138004",
    "password": "test123",
    "code": "123456"
  }'
```

**预期结果**: 注册成功，如果有自动登录则正常登录

#### 步骤5.2：设置新用户限制
```sql
UPDATE user_state SET ban_login = 1 WHERE uid = (SELECT id FROM user WHERE phone = '13800138004');
```

#### 步骤5.3：再次注册测试（如果支持重复注册）
验证注册后的自动登录是否被阻止

### 测试用例6：错误码验证

**目的**: 验证错误码的正确性

#### 步骤6.1：验证444错误码（原有）
使用无效token访问API：
```bash
curl -X GET "http://your-domain/api/user/info" \
  -H "Accept-Token: invalid_token"
```

**预期结果**: 返回错误码444

#### 步骤6.2：验证445错误码（新增）
使用被限制用户的有效token访问API（参考测试用例3）

**预期结果**: 返回错误码445

## 边界条件测试

### 1. 并发测试
- 同时设置多个用户的限制状态
- 验证系统在高并发下的表现

### 2. 数据一致性测试
- 验证ban_login字段的数据类型处理
- 测试NULL值、非0/1值的处理

### 3. 性能测试
- 测试添加检查后的登录性能
- 测试中间件检查对API响应时间的影响

## 测试结果记录

### 测试结果模板
```
测试用例: [用例编号]
执行时间: [YYYY-MM-DD HH:MM:SS]
执行人: [测试人员]
测试结果: [通过/失败]
实际结果: [详细描述]
问题描述: [如果失败，描述问题]
```

### 关键指标
- [ ] 正常用户登录成功率: 100%
- [ ] 被限制用户登录阻止率: 100%
- [ ] 强制退出成功率: 100%
- [ ] 状态恢复成功率: 100%
- [ ] 错误码准确率: 100%

## 回归测试

### 验证原有功能
- [ ] 正常用户登录流程
- [ ] 用户注册流程
- [ ] 管理后台用户管理功能
- [ ] 其他用户状态设置功能

### 验证新功能
- [ ] 登录时ban_login检查
- [ ] 中间件ban_login检查
- [ ] 强制退出功能
- [ ] 错误码445返回

## 测试完成标准

### 必须通过的测试
1. 所有测试用例执行通过
2. 无功能回归问题
3. 性能影响在可接受范围内
4. 错误处理机制正常

### 可选验证项
1. 日志记录完整性
2. 用户体验友好性
3. 管理后台操作便利性

## 测试数据清理

测试完成后清理测试数据：
```sql
-- 删除测试用户
DELETE FROM user WHERE id IN (9001, 9002, 9003);
DELETE FROM user_state WHERE uid IN (9001, 9002, 9003);

-- 清理其他相关测试数据
DELETE FROM user_login WHERE uid IN (9001, 9002, 9003);
```
