<template>
	<view>
		<uv-gap :height="height"></uv-gap>
		
		<view class="btn-area" v-if="safe"></view>
		
		<view class="fixed-footer-view">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name:"fixed-footer-view",
		props: {
			height: {
				type: String,
				default: '100rpx'
			},
			safe:{
				type:Boolean,
				default:true
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.fixed-footer-view{
		position: fixed;
		z-index: 66;
		left: 0;
		right: 0;
		bottom: 0;
	}
</style>