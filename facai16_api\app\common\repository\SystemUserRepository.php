<?php
namespace app\common\repository;
use app\common\model\SystemUser;
use think\facade\Request;


class SystemUserRepository extends SystemUser
{

    use BaseRepository;

    public function adminLoginUserInfo(): array
    {
        $token   = session('admin_token');
        $where   = [];
        $where[] = ['token', '=', $token];
        return $this->findByCondition($where);
    }


    public function isLogin(): bool
    {
        $adminUser = $this->adminLoginUserInfo();

        if (empty($adminUser))
        {
            //没登录
            return false;
        }else
        {
            //登录了
            return true;
        }
    }


    /**
     * 获取用户信息
     * @param string $field
     * @param string $token
     * @return array|string
     */
    public function userByHeader(string $field = '*', string $token = '')
    {

        $token      = Request::header('Accept-Token', $token);

        if(empty($token))
        {
            return [];
        }

        $where   = [];

        $where[] = ['token', '=', $token];

        if ($field == '*')
        {
            $user    = $this->findByCondition($where, $field,[],true);
        }
        elseif (count(explode(',',$field)) > 1)
        {
            $user    = $this->findByCondition($where, $field, [],true);
        }
        else
        {
            $user    = $this->valueByCondition($where, $field, true);
        }

        if(empty($user))
        {
            return [];
        }

        return $user;
    }

}
