<?php

namespace app\common\strategy\sms\lib;

use app\common\strategy\sms\SmsInterface;
use app\common\utils\Result;
use think\facade\Config;



class JiuDingGe implements SmsInterface
{

    /**
     * 发动短信
     * @param $phone
     * @param $smscode
     * @return array
     */
    public function send($phone, $smscode): array
    {
        $config   = Config::get('serve_sms.strategy.JiuDingGe');

        //地址
        $url     = $config['url'];

        //短信平台帐号
        $user    = $config['account'];
        //短信平台密码
        $pass    = $config['password'];

        //要发送的短信内容

//        $content = "您的验证码是" . $smscode . "。如非本人操作，请忽略本短信";
        $content = __('verification_code_operated_please_ignore_message',['code' => $smscode]);

        $data    = [
            'SpCode'         => $config['uid'],
            'LoginName'      => $user,
            'Password'       => $pass,
            'MessageContent' => $content,
            'UserNumber'     => $phone,
            'SerialNumber'   => '',
            'ScheduleTime'   => '',
            'subPort'        => '',
        ];

        $headers = array('Content-Type: application/x-www-form-urlencoded');

        $ch = curl_init();
        curl_setopt($ch,CURLOPT_URL,$url); //支付请求地址
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response=curl_exec($ch);

        //$res=simplexml_load_string($response);
        curl_close($ch);

        $result = json_decode($response, true);

        $return = [
            'code' => $result['result'],
            'info' => $result['description'],
        ];

        return Result::success($return);
    }
}
