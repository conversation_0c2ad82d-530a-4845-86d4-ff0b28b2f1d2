<?php
namespace app\admin\service;

use app\common\model\MoneyClass;
use app\common\repository\MoneyClassRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;

/**
 * 用户流水
 */
class MoneyLogService
{

    /**
     * 账变记录
     * @param $params
     * @return array
     */
    public function getMoneyLogLists($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_id']))
        {
            $where[] = ['order_id', '=', $params['order_id']];
        }

        if (!empty($params['class_id']))
        {
            $where[] = ['class_id', '=', $params['class_id']];
        }



        $MoneyLogRepo = new MoneyLogRepository();
        $data         = $MoneyLogRepo->paginates($where);

        return Result::success($data);
    }


    /**
     * 添加流水
     * @param $id
     * @param $money
     * @param $classId
     * @return array
     */
    public function addMoney($id, $money, $classId): array
    {
        $MoneyClassRepo = new MoneyClassRepository();
        $classes        = $MoneyClassRepo->findById($classId);

        if (empty($classes))
        {
            return Result::fail('数据错误');
        }


        $MoneyLogRepo = new MoneyLogRepository();

        if ($classId == MoneyClass::POINTS_ADD)
        {
            $res          = $MoneyLogRepo->point($id, $money, $classId,0,$classes['title']. ':增加' . $money.'元');
        }
        elseif ($classId ==  MoneyClass::POINTS_DEL)
        {
            $res          = $MoneyLogRepo->point($id, $money, $classId,0,$classes['title']. ':减少' . $money.'元');
        }
        else
        {
            $txt = [
                '0' => '可提余额增加',
                '1' => '可提需要钱少',
                '2' => '可用金额增加',
                '3' => '冻结金额减少',
                '4' => '可用冻结金额减少',
            ];

            $txt          = $txt[$classes['style']];
            $res          = $MoneyLogRepo->fund($id, $money, $classId,0,$classes['title']. ':' . $txt . $money.'元');

            if ($classId == MoneyClass::RECHARGE)
            {
                $UserInfoRepo = new UserInfoRepository();
                $res          = $UserInfoRepo->statistic($id, ['recharge_money' => $money, 'recharge_num' => 1]);
            }
        }

        if ($res['code'])
        {
            return Result::fail($res['msg']);
        }

        return Result::success();
    }



    /**
     * 类型
     * @return array
     */
    public function classes(): array
    {
        $MoneyLogRepo = new  MoneyClassRepository();
        $data         = $MoneyLogRepo->selectByCondition();

        return Result::success($data);
    }

}
