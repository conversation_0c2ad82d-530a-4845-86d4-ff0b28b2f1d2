<?php
declare (strict_types = 1);

namespace app\common\command\nouse;


use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Config;
use think\facade\Db;
use think\facade\Log;

/**
 * 大表数据迁移
 * Class Migration
 * @package app\common\command
 */
class MigrationAll extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('MigrationAll')->setDescription('the MigrationAll command');
    }




    /**
     * 注单表等大表数据迁移
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        // 获取需清空的表
        $cleanTableArr = Config::load('more/migration')['migration'];

        // 没有清空的表则停止运行
        if(empty($cleanTableArr))
        {
            Log::channel('command')->info('no_table_to_clean_up');
            return;
        }

        $backupDb  = Db::connect('migration');

        $currentDb = Db::connect('mysql');

        $dateBase  = config('database.connections.mysql.database');

        foreach ($cleanTableArr as $val)
        {
            $table    = $val['table'];
            $number   = $val['number'];
            $timeType = $val['time_type'];
            $field    = $val['field'];
            $type     = $val['type'];

            if (!in_array($type,['int','date']))
            {
                continue;
            }

            // 时间不能等于0
            if($number == 0)
            {
                continue;
            }

            //操作数据
            $this->doing($currentDb, $backupDb, $table, $number, $timeType, $field, $type, $dateBase);
        }

    }

    /**
     * 操作数据
     * @param $currentDb
     * @param $backupDb
     * @param $table
     * @param $number
     * @param $timeType
     * @param $field
     * @param $type
     * @param $dateBase
     */
    private function doing($currentDb, $backupDb, $table, $number, $timeType, $field, $type, $dateBase)
    {
        // 当前时间
        $startTime       = (int) microtime(true);
        // 数据大于三个月则开始创建归档数据表
        $strToTime       = strtotime("-{$number} {$timeType} -1 day");
        $lastTime        = ($type == 'int') ? strtotime(date('Y-m-d 00:00:00', $strToTime + 86400)) : date('Y-m-d 00:00:00', $strToTime + 86400);


        // 尝试获取锁
        while (true)
        {
            // 检查是否超过锁定尝试的超时时间
            if (((int) microtime(true) - $startTime) > 57)
            {
                break;
            }

            //获取数据
            $data     = $currentDb->table("{$dateBase}.{$table}")->where($field, '<=', $lastTime)->order('id', 'asc')->find();
          
            if (empty($data))
            {
                break;
            }

            //创建表
            $archiveTable = $this->mkTable($currentDb, $backupDb, $table, $data['create_time']);

            //复制数据
            $this->copyData($backupDb, $currentDb, $archiveTable, $data, $dateBase, $table);

            // usleep( 1 * 10);
        }


    }


    /**
     * 创建表
     * @param $currentDb
     * @param $backupDb
     * @param $table
     * @param $strToTime
     * @return string
     */
    private function mkTable ($currentDb, $backupDb, $table, $strToTime): string
    {
           $month          = date('Ym', $strToTime);

            // 检查存档表是否存在
            $archiveTable   = $table . '_' . $month;
            $sql            = "show tables like '" . $archiveTable . "'";

            $exists         = $backupDb->query($sql);

            // 没有则创建
            if(!$exists)
            {
                // 先在备库创建存档表
                $sql         = "show create table {$table}";
                $tableSqlArr = $currentDb->query($sql);
                $tableSql    = $tableSqlArr[0]['Create Table'];
                $tableSql    = str_replace('ENGINE=InnoDB', 'ENGINE=MyISAM', $tableSql);
                $sql         = preg_replace('/AUTO_INCREMENT=(\d+)/i', '', $tableSql);
                $sql         = str_replace("CREATE TABLE `" . $table . "`", "CREATE TABLE IF NOT EXISTS `" . $archiveTable . "`", $sql);

                unset($tableSql);

                try
                {
                    $backupDb->execute($sql);
                }
                catch (\Exception $e)
                {
                    Log::error("failed_to_create_backup_table: table: {$archiveTable}, sql: {$sql}, error: {$e->getMessage()}");
                    die;
                }
            }

            return $archiveTable;
    }


    /**
     * 复制数据
     */
    private function copyData($backupDb, $currentDb, $archiveTable, $data, $dateBase, $table)
    {

        // 查询是否已经存在相同的数据
        $exists = $backupDb->table($archiveTable)->where('id', '=', $data['id'])->find();

        $errs  = json_encode($data);
    
        if (empty($exists))
        {
            // 如果不存在，则插入数据
            $res   = $backupDb->table($archiveTable)->insert($data);
        

            // 添加失败
            if(empty($res))
            {
                Log::error("source_database_table_data_deletion_failed_add: {$archiveTable}, data: {$errs}");
            }


        }
        
        
        $res = $currentDb->table("{$dateBase}.{$table}")->where('id', '=', $data['id'])->delete();

        var_dump($errs);
        
        // 添加失败
        if(empty($res))
        {
            Log::error("source_database_table_data_deletion_failed_delete: {$archiveTable}, data: {$errs}");
        }


    }


}
