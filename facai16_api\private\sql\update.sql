ALTER TABLE `yuebao`
    CHANGE COLUMN `uid` `uid` INT(11) NOT NULL DEFAULT '0' COMMENT '用户id' AFTER `id`,
    CHANGE COLUMN `money` `money` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额' AFTER `type`,
    ADD COLUMN `finish_money` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '完结金额' AFTER `money`,
    CHANGE COLUMN `info` `info` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '描述' COLLATE 'utf8_general_ci' AFTER `finish_money`,
    CHANGE COLUMN `create_time` `create_time` INT(11) NOT NULL DEFAULT '0' COMMENT '创建时间' AFTER `info`,
    CHANGE COLUMN `update_time` `update_time` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间' AFTER `create_time`,
    <PERSON>ANGE COLUMN `create_at` `create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `update_time`,
    CHANGE COLUMN `update_at` `update_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_at`;

ALTER TABLE `yuebao`
    ADD COLUMN `settle_time` INT(11) NOT NULL DEFAULT '0' COMMENT '结算时间' AFTER `create_time`;


ALTER TABLE `yuebao` ADD INDEX idx_type_finish_settle (type, finish_money, settle_time);

ALTER TABLE `yuebao` ADD INDEX idx_uid_type_finish (uid, type, finish_money);


ALTER TABLE `item` ADD COLUMN `level_income` TINYINT NOT NULL DEFAULT 0 COMMENT '等级收益0按周期1按项目天数' AFTER `level`;
ALTER TABLE `item_order` ADD COLUMN `level_income` TINYINT(4) NOT NULL DEFAULT '0' COMMENT 'vip收益 0周期结算,1项目天数结算' AFTER `is_coupon`;

INSERT INTO `system_set` (`key`, `val`, `class`, `desc`) VALUES ('usdt_recharge_gift', '1', 'basic', 'usdt充值返利');


ALTER TABLE `item`
    CHANGE COLUMN `video_link` `video_link` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '详情视频' COLLATE 'utf8_general_ci' AFTER `update_at`,
    ADD COLUMN `signature` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '签名' AFTER `video_link`,
    ADD COLUMN `signature_time` INT NOT NULL DEFAULT '0' COMMENT '签名时间' AFTER `signature`;

ALTER TABLE `withdraw`
    ADD COLUMN `rate` DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '利息';


ALTER TABLE `shares_log`
    ADD COLUMN `release` DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '每日释放' AFTER `shares`;
ALTER TABLE `shares_log`
    CHANGE COLUMN `money` `money` DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '每股的钱' AFTER `shares`;

CREATE TABLE `message` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `uid` int(11) NOT NULL COMMENT '用户id',
    `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
    `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
    `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
    `is_read` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已读0未读1已读',
    `title` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
    `desc` TEXT(65535) NOT NULL COLLATE 'utf8mb4_general_ci',
    `create_time` INT(11) NOT NULL DEFAULT '0',
    `update_time` INT(11) NOT NULL DEFAULT '0',
    `create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE
)
COMMENT='站内信'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=4
;

ALTER TABLE `user_words_logs` CHANGE COLUMN `words` `words` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '字' AFTER `is_test`;

CREATE TABLE `user_words` (
   `id` int(11) NOT NULL,
   `uid` int(11) NOT NULL COMMENT '用户id',
   `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
   `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
   `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
   `zhi` INT(11) NOT NULL DEFAULT '0'  COMMENT '智',
   `qi` INT(11) NOT NULL DEFAULT '0'  COMMENT '启',
   `wei` INT(11) NOT NULL DEFAULT '0'  COMMENT '未',
   `lai` INT(11) NOT NULL DEFAULT '0'  COMMENT '来',
   `chuang` INT(11) NOT NULL DEFAULT '0'  COMMENT '创',
   `ling` INT(11) NOT NULL DEFAULT '0'  COMMENT '领',
   `wu` INT(11) NOT NULL DEFAULT '0'  COMMENT '无',
   `xian` INT(11) NOT NULL DEFAULT '0'  COMMENT '限',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户集卡' ROW_FORMAT=DYNAMIC;

CREATE TABLE `user_words_logs` (
  `id` int(11) NOT NULL,
  `uid` int(11) NOT NULL COMMENT '用户id',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
  `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
  `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
  `words` INT(11) NOT NULL DEFAULT '0'  COMMENT '字体',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户集卡记录' ROW_FORMAT=DYNAMIC;

CREATE TABLE `shares` (
   `id` int(11) NOT NULL,
   `title` varchar(50) NOT NULL DEFAULT '' COMMENT '股权名称',
   `desc` varchar(50) NOT NULL DEFAULT '' COMMENT '股权描述',
   `img` varchar(50) NOT NULL DEFAULT '' COMMENT '股权图片',
   `shares` INT(11) NOT NULL DEFAULT '0'  COMMENT '股权',
   `cycle` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期',
   `money` INT(11) NOT NULL DEFAULT '0'  COMMENT '每股的钱',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='股权表' ROW_FORMAT=DYNAMIC;

CREATE TABLE `shares_logs` (
   `id` int(11) NOT NULL,
   `uid` int(11) NOT NULL COMMENT '用户id',
   `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
   `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
   `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
   `cycle_start` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期开始',
   `cycle_end` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期结束',
   `status` INT(11) NOT NULL DEFAULT '0'  COMMENT '0进行中1已结束',
   `shares` INT(11) NOT NULL DEFAULT '0'  COMMENT '股权',
   `money` INT(11) NOT NULL DEFAULT '0'  COMMENT '每股的钱',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='股权表记录' ROW_FORMAT=DYNAMIC;



ALTER TABLE `user`
    ADD COLUMN `is_test` TINYINT NOT NULL DEFAULT 0 COMMENT '0正常1测试 冗余字段' AFTER `pin`

ALTER TABLE `user_info`
    ADD COLUMN `invest_not_finish` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户在订购中金额' AFTER `invest_money`;

ALTER TABLE `item_order` ADD COLUMN `principal` DECIMAL(10,2) NOT NULL DEFAULT '0' COMMENT '本金金额' AFTER `update_at`;

ALTER TABLE `item`
    ADD COLUMN `company_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '公司名称' AFTER `deduction`;

ALTER TABLE `item_order`
    ADD COLUMN `company_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '公司名称' AFTER `order_no`;



ALTER TABLE `payment_account`
    ADD COLUMN `bank_owner` VARCHAR(60) NOT NULL DEFAULT '' COMMENT '银行归属人' AFTER `bank_name`;



ALTER TABLE `question`
    CHANGE COLUMN `desc` `desc` TEXT NOT NULL COLLATE 'utf8mb4_general_ci' AFTER `title`;


INSERT INTO `facai4`.`money_class` (`title`) VALUES ('首存奖励');
INSERT INTO `facai4`.`system_set` (`key`, `val`, `class`, `desc`) VALUES ('first_deposit_bonus', '100', 'basic', '首存奖励');

ALTER TABLE `item`
    CHANGE COLUMN `coupon_limit` `coupon_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '执行卡限制' AFTER `update_at`;



CREATE TABLE `question` (
`id` INT(11) NOT NULL AUTO_INCREMENT,
`title` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
`desc` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
`create_time` INT(11) NOT NULL DEFAULT '0',
`update_time` INT(11) NOT NULL DEFAULT '0',
`create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
`update_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='问题列表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=4
;

#
ALTER TABLE `item` ADD COLUMN `coupon_limit` DECIMAL(5,2) NOT NULL DEFAULT '0' COMMENT '执行卡限制' AFTER `update_at`;