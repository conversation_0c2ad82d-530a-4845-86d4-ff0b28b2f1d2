<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\index\service\YueBaoService;
use think\facade\Request;
use think\response\Json;

/**
 * 余额宝
 */
class YueBao
{
    /**
     * 充值
     * @return Json
     */
    public function deposit(): Json
    {

        $params       = Request::only([
            'money' => 0,
        ]);

        $UserRepo     = new UserRepository();

        $id           = $UserRepo->userByHeader('id');

        $RedisLock    = new RedisLock();
        $status       = $RedisLock->lock('YueBao_deposit:' . $id);

        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理订单');
            }

            $YueBaoService    = new YueBaoService();
            $result           = $YueBaoService->deposit($params);

            $RedisLock->unLock('YueBao_deposit:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('YueBao_deposit:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }

    }

    /**
     * 提现
     * @return Json
     */
    public function withdraw(): Json
    {
        $params       = Request::only([
            'money' => 0,
        ]);

        $UserRepo     = new UserRepository();

        $id           = $UserRepo->userByHeader('id');

        $RedisLock    = new RedisLock();
        $status       = $RedisLock->lock('YueBao_withdraw:' . $id);


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理订单');
            }

            $YueBaoService    = new YueBaoService();
            $result           = $YueBaoService->withdraw($params);

            $RedisLock->unLock('YueBao_withdraw:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('YueBao_withdraw:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }
    }

    /**
     * 余额宝记录
     * @return Json
     */
    public function record(): Json
    {
        $params  = Request::only([
            'type'   => '',
        ]);

        $YueBaoService    = new YueBaoService();
        $result           = $YueBaoService->record($params['type']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}