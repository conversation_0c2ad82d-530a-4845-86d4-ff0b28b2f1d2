import {
	getUserId,
	getUserInfo
} from "../utils/request/auth"
import request from "../utils/request/request"
/**
 * 用户绑定的钱包
 */
export function getUserWallet() {
	return request({
		url: `/wallet/getUserWallet`,
		method: 'GET',
	})
}
export function getWalletAddress() {
	return request({
		url: '/user/getWalletAddress',
		method: 'GET'
	})
}
//新增钱包
export function bindingWallet(data) {
	return request({
		url: `/wallet/binding`,
		method: 'POST',
		data
	})
}

//设置钱包密码
export function setPayPassword(data) {
	return request({
		url: `/user/updateUserInfo`,
		method: 'POST',
		data
	})
}

// 申请提现
export function userWithdrawal(data) {
	return request({
		url: `/withdrawal/userWithdrawal`,
		method: 'POST',
		data
	})
}

// 提现记录
export function listWithdrawal(data) {
	return request({
		url: `/user/withdrawal/list`,
		method: 'POST',
		data
	})
}
