<template>
  <el-form
    label-width="80px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="类型名称" required>
      <el-input v-model="form.title" />
    </el-form-item>

    <el-form-item label="汇率" required>
      <el-input v-model="form.rate" />
    </el-form-item>
    <!-- <el-form-item
      label="开启状态"
      prop="status"
      :rules="[
        {
          required: true,
          message: '请选择开启状态',
          trigger: ['change'],
        },
      ]"
    >
      <el-select v-model="form.status" placeholder="开启状态" clearable>
        <el-option
          v-for="item in openEnums"
          :label="item.label"
          :key="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item> -->
    <el-form-item
      label="类型图标"
      prop="img"
      :rules="[{ required: true, message: '请上传图片' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="successUpload"
        :on-error="handleErr"
        :multiple="false"
      >
        <img v-if="form.img" :src="proxy.IMG_BASE_URL + form.img" width="100%" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { getCurrentInstance, nextTick, onMounted, ref } from "vue";
import { openEnums, getLabelByVal } from "@/config/enums";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  img: "",
  rate: "",
  status: "",
});
const props = defineProps(["item"]);

const { proxy } = getCurrentInstance()

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    form.value = Object.assign(form, props.item);
  });
});

const successUpload = (res) => {
  form.value.img = res.data.url;
};

const handleErr = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}
</style>
