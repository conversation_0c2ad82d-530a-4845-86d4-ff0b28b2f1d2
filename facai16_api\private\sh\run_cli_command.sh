#!/bin/bash

# 设置工作目录
WORK_DIR="/path/to/your/thinkphp/project"
cd $WORK_DIR || { echo "目录不存在"; exit 1; }

# 要执行的 CLI 命令
COMMAND="php think your:command"

# 无限循环，每 3 秒执行一次命令
while true; do
    echo "执行命令: $COMMAND"

    # 执行命令
    $COMMAND

    # 检查命令是否成功执行
    if [ $? -ne 0 ]; then
        echo "命令执行失败，正在等待 3 秒后重试..."
        sleep 3
    else
        echo "命令执行成功！"
    fi

    # 等待 3 秒
    sleep 3
done

#chmod +x run_cli_command.sh
#./run_cli_command.sh >> script.log 2>&1