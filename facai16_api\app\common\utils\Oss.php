<?php
namespace app\common\utils;
use OSS\Core\MimeTypes;
use OSS\OssClient;
use OSS\Core\OssException;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Log;

/**
 * oss 上传
 * Class Oss
 * @package app\common\utils
 */
class Oss
{
    protected  $config = [];


    public function __construct()
    {
        $this->config    = Config::get('serve_oss.strategy.default');
    }

    public function getUrl(string $fileName): string
    {
        $endpoint   = str_replace('http://', '', $this->config['Endpoint']);
        return  "https://" . $this->config['Bucket'] . "." . $endpoint . "/"  . $fileName;
    }



    public function multi(string $fileName, $files)
    {
        $position =  Cache::get($fileName);

        empty($position) &&  $position = 0;


        try {

            $ossClient = new OssClient($this->config['KeyId'], $this->config['KeySecret'], $this->config['Endpoint']);
            // 第一次追加上传。第一次追加的位置是0，返回值为下一次追加的位置。后续追加的位置是追加前文件的长度。
            $position  = $ossClient->appendFile($this->config['Bucket'], $fileName, $files, $position);

            Cache::set($fileName, $position);
            return true;

        } catch (\Exception $e) {
            Log::channel('oss')->error(__FUNCTION__ . ": FAILED\n" . $e->getMessage() . "\n");
            return false;
        }

    }
}