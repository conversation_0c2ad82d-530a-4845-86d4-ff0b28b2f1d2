<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editItem('add', {})"
        v-permission
        >添加</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="150" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="type_name" label="钱包类型" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.type, userWalletTypeEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="before" label="详情" width="200">
        <template #default="scope">
          姓名:{{ scope.row.name }}<br />
          <div v-if="scope.row.type == 0">
            银行名称:{{ scope.row.bank_name }}<br />
            支行:{{ scope.row.bank_branch }}<br />
            账号:{{ scope.row.bank_account }}<br />
          </div>
          <div v-else-if="scope.row.type == 1">
            币名称:{{ scope.row.coin_name }}<br />
            币区块链:{{ scope.row.coin_blockchain }}<br />
            账号:{{ scope.row.coin_account }}<br />
          </div>
          <div v-else-if="scope.row.type == 2 || scope.row.type == 3">
            账号:{{ scope.row.alipay_account }}<br />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="默认" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.default, booleanEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="添加时间" width="130" />
      <el-table-column
        label="操作"
        width="200"
        fixed="right"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editItem('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="新增/编辑" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { booleanEnums, getLabelByVal, userWalletTypeEnums } from "@/config/enums";
import EditPop from "./components/userWallet/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  username: "",
  phone: "",
  starttime: "",
  endtime: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);
const formType = ref("add");

onMounted(() => {
  getList();
});

const editItem = (type, row = {}) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url =
    formType.value == "add"
      ? "UserBank/addUserBank"
      : "UserBank/updateUserBank";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      phone: data.phone,
      default: data.default,
      type: data.type,
      bank_name: data.bank_name,
      bank_branch: data.bank_branch,
      bank_account: data.bank_account,
      coin_name: data.coin_name,
      coin_blockchain: data.coin_blockchain,
      coin_account: data.coin_account,
      alipay_account: data.alipay_account,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "UserBank/deleteUserBank?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/UserBank/getUserBankLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
