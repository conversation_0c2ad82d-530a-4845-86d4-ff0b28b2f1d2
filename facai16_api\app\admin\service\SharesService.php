<?php
namespace app\admin\service;

use app\common\repository\SharesLogRepository;
use app\common\repository\SharesRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 股权服务
 */
class SharesService
{
    /**
     * 股权列表
     * @return array
     */
    public function getSharesLists(): array
    {
        $SharesRepo = new SharesRepository();

        $data       = $SharesRepo->paginates([],'*',10,['id'=> 'desc']);

        return Result::success($data);
    }


    /**
     * 获取股权信息
     * @param $id
     * @return array
     */
    public function getSharesInfo($id): array
    {
        $SharesRepo = new SharesRepository();
        $data       = $SharesRepo->findById($id);

        return Result::success($data);
    }


    /**
     * 添加股权
     * @param $params
     * @return array
     */
    public function addShares($params): array
    {
        $SharesRepo = new SharesRepository();
        $res        = $SharesRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新股权
     * @param $params
     * @return array
     */
    public function updateShares($params): array
    {

        $params['update_time'] = Request::time();

        $SharesRepo = new SharesRepository();
        $res        = $SharesRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除股权
     * @param $id
     * @return array
     */
    public function deleteShares($id): array
    {
        $SharesRepo = new SharesRepository();

        $res        = $SharesRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户股权记录
     * @return array
     */
    public function getSharesLogLists(): array
    {
        $SharesRepo = new SharesLogRepository();

        $data       = $SharesRepo->paginates();

        return Result::success($data);
    }


    /**
     * 删除用户股权记录
     * @param $id
     * @return array
     */
    public function deleteSharesLogLists($id): array
    {
        $SharesRepo = new SharesLogRepository();
        $res        = $SharesRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}