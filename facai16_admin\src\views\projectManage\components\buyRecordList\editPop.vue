<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        
        <h4 class="form-title">买家信息</h4>
        <el-form-item label="用户名" required >
            <el-input v-model="form.username" clearable  />
        </el-form-item>
        <el-form-item label="订单号" required >
            <el-input v-model="form.order_no" clearable  />
        </el-form-item>
        <el-form-item label="商品名称" required >
            <el-input v-model="form.item_name" clearable  />
        </el-form-item>
        <el-form-item label="姓名" required >
            <el-input v-model="form.address_name" clearable  />
        </el-form-item>
        <el-form-item label="手机" required >
            <el-input v-model="form.address_phone" clearable  />
        </el-form-item>
        <el-form-item label="市区" required >
            <el-input v-model="form.address_city" clearable  />
        </el-form-item>
        <el-form-item label="详细地址" required >
            <el-input v-model="form.address_place" clearable  />
        </el-form-item>
        
        
      
        <h4 class="form-title">发货信息</h4>
        <el-form-item label="订单状态" required>
            <el-select v-model="form.status" placeholder="" clearable>
                <el-option v-for="item in sendStatusEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="发货公司" required >
            <el-input v-model="form.deliver_title" clearable />
        </el-form-item>
        <el-form-item label="发货单号" required >
            <el-input v-model="form.deliver_order_no" clearable />
        </el-form-item>
        <el-form-item label="发货时间" required >
            <el-input v-model="form.deliver_time" clearable />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import {rolesEnums, getLabelByVal, sendStatusEnums} from '@/config/enums'

const form = ref({
    username: '',
    role: '',
    role_id: '',
    invite_code: '',
    password: '',
    email: '',
    remarks: ''
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
 width: 100%;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>