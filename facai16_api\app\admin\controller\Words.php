<?php
namespace app\admin\controller;

use app\admin\service\WordsService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 集字
 */
class Words
{
    /**
     * 集字列表
     * @return Json
     */
    public function getWordsLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order',
            'name',
            'is_test',
            'limit' => 10,
        ]);

        $WordsService   = new WordsService();
        $data           = $WordsService->getWordsLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 获取集字信息
     * @return Json
     */
    public function getWordsInfo(): Json
    {
        $id             = Request::param('id',0);
        $WordsService   = new WordsService();
        $data           = $WordsService->getWordsInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加集字
     * @return Json
     */
    public function addWords(): Json
    {
        $param          = Request::param();
        $WordsService   = new WordsService();
        $data           = $WordsService->addWords($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新集字
     * @return Json
     */
    public function updateWords(): Json
    {
        $param          = Request::param();
        $WordsService   = new WordsService();
        $data           = $WordsService->updateWords($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除集字
     * @return Json
     */
    public function deleteWords(): Json
    {
        $id             = Request::param('id',0);
        $WordsService   = new WordsService();
        $data           = $WordsService->deleteWords($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 用户集字记录
     * @return Json
     */
    public function getWordsLogLists(): Json
    {
        $WordsService   = new WordsService();
        $data           = $WordsService->getWordsLogLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 用户集字记录
     * @return Json
     */
    public function deleteWordsLogLists(): Json
    {
        $id             = Request::param('id',0);
        $WordsService   = new WordsService();
        $data           = $WordsService->deleteWordsLogLists($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}