<?php
namespace app\admin\service;

use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\WithdrawRepository;
use app\common\utils\BCalculator;
use app\common\utils\Excel;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Db;

/**
 * 提现
 */
class WithdrawService
{

    /**
     * 提现记录
     * @param $params
     * @return array
     */
    public function getWithdrawLists($params): array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order']))
        {
            $where[] = ['order_no', '=', $params['order']];
        }

        if (!empty($params['name']))
        {
            $where[] = ['name', '=', $params['name']];
        }


        if (isset($params['is_test']) && is_numeric($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }

        if (!empty($params['status']))
        {
            $where[] = ['status', '=', $params['status']];
        }

        $WithdrawRepo = new WithdrawRepository();
        $data         = $WithdrawRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 导出
     * @param $params
     * @return array
     */
    public function export($params): array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order']))
        {
            $where[] = ['order_no', '=', $params['order']];
        }

        if (!empty($params['name']))
        {
            $where[] = ['name', '=', $params['name']];
        }

        if (!empty($params['status']))
        {
            $where[] = ['status', '=', $params['status']];
        }

        if (!empty($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }

        $WithdrawRepo = new WithdrawRepository();
        $list         = $WithdrawRepo->limits($where,'*', ['id' => 'desc'],0,10000);

        $fileName     = Excel::createCsvFile('提现' . Date('Y-m-d'), 'withdraw');


        foreach ($list as $row)
        {
            if ($row['status'] == 0) {
                $row['status'] = '成功';
            } elseif ($row['status'] == 1)
            {
                $row['status'] = '审核';
            }elseif ($row['status'] == 2)
            {
                $row['status'] = '失败';
            }

            if ($row['is_test'] == 0) {
                $row['is_test'] = '正常';
            } elseif ($row['is_test'] == 1)
            {
                $row['is_test'] = '测试';
            }

            if ($row['type'] == 0) {
                $row['type'] = '银行卡';
            } elseif ($row['type'] == 1)
            {
                $row['type'] = '数字货币';
            }elseif ($row['type'] == 2)
            {
                $row['type'] = '支付宝';
            }elseif ($row['type'] == 3)
            {
                $row['type'] = '微信';
            }

            Excel::formatDataAppend($row,'withdraw', $fileName);
        }

        $data = [
            'url' => Uri::file('/' . $fileName)
        ];

        return Result::success($data);

    }

    /**
     * 支付信息
     * @param $id
     * @return array
     */
    public function getWithdrawInfo($id): array
    {
        $WithdrawRepo = new WithdrawRepository();
        $data         = $WithdrawRepo->findById($id);

        return Result::success($data);
    }


    /**
     * 用户提现
     * @param array $ids
     * @param int $status
     * @param string $remark
     * @return array
     */
    public function userWithdraw(array $ids, int $status, string $remark): array
    {
        $error = [];

        foreach ($ids as $id)
        {
            $WithdrawRepo = new WithdrawRepository();
            $info         = $WithdrawRepo->findById($id);

            if (!$info)
            {
                $error[] = '订单信息不存在 ' . $id;
                continue;
            }

            $money = BCalculator::calc($info['amount_real'])->add($info['handling_fee'])->result();

            //
            Db::startTrans();

            if ($status == 2)
            {

                try {

                    $MoneyLogRepo = new MoneyLogRepository();


                    $res = $MoneyLogRepo->fund($info['uid'], $money,MoneyClass::WITHDRAW_REJECT, $info['id'],'取款拒绝: +' . $info['amount'] . '元');

                    if ($res['code'])
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .' '. $res['msg'];
                        continue;
                    }

                    $update = [
                       'status'      => $status,
                       'update_time' => time(),
                       'remark'      => $remark
                    ];

                    $res = $WithdrawRepo->updateById($id, $update);

                    if (!$res)
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .'修改状态失败';
                        continue;
                    }


                    // 提交事务
                    Db::commit();

                } catch (\Exception $exception)
                {
                    // 回滚事务
                    Db::rollback();
                    //错误日志
                    $error[]  = Record::exception('admin', $exception,'ItemService->pay');
                }
            }
            else
            {

                try {

                    $update = [
                        'status'      => $status,
                        'update_time' => time(),
                        'remark'      => $remark
                    ];

                    $res = $WithdrawRepo->updateById($id, $update);

                    if (!$res)
                    {
                        $error[]  ='订单 ' . $id . '修改状态失败';
                        Db::rollback();
                        continue;
                    }

                    $UserInfoRepo = new UserInfoRepository();

                    $update = [
                        'withdraw_money' => $money,
                        'update_time'    => time(),
                        'withdraw_num'   => 1
                    ];

                    $res    = $UserInfoRepo->statistic($info['uid'], $update);

                    if (!$res)
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .'修改状态失败';
                        continue;
                    }

                    Db::commit();

                }catch (\Exception $exception)
                {
                    // 回滚事务
                    Db::rollback();
                    //错误日志
                    $error[]  = Record::exception('admin', $exception,'ItemService->pay2');
                }

            }

        }


        if ($error)
        {
            return Result::fail(join(',',$error));
        }

        return Result::success();
    }
}
