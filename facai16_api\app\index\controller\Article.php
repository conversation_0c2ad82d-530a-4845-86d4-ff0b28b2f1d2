<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\ArticleService;
use think\facade\Request;
use think\response\Json;

/**
 * 新闻
 */
class Article
{
    /**
     * 新闻列表
     */
    public function lists(): <PERSON>son
    {
        $params         = Request::only(['class_id' => 0]);

        $ArticleService = new ArticleService();

        $result         = $ArticleService->lists($params['class_id']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 新闻内容
     * @return Json
     */
    public function info(): Json
    {
        $params             = Request::only(['id' => '','code' => '']);


        $ArticleService     = new ArticleService();
        $result             = $ArticleService->info($params['id'],$params['code']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

}