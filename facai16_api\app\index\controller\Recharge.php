<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\RechargeService;
use think\facade\Request;
use think\response\Json;

/**
 * 充值
 */
class Recharge
{

    /**
     * 充值方式
     * @return Json
     */
    public function method(): Json
    {
        $params             = Request::only(['money' => 0.00, 'type' => 0]);

        $RechargeService    = new RechargeService();
        $result             = $RechargeService->method($params['money'], $params['type']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 充值
     * @return Json
     */
    public function recharge(): Json
    {
        $params             = Request::only(['id' => 0, 'money' => 0, 'account' => '','name' => '', 'img' => '','type'=> 0]);

        $RechargeService    = new RechargeService();

        $result             = $RechargeService->recharge($params['id'],$params['money'], $params['account'], $params['name'], $params['img'], $params['type']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);

    }



    /**
     * 充值记录
     * @return Json
     */
    public function record(): Json
    {

        $RechargeService    = new RechargeService();
        $result             = $RechargeService->record();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}