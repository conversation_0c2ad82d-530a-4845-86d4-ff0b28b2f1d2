<template>
  <el-menu
    :default-active="activeKey"
    class="el-menu-vertical-demo layout-menu"
    :collapse="props.isCollapse"
    @open="handleOpen"
    @close="handleClose"
    :unique-opened="true"
  >
    <el-menu-item index="1" @click="gotohome">
      <el-icon><component is="menu"></component></el-icon>
      <template #title>首页</template>
    </el-menu-item>
    <el-sub-menu
      :index="item.index"
      :key="item.index"
      v-for="(item, index) in menuList"
    >
      <template #title>
        <el-icon>
          <component :is="item.icon"></component>
        </el-icon>
        <span>{{ item.name }}</span>
      </template>
      <el-menu-item-group>
        <el-menu-item
          @click="selectItem(item, child)"
          :index="child.index"
          :key="child.path"
          v-for="(child, cIndex) in item.children"
          ><el-icon> <ArrowRight /> </el-icon>{{ child.name }}</el-menu-item
        >
      </el-menu-item-group>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import store from "@/store";
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router/dist/vue-router";
const menuList = ref([
  {
    name: "用户管理",
    index: "1",
    path: "/",
    icon: "User",
    children: [
      {
        name: "用户列表",
        index: "1-1",
        path: "/userList",
      },

      {
        name: "钱包管理",
        index: "1-8",
        path: "/userWallet",
      },
      {
        name: "地址管理",
        index: "1-9",
        path: "/userAddress",
      },
      {
        name: "实名审核",
        index: "1-11",
        path: "/realnameList",
      },
    ],
  },
  {
    name: "资金管理",
    index: "11",
    path: "/",
    icon: "Coin",
    children: [
      {
        name: "普通充值",
        index: "11-2",
        path: "/chargeList",
      },
      {
        name: "usdt充值",
        index: "11-15",
        path: "/UsdtChargeList",
      },
      {
        name: "提款列表",
        index: "11-3",
        path: "/withdrawList",
      },
      {
        name: "流水记录",
        index: "11-4",
        path: "/flowList",
      },
      {
        name: "余额宝记录",
        index: "11-41",
        path: "/yuebaoLogLists",
      },
      {
        name: "转账记录",
        index: "11-42",
        path: "/transferLogLists",
      },
    ],
  },
  {
    name: "抽奖管理",
    index: "12",
    path: "/",
    icon: "GoldMedal",
    children: [
      {
        name: "集字列表",
        index: "12-8",
        path: "/collectWords",
      },
      {
        name: "集字记录",
        index: "12-10",
        path: "/wordsLogLists",
      },
      {
        name: "大奖设置",
        index: "12-9",
        path: "/bigRaffleLists",
      },
      {
        name: "大奖记录",
        index: "12-11",
        path: "/bigRaffleLogLists",
      },
      {
        name: "幸运抽奖",
        index: "12-3",
        path: "/lottory",
      },
      {
        name: "抽奖记录",
        index: "12-4",
        path: "/lottoryDetails",
      },
    ],
  },
  {
    name: "团队管理",
    index: "13",
    path: "/",
    icon: "Baseball",
    children: [
      {
        name: "用户等级设置",
        index: "13-5",
        path: "/memberLevel",
      },
      {
        name: "用户登录记录",
        index: "13-13",
        path: "/userLoginLog",
      },
      {
        name: "用户升级记录",
        index: "13-12",
        path: "/levelLogLists",
      },
      {
        name: "团队等级设置",
        index: "13-7",
        path: "/teamLevel",
      },
      {
        name: "团队升级记录",
        index: "13-21",
        path: "/teamLevelLogLists",
      },
      {
        name: "团队奖励记录",
        index: "13-22",
        path: "/teamRewardLogLists",
      },
    ],
  },
  {
    name: "签到管理",
    index: "14",
    path: "/",
    icon: "Present",
    children: [
      {
        name: "签到记录",
        index: "14-4",
        path: "/signInLists",
      },
      {
        name: "签到奖品领取记录",
        index: "14-6",
        path: "/giftsList",
      },
      {
        name: "签到奖品设置",
        index: "14-11",
        path: "/signRewardTypes",
      },
    ],
  },
  {
    name: "股权管理",
    index: "15",
    path: "/",
    icon: "Guide",
    children: [
      {
        name: "股权列表",
        index: "15-12",
        path: "/stockLists",
      },
      {
        name: "股权记录",
        index: "15-13",
        path: "/stockLogLists",
      },
    ],
  },
  {
    name: "项目管理",
    index: "2",
    path: "/",
    icon: "WindPower",
    children: [
      {
        name: "项目分类",
        index: "2-1",
        path: "/projectType",
      },
      {
        name: "项目列表",
        index: "2-2",
        path: "/projectList",
      },
      {
        name: "购买记录",
        index: "2-6",
        path: "/buyRecordList",
      },
      {
        name: "结算记录",
        index: "2-7",
        path: "/projectLogList",
      },
    ],
  },
  {
    name: "支付模块",
    index: "8",
    path: "/",
    icon: "Wallet",
    children: [
      {
        name: "支付类型",
        index: "8-3",
        path: "/rechargeType",
      },
      {
        name: "支付上游",
        index: "8-5",
        path: "/paymentUpper",
      },
      {
        name: "支付账号",
        index: "8-7",
        path: "/paymentAccount",
      },
      {
        name: "支付渠道",
        index: "8-6",
        path: "/paymentChannel",
      },
      {
        name: "银行列表",
        index: "8-8",
        path: "/bankList",
      },
    ],
  },
  {
    name: "商品管理",
    index: "3",
    path: "/",
    icon: "Handbag",
    children: [
      {
        name: "商品分类",
        index: "3-1",
        path: "/goodsType",
      },
      {
        name: "商品列表",
        index: "3-2",
        path: "/goodsList",
      },

      {
        name: "购买记录",
        index: "3-3",
        path: "/goodsRecords",
      },
    ],
  },
  {
    name: "运营管理",
    index: "4",
    path: "/",
    icon: "Pointer",
    children: [
      {
        name: "优惠券列表",
        index: "23-6",
        path: "/discountList",
      },
      {
        name: "轮播图",
        index: "4-15",
        path: "/bannerList",
      },
      {
        name: "站内信",
        index: "41-15",
        path: "/messageLists",
      },
      {
        name: "文章类型",
        index: "4-10",
        path: "/newsTypeList",
      },
      {
        name: "文章资讯",
        index: "4-1",
        path: "/newsList",
      },

      // {
      //     name: '站内信',
      //     index: '4-5',
      //     path: '/letters'
      // },

      {
        name: "短信记录",
        index: "4-8",
        path: "/smsCodeLists",
      },
      {
        name: "用户反馈",
        index: "4-9",
        path: "/userFeedback",
      },
      {
        name: "问答列表",
        index: "4-12",
        path: "/questionList",
      },
    ],
  },
  {
    name: "系统设置",
    index: "5",
    path: "/",
    icon: "Setting",
    children: [
      {
        name: "参数设置",
        index: "5-1",
        path: "/paramsSetting",
      },
      // {
      //     name: '网站介绍',
      //     index: '5-2',
      //     path: '/siteDesc'
      // },

      {
        name: "谷歌密钥",
        index: "5-4",
        path: "/googleKeys",
      },
    ],
  },
  // {
  //     name: '视频管理',
  //     index: '6',
  //     path: '/',
  //     icon: 'Film',
  //     children: [
  // {
  //     name: '视频分类',
  //     index: '6-1',
  //     path: '/videoType'
  // },{
  //     name: '视频管理',
  //     index: '6-2',
  //     path: '/videoList'
  // },
  //         {
  //             name: '宣传视频',
  //             index: '6-3',
  //             path: '/xuanchuanVideo'
  //         }
  //     ]
  // },
  {
    name: "后台管理",
    index: "7",
    path: "/",
    icon: "Guide",
    children: [
      {
        name: "后台用户",
        index: "7-1",
        path: "/backendUser",
      },
      {
        name: "登录日志",
        index: "7-2",
        path: "/loginLog",
      },
    ],
  },
]);

const router = useRouter();
const selectItem = (item, child) => {
  // const selectItem
  let arr = [
    {
      name: "首页",
      index: "1",
      path: "/home",
    },
    item,
    child,
  ];
  store.commit("updateBreakCum", arr);
  router.push(child.path);
};

const activeKey = ref("1");
const route = useRoute();

watch(
  () => route.path,
  (newPath, oldPath) => {
    menuList.value.map((item) => {
      let res = item.children.filter((_t) => _t.path === newPath);
      if (res.length > 0) {
        activeKey.value = res[0].index;
      }
    });
  },
  { immediate: true }
);

const gotohome = () => {
  let arr = [
    {
      name: "首页",
      index: "1",
      path: "/home",
    },
  ];
  store.commit("updateBreakCum", arr);
  router.push("/home");
};

const handleOpen = (key, keyPath) => {};
const handleClose = (key, keyPath) => {};

const props = defineProps(["isCollapse"]);
</script>

<style lang="less" scoped>
.layout-menu {
  // width: 220px;
  height: 100%;
  background-color: #eee;
  transition: all 0.25s;
  overflow-y: auto;
}
</style>
