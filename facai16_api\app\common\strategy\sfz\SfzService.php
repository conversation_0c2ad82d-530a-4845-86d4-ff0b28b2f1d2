<?php
namespace app\common\strategy\sfz;
use app\common\strategy\sfz\lib\AliYun;
use app\common\strategy\sfz\lib\JuHe;
use app\common\strategy\sfz\lib\Tencent;
use think\facade\Config;

/**
 * 实名认证
 * Class SmsService
 * @package app\common\strategy\sms
 */
class SfzService
{
    public function send($data)
    {
        $config    = Config::get('serve_sfz.default');
        switch ($config)
        {
            case 'AliYun':
                $strategy = new AliYun();
                break;
            case 'JuHe':
                $strategy = new JuHe();
                break;
            default:

                $strategy = new Tencent();
                break;
        }


        $SfzContext = new SfzContext($strategy);

        return $SfzContext->sendMessage($data);
    }

}