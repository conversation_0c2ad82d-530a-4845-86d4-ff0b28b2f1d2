# 用户登录限制修复代码差异对比

## 文件修改概览

本次修复涉及3个核心文件的修改：
1. `facai16_api/app/index/service/LoginService.php` - 登录时状态检查
2. `facai16_api/app/index/middleware/LoginMiddleware.php` - 中间件实时检查  
3. `facai16_api/app/admin/service/UserService.php` - 强制退出功能

## 详细代码差异

### 1. LoginService.php 修改

#### 修改位置1：login()方法中添加状态检查

**文件**: `facai16_api/app/index/service/LoginService.php`
**行号**: 第57行后

```diff
        //账号或者密码错误
        if(empty($user))
        {
            return Result::fail('账号或者密码错误');
        }

+       // 检查用户登录限制状态
+       $UserStateRepo = new UserStateRepository();
+       $banLogin = $UserStateRepo->valueByCondition(['uid' => $user['id']], 'ban_login');
+
+       if ($banLogin == 1) {
+           return Result::fail('账号已被限制登录，请联系客服');
+       }

        $ip         = IpAddress::getIp();
        $time       = Request::time();
```

#### 修改位置2：register()方法中添加状态检查

**文件**: `facai16_api/app/index/service/LoginService.php`
**行号**: 第338行后

```diff
        $UserStateRepo = new UserStateRepository();

        $isTest        = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']],'is_test');

+       // 检查用户登录限制状态
+       $banLogin = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']], 'ban_login');
+
+       if ($banLogin == 1) {
+           return Result::fail('账号已被限制登录，请联系客服');
+       }

        //登入失败
        if (!$res)
```

### 2. LoginMiddleware.php 修改

#### 修改位置：handle()方法中添加实时状态检查

**文件**: `facai16_api/app/index/middleware/LoginMiddleware.php`
**行号**: 第57行前

```diff
        //非名单接口
        $UserRepo  = new UserRepository();
        $res       = $UserRepo->userByHeader('id');

        if (empty($res))
        {
            $info     = json_encode(['code' => 444, '登录失效,请重新登录','data'=>[]],320);
            $type     = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);

            throw new HttpResponseException($response);
        }

+       // 检查用户登录限制状态
+       $userState = $UserRepo->userStateByHeader('ban_login');
+       if (!empty($userState) && $userState['ban_login'] == 1) {
+           $info = json_encode(['code' => 445, 'msg' => '账号已被限制登录，请联系客服','data'=>[]], 320);
+           $type = array("Content-type", "application/json");
+           $response = Html::create('','json',256)->content($info)->header($type);
+           
+           throw new HttpResponseException($response);
+       }

        return $next($request);
```

### 3. UserService.php 修改

#### 修改位置：updateUserState()方法中添加强制退出逻辑

**文件**: `facai16_api/app/admin/service/UserService.php`
**行号**: 第328行后

```diff
        $UserStateRepo = new UserStateRepository();
        $res           = $UserStateRepo->updateByCondition(['uid' => $params['uid']], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

+       // 如果设置为禁止登录，立即清除用户token
+       if ($params['ban_login'] == 1) {
+           $UserRepo = new UserRepository();
+           $UserRepo->updateByCondition(['id' => $params['uid']], ['token' => '']);
+       }

        $UserRepo = new UserRepository();
        $res      = $UserRepo->updateByCondition(['id' => $params['uid']], ['is_test' => $params['is_test'],'update_time' => time()]);
```

## 新增功能说明

### 1. 错误码扩展
- **原有**: 444 - 登录失效，请重新登录
- **新增**: 445 - 账号已被限制登录，请联系客服

### 2. 检查逻辑
- **登录检查**: 在用户名密码验证通过后，检查ban_login状态
- **中间件检查**: 在每次API请求时，检查当前用户的ban_login状态
- **强制退出**: 当管理员设置ban_login=1时，立即清除用户token

### 3. 数据流程
```
用户登录请求 → 验证用户名密码 → 检查ban_login状态 → 允许/拒绝登录
已登录用户请求 → 验证token → 检查ban_login状态 → 允许/拒绝访问
管理员设置限制 → 更新ban_login状态 → 清除用户token → 用户被踢出
```

## 依赖关系

### 必需的类和方法
1. **UserStateRepository**
   - `valueByCondition(['uid' => $uid], 'ban_login')` - 获取用户ban_login状态

2. **UserRepository**
   - `userStateByHeader('ban_login')` - 通过header获取用户状态
   - `updateByCondition(['id' => $uid], ['token' => ''])` - 清除用户token

3. **响应处理类**
   - `Html::create()` - 创建JSON响应
   - `HttpResponseException` - 抛出HTTP异常

### 数据库字段
- `user_state.ban_login` - 用户登录限制状态（0=允许，1=限制）
- `user.token` - 用户登录token

## 测试用例

### 测试数据准备
```sql
-- 创建测试用户（如果不存在）
INSERT INTO user (username, phone, password) VALUES ('testuser', '13800138000', 'password123');

-- 设置用户状态
INSERT INTO user_state (uid, ban_login) VALUES (1, 0); -- 正常状态
UPDATE user_state SET ban_login = 1 WHERE uid = 1;     -- 限制状态
```

### API测试
```bash
# 测试登录限制
curl -X POST "http://your-domain/api/login" \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","password":"password123"}'

# 测试中间件检查
curl -X GET "http://your-domain/api/user/info" \
  -H "Accept-Token: user_token_here"
```

## 回滚方案

如果需要回滚修改，只需要删除添加的代码行：

### LoginService.php 回滚
删除第57行后添加的6行代码和第338行后添加的6行代码

### LoginMiddleware.php 回滚
删除第57行前添加的8行代码

### UserService.php 回滚
删除第328行后添加的4行代码

## 性能影响评估

### 数据库查询增加
- 每次登录增加1次user_state表查询
- 每次API请求增加1次user_state表查询（通过现有方法）
- 管理员设置状态时增加1次user表更新

### 优化建议
- 考虑将用户状态缓存到Redis中
- 对于高频访问用户，可以考虑状态缓存策略
- 监控数据库查询性能，必要时添加索引

## 安全性增强

### 防护机制
1. **即时生效**: 状态变更立即生效，无延迟
2. **双重验证**: 登录时检查 + 运行时检查
3. **强制退出**: 状态变更时立即清除认证信息
4. **明确反馈**: 用户收到明确的限制提示

### 攻击防护
- 防止被限制用户通过保持连接绕过限制
- 防止通过缓存的token继续访问系统
- 提供清晰的错误信息，避免信息泄露
