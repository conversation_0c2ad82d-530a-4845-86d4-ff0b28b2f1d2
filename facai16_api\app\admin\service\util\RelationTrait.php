<?php
namespace app\admin\service\util;

use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Db;

/**
 * 用户关系
 */
trait RelationTrait
{
    /**
     * 关系转移
     * @param $fromUser
     * @param $toUser
     * @return array
     */
    public function relationshipTransfer($fromUser, $toUser): array
    {

        $UserRepo     = new UserRepository();
        $fromUserInfo = $UserRepo->findByCondition(['phone' => $fromUser]);
        $toUserInfo   = $UserRepo->findByCondition(['phone' => $toUser]);


        // 验证用户是否存在
        if (!$fromUserInfo)
        {
            return Result::fail('来源用用户为空');
        }

        if (!$toUserInfo)
        {
            return Result::fail('转移用用户为空');
        }

        $fromUserId = $fromUserInfo['id'];
        $toUserId   = $toUserInfo['id'];

        // 检查循环引用
        if ($this->isCircularReference($fromUserId, $toUserId))
        {
            return Result::fail('循环引用!!');
        }

        Db::startTrans();

        try {

            // 1. 更新被转移用户(4)的上级关系
            $this->updateUserRelations($fromUserId, $toUserId);

            // 2. 递归更新所有下级用户(7,8等)的关系
            $this->updateAllSubordinates($fromUserId);

            // 3. 重新赋值 v1 v2 v3
            $this->updateUserLevel($fromUserId, $toUserId);

            Db::commit();

            return Result::success();

        } catch (\Exception $e) {
            Db::rollback();
            return Result::fail('转移失败');
        }

    }


    /**
     * 检查循环引用
     */
    protected function isCircularReference(int $fromUserId, int $toUserId): bool
    {
        // 检查toUserId是否已经是fromUserId的下级
        $path = $this->getUserRelationPath($toUserId);

        return in_array($fromUserId, $path);
    }

    /**
     * 获取用户的所有上级ID路径
     */
    protected function getUserRelationPath(int $userId): array
    {

        $path               = [];
        $where              = [];
        $where[]            = ['uid', '=', $userId];
        $UserRelationRepo   = new UserRelationRepository();
        $relations          = $UserRelationRepo->selectByCondition($where,'*',['level' => 'asc']);


        foreach ($relations as $relation)
        {
            // 跳过无效的上级ID (0或自己)
            if ($relation['top_id'] <= 0 || $relation['top_id'] == $userId)
            {
                continue;
            }

            // 确保不重复添加相同的上级
            if (!in_array($relation['top_id'], $path))
            {
                $path[] = $relation['top_id'];
            }
        }

        return $path;
    }

    /**
     * 更新单个用户的上级关系
     */
    protected function updateUserRelations(int $userId, int $newParentId): array
    {
        $UserRelationRepo   = new UserRelationRepository();

        $where      = [];
        $where[]    = ['uid', '=', $userId];
        $res        = $UserRelationRepo->deleteByCondition($where);

        // 删除旧关系
        if (!$res)
        {
            return Result::fail('删除旧关系失败');
        }

        // 8 7
        // 建立直接上级关系
        $relations = [
            [
                'uid'           => $userId, // 8
                'top_id'        => $newParentId, //7
                'level'         => 1,
                'create_time'   => time(),
                'update_time'   => time(),
            ]
        ];

        $where              = [];
        $where[]            = ['uid', '=', $newParentId];
        // 获取新上级的所有上级关系
        $superiors          = $UserRelationRepo->selectByCondition($where);

        // 7
        // 添加间接上级关系
        foreach ($superiors as $superior)
        {
            $relations[] =  [
                'uid'           => $userId, // 8
                'top_id'        => $superior['top_id'], //7
                'level'         => $superior['level'] + 1,
                'create_time'   => time(),
                'update_time'   => time(),
            ];
        }

        $res = $UserRelationRepo->insertsAll($relations);

        if (!$res)
        {
            return Result::fail('添加关系失败');
        }

        return Result::success();
    }

    /**
     * 递归更新所有下级用户关系
     */
    protected function updateAllSubordinates(int $userId) //7
    {
        $where      = [];
        $where[]    = ['top_id', '=', $userId];
        $where[]    = ['level', '=', 1];

        // 获取所有直接下级
        $UserRelationRepo   = new UserRelationRepository();
        $directSubordinates = $UserRelationRepo->columnByCondition($where,'uid');

        //8 9
        foreach ($directSubordinates as $subordinateId)
        {
            $where          = [];
            $where[]        = ['uid', '=', $subordinateId];
            $where[]        = ['level', '=', 1];

            // 获取该下级的直接上级(应该是当前用户)
            $directParent   = $UserRelationRepo->valueByCondition($where,'top_id');

            //8
            if ($directParent != $userId)
            {
                continue; // 确保只处理真正的直接下级
            }

            // 重新构建该下级的关系链 8 7
            $this->updateUserRelations($subordinateId, $userId);

            // 递归处理下级的下级 8
            $this->updateAllSubordinates($subordinateId);
        }
    }

    /**
     * 更新用户等级
     * @param int $fid
     * @param int $tid
     * @return array
     */
    protected function updateUserLevel(int $fid, int $tid):array
    {
        $UserRepo = new UserRepository();
        $pUser    = $UserRepo->findById($tid);

        $update  = [
            'v1_id'         => $pUser['id'] ?? 0,
            'v1_name'       => $pUser['phone'] ?? '',
            'v2_id'         => $pUser['v1_id'] ?? 0,
            'v2_name'       => $pUser['v1_name'] ?? '',
            'v3_id'         => $pUser['v2_id'] ?? 0,
            'v3_name'       => $pUser['v2_name'] ?? '',
            'update_time'   => time(),
        ];

        $res = $UserRepo->updateById($fid, $update);

        if (!$res)
        {
            return Result::fail('添加关系失败');
        }

        return Result::success();
    }


}