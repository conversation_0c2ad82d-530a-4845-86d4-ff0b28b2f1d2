<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="问题" required>
      <el-input v-model="form.title" clearable />
    </el-form-item>
    <el-form-item label="答复" required>
      <el-input v-model="form.desc" clearable />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { rolesEnums } from "@/config/enums";
import {
  onBeforeUnmount,
  nextTick,
  ref,
  shallowRef,
  onMounted,
  getCurrentInstance,
} from "vue";
import fileUploadHoook from "@/hooks/fileUpload";
import { htmlDecodeByRegExp } from "@/utils/utils";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  desc: "",
});
const props = defineProps(["item"]);

onMounted(() => {
  nextTick(() => {
    form.value = Object.assign(form.value, props.item);

  });
});



defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}

.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/deep/ .el-radio-group {
  width: 220px;
}

.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}

/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
