import { getCurrentInstance, ref } from "vue";


// 已弃用
const fileUploadHoook = () => {

  const { proxy } = getCurrentInstance();
  const fileLoading = ref(false);

  const fileUpload = async (file, files) => {
    fileLoading.value = true;
    const res = await proxy.$http({
      method: "post",
      url: "index/upload",
      data: {
        file: file,
      },
    });
    fileLoading.value = false;
    
  };

  const fileChange = async (file, files) => {
    // fileUpload(file)
    // return file;
  };

  return {
    fileUpload,
    fileChange,
  };
};

export default fileUploadHoook;
