<template>
	<view>
		<view class="showBigPicWrap inset">
			<view class="fixed-view">
				<top-status-bar></top-status-bar>
				<view class="px-24 py-35">
					<view class="fcc f-pr color-#fff t-32 lh-45">
						<view class="fcc w-88 h-88 r-100 bg-#000 f-pa left-0" @click="closeBigPic">
							<image src="/static/x.png" mode="aspectFit" size-48></image>
						</view>
						<view class="tab h-88 fcc-h" :class="{on:tabIndex == 0}" @click="tabIndex = 0">
							<text>
								视频
							</text>
							<view class="line"></view>
						</view>
						<view class="tab h-88 fcc-h ml-106" :class="{on:tabIndex == 1}" @click="tabIndex = 1">
							<text>图片</text>
							<view class="line"></view>
						</view>
					</view>
				</view>
			</view>
			<view class="bigPicWrap color-#fff">
				<swiper v-if="tabIndex==1" class="swiper-view" @change="e => current = e.detail.current" :indicator-dots="false" :autoplay="false" :interval="3000" :duration="1000">
					<swiper-item v-for="(item,index) in getPics" :key="index" >
						
						<image :src="item.url" mode="aspectFit"></image>
					
					</swiper-item>
				</swiper>
				<video :src="getVideo[0].url" v-else></video>
			</view>
			<view class="fcc f-pa left-0 right-0 t-24 color-#fff bottom-0 h-200
			" v-if="tabIndex==1">
				{{current+1 }}/ {{getPics.length}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"show-big-pic-video",
		props: {
			list: {
				type: Array,
				default: ()=>{
					return []
				}
			},
		},
		data() {
			return {
				tabIndex:0,
				current: 0,
			};
		},
		computed: {
			getPics() {
				return this.list.filter(item => item.type != "video")
			},
			getVideo() {
				return this.list.filter(item => item.type == "video")
			}
		},
		mounted() {
			console.log(this.getPics)
		},
		methods: {
			closeBigPic() {
				this.$emit('close')
			}
		},
	}
</script>

<style lang="scss" scoped>
	
	.bigPicWrap{
		
	}
	
</style>