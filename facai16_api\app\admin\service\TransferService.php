<?php
namespace app\admin\service;


use app\common\cache\RedisLock;
use app\common\jobs\LevelUpJob;
use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\TransferRepository;
use app\common\repository\UserInfoRepository;
use app\common\utils\Record;
use app\common\utils\Result;
use think\console\Input;
use think\facade\Db;
use think\facade\Request;

class TransferService
{

    /**
     * 转载记录
     * @param $params
     * @return array
     */
    public function getTransferLog($params): array
    {

        $where = [];


        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }


        $TransferRepo = new TransferRepository();
        $data         = $TransferRepo->paginates($where);

        return Result::success($data);
    }


    /**
     * 用户充值
     * @param array $ids
     * @param int $status
     * @param string $remark
     * @return array
     */
    public function userTransfer(array $ids, int $status, string $remark): array
    {

        $error = [];

        foreach ($ids as $id)
        {
            $TransferRepo = new TransferRepository();
            $info         = $TransferRepo->findById($id);

            if (!$info)
            {
                $error[] = '订单信息不存在 ' . $id;
                continue;
            }

            $RedisLock    = new RedisLock();
            $_status      = $RedisLock->lock('userTransfer:' . $id);

            if (empty($_status))
            {
                continue;
            }

            //
            if ($status == 1)
            {

                Db::startTrans();

                try {

                    $MoneyLogRepo   = new MoneyLogRepository();

                    $phone = substr($info['to_phone'],0,3).'****'.substr($info['to_phone'],strlen($info['to_phone'])-4,4);
                    $txt   = '用户转账(出):' . $info['money'] . '到' . $phone . '元';
                    $res   = $MoneyLogRepo->fund($info['uid'],$info['money'],MoneyClass::TRANSFER_OUT, $id, $txt);

                    if ($res['code'])
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .' '. $res['msg'];
                        $RedisLock->unLock('userTransfer:' . $id);
                        continue;
                    }

                    $txt   = '用户转账(进):' . $info['money'] . '收' . $phone. '元';
                    $res   =  $MoneyLogRepo->fund($info['to_uid'],$info['money'],MoneyClass::TRANSFER_IN, $id,$txt);

                    if ($res['code'])
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .' '. $res['msg'];
                        $RedisLock->unLock('userTransfer:' . $id);
                        continue;
                    }

                    $update = [
                        'status'        => 1,
                        'update_time'   => time(),
                        'remark'        => $remark,
                    ];

                    $res          = $TransferRepo->updateById($info['id'], $update);

                    if (!$res)
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .'修改状态失败';
                        $RedisLock->unLock('userTransfer:' . $id);
                        continue;
                    }

                    // 提交事务
                    Db::commit();

                } catch (\Exception $exception)
                {
                    // 回滚事务
                    Db::rollback();
                    //错误日志
                    $error[]  = Record::exception('admin', $exception,'ItemService->userTransfer');
                    $RedisLock->unLock('userTransfer:' . $id);
                }
            }
            else
            {

                $TransferRepo = new TransferRepository();

                $update = [
                    'status'      => 2,
                    'update_time' => time(),
                    'remark'      => $remark,
                ];

                $res = $TransferRepo->updateById($id, $update);

                if (!$res)
                {
                    $error[]  ='订单 ' . $id . '修改状态失败';
                }

                $RedisLock->unLock('userTransfer:' . $id);
            }

        }


        if ($error)
        {
            return Result::fail(join(',',$error));
        }

        return Result::success();
    }

}