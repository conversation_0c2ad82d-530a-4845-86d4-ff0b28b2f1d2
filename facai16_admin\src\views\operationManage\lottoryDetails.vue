<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="用户名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名称" width="130" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="raffle_id" label="抽奖id" width="130" />
      <el-table-column prop="raffle_name" label="抽奖产品名称" width="130" />
      <el-table-column prop="amount" label="金额" width="130" />
      <el-table-column prop="desc" label="描述" width="130" />
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column prop="update_at" label="更新时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRoute } from 'vue-router'

const route = useRoute()

const searchForm = ref({
  phone: "",
  username: "",
  starttime: "",
  endtime: "",
});

onMounted(() => {
  if (route.query && route.query.phone) {
    searchForm.value.phone = route.query.phone
  }
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Raffle/getRaffleLogLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
