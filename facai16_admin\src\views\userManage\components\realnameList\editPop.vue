<template>
    <el-form label-width="100px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="身份证名称" required >
            <el-input v-model="form.sfz_name" clearable  />
        </el-form-item>
        <el-form-item label="身份证号码" required >
            <el-input v-model="form.sfz_number" clearable />
        </el-form-item>
        
        <el-form-item label="实名状态" required>
            <el-select v-model="form.status" placeholder="" clearable>
                <el-option v-for="item in sfzStatusEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="用户名称" required >
            <el-input v-model="form.username" clearable />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import {sfzStatusEnums} from '@/config/enums'

const form = ref({
    sfz_name: '',
    sfz_number: '',
    status: '',
    username: '',
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>