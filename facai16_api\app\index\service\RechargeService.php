<?php
namespace app\index\service;

use app\common\model\PaymentClass;
use app\common\repository\PaymentAccountRepository;
use app\common\repository\PaymentChannelRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserRepository;
use app\common\strategy\pay\PayService;
use app\common\utils\BCalculator;
use app\common\utils\Order;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Request;


class RechargeService
{

    /**
     * 充值
     * @param $money
     * @param $type
     * @return array
     */
    public function method($money, $type): array
    {
        $where              = [];
        $where[]            = ['status','=',0];
        $where[]            = ['min','<=',$money];
        $where[]            = ['max','>=',$money];

        $type && $where[]   = ['class_id','=', PaymentClass::USDT];

        $PaymentChannelRepo = new PaymentChannelRepository();
        $data               = $PaymentChannelRepo->selectByCondition($where);

        foreach ($data as &$item)
        {

            $item['class_img'] = Uri::file($item['class_img']);

            if ($item['style'] == PaymentClass::ACCOUNTS)
            {
                $PaymentAccountRepo   = new PaymentAccountRepository();
                $account              = $PaymentAccountRepo->findById((int)$item['accounts']);

                if ($account)
                {
                    $account['img']         = Uri::file($account['img']);
                    $item['account_info']   = $account;
                }
            }
        }

        return Result::success($data);
    }

    /**
     * 充值
     * @param $id
     * @param $money
     * @param $account
     * @param $name
     * @param $img
     * @param $type
     * @return array
     */
    public function recharge($id, $money, $account, $name, $img, $type): array
    {

        $PaymentChannelRepo  = new PaymentChannelRepository();
        $UserRepo            = new UserRepository();
        $user                = $UserRepo->userByHeader();
        $state               = $UserRepo->userStateByHeader();
        $PaymentRepo         = new PaymentRepository();
        $channel             = $PaymentChannelRepo->findById($id);


        //请先进行实名认证
        if($state['sfz_status'])
        {
            return Result::fail('请先进行实名认证');
        }


        $SystemSetRepo    = new SystemSetRepository();

        $period           = $SystemSetRepo->valueByCondition(['key'=> 'recharge_time'],'val');


        if($period)
        {
            $periods = explode('~', $period);


            if(strtotime(date('H:i')) < strtotime($periods[0]) || strtotime(date('H:i')) > strtotime($periods[1]))
            {
                return Result::fail('存款时间' . $period);
            }
        }

        $SystemSetRepo    = new SystemSetRepository();
        $minRecharge      = $SystemSetRepo->valueByCondition(['key'=> 'min_recharge'],'val');

        if($type)
        {
            $huilv     = $SystemSetRepo->valueByCondition(['key'=> 'usdt_huilv'],'val');

            $huilv2    = BCalculator::calc($money)->div($huilv)->result();

            if($money < $huilv2)
            {
                return Result::fail('最少存款' . $huilv2 .'U');
            }
        }
        else
        {
            if($money < $minRecharge)
            {
                return Result::fail('最少存款' . $minRecharge);
            }
        }

        //
        if (empty($id))
        {
            return $this->manualUpload($money, $type, $name, $img);
        }

        if (empty($channel))
        {
            return  Result::fail('没有支付通道');
        }

        $accountName = '';

        //如果是支付账号
        if ($channel['style'] == PaymentClass::ACCOUNTS)
        {
            $PaymentAccountRepo = new PaymentAccountRepository();
            $AccountInfo        = $PaymentAccountRepo->findById($channel['accounts']);

            if (empty($AccountInfo))
            {
                return Result::fail('没有此充值账号信息');
            }

            $accountName = $AccountInfo['title'] ?? '';
        }

        $time    = Request::time();

        $orderNo = Order::encode('PAY-' . $time . rand(1000,9999));

        $real    = BCalculator::calc($money)->mul($channel['class_rate'])->result();


        $remark  = $channel['class_id'] == 4 ? '您的账户充值 ' . $money . 'U' : '您的账户充值RMB ' . $money . '元';

        $insert = [
            'uid'               => $user['id'],
            'username'          => $user['username'],
            'phone'             => $user['phone'],
            'is_test'           => $state['is_test'],
            'channel_id'        => $channel['id'],
            'channel_name'      => $channel['title'],
            'class_id'          => $channel['class_id'],
            'class_name'        => $channel['class_name'],
            'account'           => $account,
            'order_no'          => $orderNo,
            'style'             => $channel['style'],
            'amount'            => $money,
            'amount_real'       => $real,
            'img'               => $img,
            'name'              => $name,
            'create_time'       => $time,
            'update_time'       => $time,
            'account_id'        => $channel['accounts'],
            'account_name'      => $accountName,
            'rate'              => $channel['class_rate'],
            'remark'            => $remark,
        ];




        $recharge_id        = $PaymentRepo->insertsGetId($insert);

        if($channel['style'] == PaymentClass::FRAMES || $channel['style'] == PaymentClass::JUMP)
        {
            $PayService = new PayService();
            $uri        = $PayService->send($insert['order_no'], $money, $channel['upper'], $channel['code']);
            
            if (strlen($uri) > 1)
            {
                return Result::success(['url' => $uri, 'api' => 1 , 'is_jump'=> $channel['style']],__('jumping_please_wait'));
            }
            else
            {
                $PaymentRepo->updateById($recharge_id,['status' => 2]);

                return Result::success(['url' => '', 'api' => 1, 'is_jump' => 0],__('jumping_please_wait'));
            }
        }
        else
        {
            return Result::success(['url' => '', 'api' => 0],__('recharge_application_successful_please_be_patient_and_wait'));
        }

    }

    /**
     * 手动上传
     * @param $money
     * @param $type
     * @param $name
     * @param $img
     * @return array
     */
    public function manualUpload($money,$type,$name,$img):array
    {
        $UserRepo            = new UserRepository();
        $user                = $UserRepo->userByHeader();
        $PaymentRepo         = new PaymentRepository();

        $SystemSetRepo = new SystemSetRepository();

        if ($type)
        {
            $huilv  = $SystemSetRepo->valueByCondition(['key'=> 'usdt_huilv'],'val');
        }
        else
        {
            $huilv = 1;
        }

        $time          = Request::time();

        $orderNo = Order::encode('PAY-' . $time . rand(1000,9999));

        $real    = BCalculator::calc($money)->mul($huilv)->result();

        $insert = [
            'uid'               => $user['id'],
            'username'          => $user['username'],
            'phone'             => $user['phone'],
            'is_test'           => $user['is_test'],
            'channel_id'        => 0,
            'channel_name'      => '手动上传',
            'class_id'          => $type ? 4 : 0,
            'class_name'        => $type ? 'USDT' : '人民币',
            'account'           => '',
            'order_no'          => $orderNo,
            'style'             => 2,
            'amount'            => $money,
            'amount_real'       => $real,
            'img'               => $img,
            'name'              => $name,
            'create_time'       => $time,
            'update_time'       => $time,
            'account_id'        => 0,
            'account_name'      => '',
            'rate'              => $huilv,
        ];

        $id        = $PaymentRepo->insertsGetId($insert);

        if (!$id)
        {
            return Result::fail('保存失败');
        }

        return Result::success(['url' => '', 'api' => 0],__('recharge_application_successful_please_be_patient_and_wait'));
    }

    /**
     * 充值记录
     * @return array
     */
    public function record(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $PaymentRepo        = new PaymentRepository();

        $where              = [];
        $where[]            = ['uid', '=',$user['id']];
        $data               = $PaymentRepo->paginates($where);

        return Result::success($data);
    }

}