<?php
namespace app\admin\service;

use app\common\cache\RedisLock;
use app\common\jobs\LevelUpJob;
use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserInfoRepository;
use app\common\utils\BCalculator;
use app\common\utils\Excel;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Db;
use think\facade\Request;

/**
 * 支付订单
 */
class PaymentService
{
    /**
     * 支付订单
     * @param $params
     * @return array
     */
    public function getPaymentLists($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }

        if (isset($params['status']) && is_numeric($params['status']))
        {

            $where[] = ['status', '=', $params['status']];
        }

        if (isset($params['is_test']) && is_numeric($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }

        if (isset($params['style']))
        {
            $where[] = ['style', 'in', $params['style']];
        }

        if (isset($params['class_id']) && is_numeric($params['class_id']))
        {

            $where[] = ['class_id', '=', $params['class_id']];
        }


        $PaymentRepo = new PaymentRepository();
        $data        = $PaymentRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 导出
     * @param $params
     * @return array
     */
    public function export($params):array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }

        if (isset($params['status']) && is_numeric($params['status']))
        {

            $where[] = ['status', '=', $params['status']];
        }

        if (isset($params['is_test']) && is_numeric($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }

        if (isset($params['style']))
        {
            $where[] = ['style', 'in', $params['style']];
        }



        $PaymentRepo = new PaymentRepository();
        $list        = $PaymentRepo->limits($where,'*', ['id' => 'desc'],0,10000);

        $fileName    = Excel::createCsvFile('充值' . Date('Y-m-d'), 'recharge');


        foreach ($list as $row)
        {
            if ($row['status'] == 0)
            {
                $row['status'] = '成功';
            } elseif ($row['status'] == 1)
            {
                $row['status'] = '审核';
            }elseif ($row['status'] == 2)
            {
                $row['status'] = '失败';
            }

            if ($row['is_test'] == 0)
            {
                $row['is_test'] = '正常';
            } elseif ($row['is_test'] == 1)
            {
                $row['is_test'] = '测试';
            }

            if ($row['style'] == 0)
            {
                $row['style'] = '框架';
            } elseif ($row['style'] == 1)
            {
                $row['style'] = '跳转';
            }elseif ($row['style'] == 2)
            {
                $row['style'] = '内置账号';
            }

            Excel::formatDataAppend($row,'recharge', $fileName);
        }

        $data = [
            'url' => Uri::file('/' . $fileName)
        ];

        return Result::success($data);
    }

    public function getPaymentInfo($id): array
    {
        $PaymentRepo = new PaymentRepository();
        $data        = $PaymentRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 用户充值
     * @param array $ids
     * @param int $status
     * @param string $remark
     * @return array
     */
    public function userRecharge(array $ids, int $status, string $remark): array
    {

        $error = [];

        foreach ($ids as $id)
        {
            $PaymentRepo  = new PaymentRepository();
            $info         = $PaymentRepo->findById($id);

            if (!$info)
            {
                $error[] = '订单信息不存在 ' . $id;
                continue;
            }

            $RedisLock    = new RedisLock();
            $_status      = $RedisLock->lock('userRecharge:' . $id);

            if (empty($_status))
            {
                continue;
            }

            //
            if ($status == 0)
            {
                $UserInfoRepo       = new UserInfoRepository();
//                $UserRepo           = new UserRepository();
//                $SystemSetRepo      = new SystemSetRepository();
//                $MoneyLogRepo       = new MoneyLogRepository();

                $userInfo           = $UserInfoRepo->findByCondition(['uid' => $info['uid']]);
//                $topId              = $UserRepo->valueByCondition(['id'=> $info['uid']], 'v1_id');

                Db::startTrans();

                try {

//                    if ($userInfo['recharge_money'] == 0)
//                    {
//                        if ($topId)
//                        {
//                            $raffle  = $SystemSetRepo->valueByCondition(['key' => 'invite_raffle'], 'val');
//
//                            if ($raffle)
//                            {
//                                $res = $MoneyLogRepo->raffle($topId, $raffle,MoneyClass::RAFFLE_ADD, $info['id'],"直聘成员:获得抽奖{$raffle}次");
//
//                                if ($res['code'])
//                                {
//                                    Db::rollback();
//                                    $error[]  ='订单 ' . $id .' '. $res['msg'];
//                                    continue;
//                                }
//                            }
//
//
//                            $points = $SystemSetRepo->valueByCondition(['key' => 'invite_points'], 'val');
//
//                            if ($points)
//                            {
//                                $res  = $MoneyLogRepo->point($topId, $points,MoneyClass::POINTS_ADD, $info['id'],'直聘奖励:获得兑换券' . $points);
//
//                                if ($res['code'])
//                                {
//                                    Db::rollback();
//                                    $error[]  ='订单 ' . $id .' '. $res['msg'];
//                                    continue;
//                                }
//                            }
//
//                            $moneys = $SystemSetRepo->valueByCondition(['key' => 'invite_moneys'], 'val');
//
//                            if ($moneys)
//                            {
//                                $res  = $MoneyLogRepo->fund($topId, $moneys,MoneyClass::INVITE_MEMBER_BONUS, $info['id'],'直聘奖励:获得作业额度' . $moneys);
//
//                                if ($res['code'])
//                                {
//                                    Db::rollback();
//                                    $error[]  ='订单 ' . $id .' '. $res['msg'];
//                                    continue;
//                                }
//                            }
//                        }


//                        $bonus = $SystemSetRepo->valueByCondition(['key' => 'first_deposit_bonus'], 'val');
//
//                        if ($bonus)
//                        {
//                            $res  = $MoneyLogRepo->fund($info['uid'], $bonus,MoneyClass::FIRST_DEPOSIT_BONUS, $info['id'],'首存奖励:获得' . $bonus);
//
//                            if ($res['code'])
//                            {
//                                Db::rollback();
//                                $error[]  ='订单 ' . $id .' '. $res['msg'];
//                                continue;
//                            }
//                        }
//                    }

                    $txt            = $info['class_id'] == 4 ? 'USDT' : '元';

                    $MoneyLogRepo  = new MoneyLogRepository();
                    $res           = $MoneyLogRepo->fund($info['uid'], $info['amount_real'],MoneyClass::RECHARGE, $info['id'],'用户充值: +' . $info['amount'] . $txt);

                    if ($res['code'])
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .' '. $res['msg'];
                        $RedisLock->unLock('userRecharge:' . $id);
                        continue;
                    }


                    $update = [
                        'recharge_money' => $info['amount_real'],
                        'recharge_num'   => 1,
                        'update_time'    => Request::time(),
                    ];

                    $res    = $UserInfoRepo->statistic($info['uid'], $update);

                    if ($res['code'])
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .' '. $res['msg'];
                        $RedisLock->unLock('userRecharge:' . $id);
                        continue;
                    }

                    $update = [
                        'status'      => $status,
                        'update_time' => time(),
                        'remark'      => $remark,
                    ];

                    $res = $PaymentRepo->updateById($id, $update);

                    if (!$res)
                    {
                        Db::rollback();
                        $error[]  ='订单 ' . $id .'修改状态失败';
                        $RedisLock->unLock('userRecharge:' . $id);
                        continue;
                    }


                    if ($info['class_id'] == 4)
                    {
                        $SystemSetRepo = new SystemSetRepository();
                        $usdtGift      = $SystemSetRepo->valueByCondition(['key' => 'usdt_recharge_gift'],'val');

                        if (!empty($usdtGift))
                        {
                            $gift          = BCalculator::calc($info['amount_real'])->mul($usdtGift)->div(100)->result();
                            $MoneyLogRepo  = new MoneyLogRepository();
                            $res           = $MoneyLogRepo->fund($info['uid'], $gift,MoneyClass::USDT_RECHARGE_GIFT, $info['id'],'USDT充值奖励: +' . $gift. '元');

                            if ($res['code'])
                            {
                                Db::rollback();
                                $error[]  ='订单 ' . $id .'修改状态失败';
                                $RedisLock->unLock('userRecharge:' . $id);
                                continue;
                            }
                        }
                    }

                    // 提交事务
                    Db::commit();

                    queue(LevelUpJob::class, $info['uid']);

                } catch (\Exception $exception)
                {
                    // 回滚事务
                    Db::rollback();
                    //错误日志
                    $error[]  = Record::exception('admin', $exception,'ItemService->userRecharge');
                    $RedisLock->unLock('userRecharge:' . $id);
                }
            }
            else
            {

                $update = [
                    'status'      => $status,
                    'update_time' => time(),
                    'remark'      => $remark,
                ];

                $res = $PaymentRepo->updateById($id, $update);

                if (!$res)
                {
                    $error[]  ='订单 ' . $id . '修改状态失败';
                }

                $RedisLock->unLock('userRecharge:' . $id);

            }

        }


        if ($error)
        {
            return Result::fail(join(',',$error));
        }

        return Result::success();
    }







}
