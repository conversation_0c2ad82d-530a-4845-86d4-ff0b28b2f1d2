<?php
namespace app\index\service;

use app\common\model\Money;
use app\common\model\MoneyClass;
use app\common\repository\GoodsLogRepository;
use app\common\repository\GoodsRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserAddressRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Order;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Db;
use think\facade\Request;


/**
 * 商店
 * Class ShopService
 * @package app\home\service
 */
class GoodsService
{

    /**
     * 拼团
     * @param $type
     * @param $limit
     * @return array
     */
    public function lists($type, $limit): array
    {
        $GoodsRepository = new GoodsRepository();

        $where           = [];

        if ($type)
        {
            $where[]    = ['class_id', '=', $type];
        }

        $data           = $GoodsRepository->paginates($where,'*',$limit);

        foreach ($data['data'] as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }


        return Result::success($data);
    }

    /**
     * 详情
     * @param int $id
     * @return array
     */
    public function info(int $id): array
    {
        $GoodsRepository = new GoodsRepository();
        $data            = $GoodsRepository->findById($id);
        $data['img']     = Uri::file($data['img']);

        return Result::success($data);
    }

    /**
     * 支付
     * @param int $id
     * @return array
     */
    public function pay(int $id): array
    {
        $GoodsRepo          = new GoodsRepository();
        $GoodsLogRepo       = new GoodsLogRepository();
        $UserRepo           = new UserRepository();
        $UserAddressRepo    = new UserAddressRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $user               = $UserRepo->userByHeader();
        $userInfo           = $UserRepo->userInfoByHeader();
        $userState          = $UserRepo->userStateByHeader();
        $goods              = $GoodsRepo->findById($id);

        if(empty($goods))
        {
            return Result::fail('暂无数据');
        }

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $address    = $UserAddressRepo->findByCondition($where);

        //请先添加默认地址
        if(empty($address))
        {
            return  Result::fail('请先添加默认地址');
        }


        Db::startTrans();

        try {

            //余额不足
            if($userInfo['user_points'] < $goods['price'])
            {
                Db::rollback();
                return  Result::fail('积分不足');
            }


            $orderNo = Order::uniqueNo();

            $res     = $MoneyLogRepo->point($user['id'], $goods['price'],MoneyClass::POINTS_DEL, $orderNo,'积分扣减' . $goods['price']);

            if ($res['code'])
            {
                Db::rollback();
                return  Result::fail($res['msg']);
            }


            //加入拼团记录
            $insert = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $userState['is_test'],
                'img'           => $goods['img'],
                'goods_id'      => $goods['id'],
                'goods_title'   => $goods['title'],
                'order_no'      => $orderNo,
                'money'         => $goods['price'],
                'address_name'  => $address['address_name'],
                'address_phone' => $address['address_phone'],
                'address_city'  => $address['address_city'],
                'address_place' => $address['address_place'],
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];


            $res = $GoodsLogRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return  Result::fail('保存失败');
            }


            // 提交事务
            Db::commit();

            //拼团成功
            return  Result::success([],__('successful_group_buying'));

        } catch (\Exception $exception)
        {
            // 回滚事务
            Db::rollback();

            Record::exception('service', $exception,'GoodsService->pay');

            //购买失败
            return  Result::fail('购买失败');
        }
    }

    /**
     * 记录
     * @param int $limit
     * @return array
     */
    public function record(int $limit): array
    {
        $UserRepo           = new UserRepository();
        $GoodsLogRepo       = new GoodsLogRepository();

        $user       = $UserRepo->userByHeader();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $data       = $GoodsLogRepo->paginates($where, '*', $limit, ['id' => 'desc']);

        foreach ($data['data'] as &$val)
        {
            $val['img']             =  Uri::file($val['img']);

            if ($val['deliver_time'] == 0)
            {
                $val['deliver_time'] = '-';
            }
            else
            {
                $val['deliver_time'] =  date('Y-m-d H:i:s', $val['deliver_time']);
            }

        }

        return  Result::success($data);
    }

}