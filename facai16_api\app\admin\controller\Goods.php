<?php
namespace app\admin\controller;

use app\admin\service\GoodsService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 产品
 */
class Goods
{

    /**
     * 产品列表
     * @return Json
     */
    public function getGoodsLists(): Json
    {
        $params        = Request::only([
            'limit'  => '200'
        ]);

        $GoodsService = new GoodsService();
        $data         = $GoodsService->getGoodsLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取产品信息
     * @return Json
     */
    public function getGoodsInfo(): Json
    {
        $id           = Request::param('id',0);
        $GoodsService = new GoodsService();
        $data         = $GoodsService->getGoodsInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加产品
     * @return Json
     */
    public function addGoods(): Json
    {
        $param        = Request::param();
        $GoodsService = new GoodsService();
        $data         = $GoodsService->addGoods($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新产品
     * @return Json
     */
    public function updateGoods(): Json
    {
        $param        = Request::param();
        $GoodsService = new GoodsService();
        $data         = $GoodsService->updateGoods($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除产品
     * @return Json
     */
    public function deleteGoods(): Json
    {
        $id           = Request::param('id',0);
        $GoodsService = new GoodsService();
        $data         = $GoodsService->deleteGoods($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 获取产品类型列表
     * @return Json
     */
    public function getGoodsClassLists(): Json
    {
        $GoodsService = new GoodsService();
        $data         = $GoodsService->getGoodsClassLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取产品类型信息
     * @return Json
     */
    public function getGoodsClassInfo(): Json
    {
        $id           = Request::param('id',0);
        $GoodsService = new GoodsService();
        $data         = $GoodsService->getGoodsClassInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加产品类型
     * @return Json
     */
    public function addGoodsClass(): Json
    {
        $param        = Request::param();
        $GoodsService = new GoodsService();
        $data         = $GoodsService->addGoodsClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新产品类型
     * @return Json
     */
    public function updateGoodsClass(): Json
    {
        $param        = Request::param();
        $GoodsService = new GoodsService();
        $data         = $GoodsService->updateGoodsClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除产品类型
     * @return Json
     */
    public function deleteGoodsClass(): Json
    {
        $id           = Request::param('id',0);
        $GoodsService = new GoodsService();
        $data         = $GoodsService->deleteGoodsClass($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    public function goodsCart()
    {

    }
}