<?php

namespace app\admin\middleware;


use Closure;
use think\Request;
use think\Response;


/**
 * 登入中间件
 * Class LoginMiddleware
 * @package app\admin\middleware
 */
class AuthVerifyMiddleware
{

    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next): Response
    {
//        $SystemUserRepo = new SystemUserRepository();
//        $SystemUserRepo->adminLoginUserInfo();
//
//        if($type == 1 && $this->adminUser['id'] == 1) return true;
//        if($type == 2 && $this->adminUser['role'] == 1) return true;
//
        return $next($request);
    }

}