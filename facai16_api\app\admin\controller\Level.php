<?php
namespace app\admin\controller;

use app\admin\service\LevelService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 等级
 */
class Level
{

    /**
     * 等级列表
     * @return Json
     */
    public function getLevelLists(): Json
    {
        $LevelService   = new LevelService();
        $data           = $LevelService->getLevelLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取等级信息
     * @return Json
     */
    public function getLevelInfo(): Json
    {
        $id           = Request::param('id',0);
        $LevelService = new LevelService();
        $data         = $LevelService->getLevelInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加 等级
     * @return Json
     */
    public function addLevel(): <PERSON>son
    {
        $param        = Request::param();
        $LevelService = new LevelService();
        $data         = $LevelService->addLevel($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新等级
     * @return Json
     */
    public function updateLevel(): Json
    {
        $param        = Request::param();
        $LevelService = new LevelService();
        $data         = $LevelService->updateLevel($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除等级
     * @return Json
     */
    public function deleteLevel(): Json
    {
        $id           = Request::param('id',0);
        $LevelService = new LevelService();
        $data         = $LevelService->deleteLevel($id);
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 升级记录
     * @return mixed
     */
    public function getLevelLogLists(): Json
    {
        $LevelService = new LevelService();
        $data         = $LevelService->getLevelLogLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}