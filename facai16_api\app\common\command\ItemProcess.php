<?php
declare (strict_types = 1);

namespace app\common\command;

use app\common\repository\ItemRepository;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 项目进度
 * @package app\common\command
 */
class ItemProcess extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('ItemProcess')->setDescription('the ItemProcess command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        $time     = time();

        $ItemRepo = new ItemRepository();

        $where    = [];
        $where[]  = ['progress', '<', 100];
        $where[]  = ['progress_time', '<=', $time];

        $items    = $ItemRepo->selectByCondition($where);

        foreach ($items as $item)
        {
            $nextTime          = $time + $item['progress_cycle'];
            $progress          = $item['progress'] + $item['progress_rate'];

            if ($progress > 100)
            {
                $update  = [
                    'progress'      => 100,
                    'progress_time' => $nextTime,
                    'status'        => 2,
                ];

            }
            else
            {
                $update  = [
                    'progress'      => $progress,
                    'progress_time' => $nextTime
                ];
            }

            $ItemRepo->updateById($item['id'], $update);
        }
    }



}

