<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * BaJiPay
 */
class BaJiPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'BaJiPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'pay_memberid'     => $this->config['mch_id'],
            'pay_orderid'      => $data['orderNo'],
            'pay_applydate'    => date('Y-m-d H:i:s'),
            'pay_bankcode'     => $data['channel'],
            'pay_amount'       => $data['money'],
            'pay_notifyurl'    => CompleteRequest('/api/callback/' . self::CHANNEL),
            'pay_callbackurl'  => CompleteRequest('/api/callback/' . self::CHANNEL)
        ];

        $param['pay_md5sign']  = $this->signature($param);

        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        $uri            = $result['data']['pay_url'] ?? '';

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"memberid=*********&orderid=**************&transaction_id=*********24415102100&amount=2248_1300&datetime=*********24452&returncode=00&sign=F179F634E7B92CFF1FB6531AD70AD422&attach=":"","memberid":"*********","orderid":"**************","transaction_id":"*********24415102100","amount":"2248.1300","datetime":"*********24452","returncode":"00","sign":"F179F634E7B92CFF1FB6531AD70AD422","attach":""}';
//        $param = json_decode($param,true);

//        $sign      = $param['sign'];
//
//        $param     = Arrays::withOut($param,['sign', 'attach']);

//        $signature = $this->signature($param);

//        if ($signature != $sign)
//        {
//            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
//            return 'fail';
//        }

        $orderId  = $param['orderid'] ?? '';
        $status   = $param['returncode'] ?? 0;

        if ($status != '00')
        {
            return 'fail';
        }

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'OK';
        }
        else
        {
            return 'fail';
        }


    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return strtoupper($sign);
    }

}