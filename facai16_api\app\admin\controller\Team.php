<?php
namespace app\admin\controller;

use app\admin\service\TeamService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 团队等级表
 */
class Team
{

    /**
     * 团队等级列表
     * @return Json
     */
    public function getTeamLists(): Json
    {
        $params        = Request::only([
            'title'        => '',
            'limit'        => 10
        ]);


        $TeamService    = new TeamService();
        $data           = $TeamService->getTeamLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加 团队等级
     * @return Json
     */
    public function addTeam(): Json
    {
        $params        = Request::only([
            'title'        => '',
            'user'         => '',
            'invest'       => '',
            'commission'   => '',
            'signin'       => '',

        ]);

        $TeamService  = new TeamService();
        $data         = $TeamService->addTeam($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新团队等级
     * @return Json
     */
    public function updateTeam(): Json
    {
        $params        = Request::only([
            'title'        => '',
            'user'         => '',
            'invest'       => '',
            'commission'   => '',
            'signin'       => '',
            'id'           => 0

        ]);

        $TeamService  = new TeamService();
        $data         = $TeamService->updateTeam($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除团队等级
     * @return Json
     */
    public function deleteTeam(): Json
    {
        $id           = Request::param('id',0);
        $TeamService  = new TeamService();
        $data         = $TeamService->deleteTeam($id);
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 升级记录
     * @return mixed
     */
    public function getTeamLogLists(): Json
    {
        $params        = Request::only([
            'username'     => '',
            'phone'        => '',
            'limit'        => 10,
            'starttime'    => '',
            'endtime'      => '',
        ]);

        $TeamService  = new TeamService();
        $data         = $TeamService->getTeamLogLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 团队奖励记录
     * @return Json
     */
    public function getTeamGiftLists(): Json
    {
        $params        = Request::only([
            'username'     => '',
            'phone'        => '',
            'limit'        => 10,
            'starttime'    => '',
            'endtime'      => '',
        ]);

        $TeamService  = new TeamService();
        $data         = $TeamService->getTeamGiftLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}