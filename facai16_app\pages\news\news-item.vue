<template>
	<view class="mx-24 f-pr mb-24 r-16 overflow">
		<image src="/static/index/itembg.png" mode="aspectFit" class="h-182 w-full block">
		</image>
		<view class="f-pa inset flex">
			<view>
				<image :src="item.img" mode="aspectFill" class="block w-162 h-162"></image>
			</view>
			<view class="flex1 px-20 py-16 flex flex-col justify-between">
				<view>
					<view class="c2 t-26 lh-34 ddd mb-4 fw-700">
						{{item.title}}
					</view>
					<view class="ddd c6 t-24 lh-33">
						{{item.desc}}
					</view>
				</view>
				<view class=" c6 t-24 lh-33">
					{{item.create_time}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item']
	}
</script>

<style>
</style>