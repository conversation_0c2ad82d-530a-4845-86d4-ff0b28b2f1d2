<?php

namespace app\common\strategy\sms\lib;

use app\common\strategy\sms\SmsInterface;
use app\common\utils\Result;
use think\facade\Config;
use think\facade\Log;

/**
 * 聚合短信
 * Class JuHe
 * @package app\common\strategy\sms
 */
class JuHe implements SmsInterface
{

    /**
     * 发动短信
     * @param $phone
     * @param $smscode
     * @return array
     */
    public function send($phone, $smscode): array
    {
        $config   = Config::get('serve_sms.strategy.JuHe');
        //短信接口的URL
        $sendUrl  = $config['url'];
        $tplId    = $config['tpl_id'];

        $smsConf = [
            'key'       => $config['key'],
            //接受短信的用户手机号码
            'mobile'    => $phone,
            //您申请的短信模板ID，根据实际情况修改
            'tpl_id'    => $tplId,
            'vars'      => '{"code":' . $smscode . '}'
        ];

        $String     = http_build_query($smsConf);
        $content    = $this->juhecurl($sendUrl, $String,1);
        $content    = json_decode($content,true);

        if($content['error_code'] == 0)
        {
            return Result::success();
        }else
        {
            Log::channel('sms')->error(json_encode($content));
            return Result::fail();
        }
    }


    /**
     * 请求接口返回内容
     * @param string $url [请求的URL地址]
     * @param bool $params [请求的参数]
     * @param int $ispost
     * @return  string
     */
    public function juhecurl($url,$params=false,$ispost=0)
    {
        $httpInfo = array();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22');
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        if ($ispost)
        {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else
        {
            if ($params)
            {
                curl_setopt($ch, CURLOPT_URL, $url . '?' . $params);
            } else
            {
                curl_setopt($ch, CURLOPT_URL, $url);
            }
        }

        $response = curl_exec($ch);

        if ($response === FALSE)
        {
            return false;
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $httpInfo = array_merge($httpInfo, curl_getinfo($ch));
        curl_close($ch);
        return $response;
    }
}
