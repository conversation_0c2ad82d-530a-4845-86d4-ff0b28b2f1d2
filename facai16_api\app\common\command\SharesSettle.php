<?php

namespace app\common\command;

use app\common\jobs\UpdateUserStateJob;
use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\SharesLogRepository;
use app\common\repository\TeamGiftRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\utils\BCalculator;
use app\common\utils\Numeral;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Config;
use think\facade\Db;


/**
 * 测试
 * Class Backup
 * @package app\common\command
 */
class SharesSettle extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('SharesSettle')->setDescription('the SharesSettle command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function execute(Input $input, Output $output)
    {
        // 获取当前时间戳
        $SharesLogRepo = new SharesLogRepository();

        $SharesLogRepo->where('status','=',0)->whereRaw("cycle_start <= cycle_end")->chunk(100, function ($member)
        {
            $UserInfoRepo  = new UserInfoRepository();
            $SharesLogRepo = new SharesLogRepository();

            foreach ($member as $value)
            {

                Db::startTrans();

                try {

                    $cycle  = $value['cycle_start'];

                    $status = 0;

                    if ($cycle >= $value['cycle_end'])
                    {
                        $status = 1;
                    }

                    $update = [
                        'cycle_start'   => $cycle,
                        'status'        => $status,
                        'update_time'   => time()
                    ];

                    $res = $SharesLogRepo->updateById($value['id'], $update);


                    if (!$res)
                    {
                        Db::rollback();
                        continue;
                    }

                    $amount       = $value['release'];

                    $MoneyLogRepo = new MoneyLogRepository();

                    $res          = $MoneyLogRepo->fund($value['uid'], $amount,MoneyClass::EQUITY_DIVIDENDS, $value['id'],'股权分红: + ' . $amount  .'元');

                    if ($res['code'])
                    {
                        Record::log('command','股权失败1');
                        Db::rollback();
                        continue;
                    }

                    $res     = $UserInfoRepo->statistic($value['uid'],['shares_money' => $amount]);

                    if ($res['code'])
                    {
                        Record::log('command','股权失败2');
                        Db::rollback();
                        continue;
                    }

                    Db::commit();

                }catch (\Exception $exception)
                {
                    Record::exception('command', $exception, '股权失败3');
                    Db::rollback();
                }

            }

        });

    }

}

