<?php

namespace app\common\strategy\sms\lib;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use app\common\strategy\sms\SmsInterface;
use app\common\utils\Result;
use think\facade\Config;
use think\facade\Log;

/**
 * 阿里云
 */
class AliYun implements SmsInterface
{

    /**
     * 发动短信
     * @param $phone
     * @param $smscode
     * @return array
     */
    public function send($phone, $smscode): array
    {
        $_config   = Config::get('serve_sms.strategy.AliYun');
        //这个是验证码
        $code      = ['code' => $smscode];
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html。
        $config = new \Darabonba\OpenApi\Models\Config([
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            "accessKeyId"     => $_config['access_key_id'],
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            "accessKeySecret" => $_config['access_key_secret']
        ]);

        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        $config->endpoint = "dysmsapi.aliyuncs.com";

        $client = new Dysmsapi($config);

        $sendSmsRequest                = new SendSmsRequest();
        $sendSmsRequest->phoneNumbers  = $phone;
        $sendSmsRequest->signName      = $_config['sms_sign_name'];
        $sendSmsRequest->templateCode  = $_config['sms_template_code'];
        $sendSmsRequest->templateParam = json_encode($code);


        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->sendSmsWithOptions($sendSmsRequest, new RuntimeOptions([]));

            return Result::success();
        }
        catch (\Exception $error) {

            dd($error->getMessage());
            Log::channel('sms')->error($phone .'*'. $smscode . '*' . $error->getMessage());

            return Result::fail();
        }

    }
}
