<template>
	<view class="f-pr">
		<image src="/static/count/bank-bg.png" mode="widthFix" class="w-full block r-20"></image>
		<view class="f-pa inset fc justify-between">
			<view class="pl-40">
				<view class="fc  mb-12">
					<view class=" t-32 lh-45 mr-15 fw-600">
						{{item.bank_name}}
					</view>
					<view @click="eye = !eye">
						<image src="/static/eye1.png" mode="aspectFit" class="block size-40" v-if="eye"></image>
						<image src="/static/eye2.png" mode="aspectFit" class="block size-40" v-else></image>
					</view>
				</view>
				<view class=" t-28 lh-40 fw-600 text-white">
					<text v-if="eye">{{item.bank_account}}</text>
					<text v-else>**** **** **** ******</text>
				</view>
			</view>
			<view class=" t-28 lh-40 fw-600 text-white pr-30" v-if="currentId">
				<image src="/static/check1-on.png" mode="aspectFill" class="size-68 block" v-if="currentId==item.id">
				</image>
				<image src="/static/check1.png" mode="aspectFill" class="size-68 block" v-else></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item', 'currentId'],
		data() {
			return {
				eye: false,
			};
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped></style>