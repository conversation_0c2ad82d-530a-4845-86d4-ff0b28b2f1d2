<template>
	<view class="m-32 !mt-0 f-pr">
		<image src="/static/team/boxbg.png" mode="widthFix" class="block w-686 h-341"></image>
		<view class="f-pa inset px-14 pt-24">
			<view class="fc pl-10 mb-6">
				<view class="c2 t-32 lh-45 fw-600">
					{{item.nickname}}
				</view>
				<view class="f-pr ml-12">
					<image src="/static/team/label.png" mode="aspectFit" class="block w-117 h-41">
					</image>
					<view class="f-pa inset fcc t-22 lh-30 color-#8B5D13">
						{{item.level_name}}
					</view>
				</view>
			</view>
			<view class="px-10 t-24 lh-33 color-#9EA59A mb-24">
				联系方式 {{item.phone}}
			</view>
			<view class="h-140 bg-#E8EFFD r-18 fc pl-25">
				<view class="flex1">
					<view class="color t-22 lh-30 mb-9">
						绩效收益
					</view>
					<view class="color t-32 lh-37 DIN">
						{{item.income}}
					</view>
				</view>
				<view class="flex1">
					<view class="color t-22 lh-30 mb-9">
						作业成员
					</view>
					<view class="color t-32 lh-37 DIN">
						{{item.works}}
					</view>
				</view>
				<view class="flex1">
					<view class="color t-22 lh-30 mb-9">
						投资金额
					</view>
					<view class="color t-32 lh-37 DIN">
						{{item.is_work}}
					</view>
				</view>
				<view class="flex1">
					<view class="color t-22 lh-30 mb-9">
						签到天数
					</view>
					<view class="color t-32 lh-37 DIN">
						{{item.signin}}
					</view>
				</view>
			</view>
			<view class="fc-bet t-24 lh-33 color-#9EA59A h-69">
				<view class="w-258">
					<text>团队人数</text>
					<text class="DIN ml-14 t-32 lh-37 c2">{{item.home}}</text>
				</view>
				<view class="flex1">
					<text>直聘人数</text>
					<text class="DIN ml-14 t-32 lh-37 c2">{{item.v1}}</text>
				</view>
				<view v-show="isV">
					<uv-icon name="arrow-right" size="26rpx" color="#333"></uv-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item', 'isV']
	}
</script>