<template>
	<view>
		<view class="scroll-view page-1">
			<view class="flex-scroll scroll-view bg">
				<view class="flex-scroll-fixed bg-fff">
					<uv-navbar title="编辑收货地址" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back-g.png" mode="aspectFit" size-48></image>
							</view>
						</template>

					</uv-navbar>
				</view>
				<view class="flex-scroll-container f-pr">
					<scroll-view scroll-y="true" class="scroll-view ">
						<view class="address-input-row px-32 py-20 flex">
							<view class="fc left">
								收货人
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="请填写收货人姓名" :disabled="true" v-model="userInfo.sfz_name">
							</view>
						</view>

						<view class="address-input-row px-32 py-20 flex">
							<view class="fc left">
								手机号码
							</view>
							<view class="right flex1 fc">
								<input type="tel" placeholder="请填写收货人手机号" :disabled="true" v-model="userInfo.phone2">
							</view>
						</view>
						<view class="address-input-row px-32 py-20 flex" @click="$refs.showCitySelect.open()">
							<view class="fc left">
								所在地区
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="选择地区" v-model="addressForm.address">
							</view>
							<view class="fc">
								<uv-icon name="arrow-right" size="24rpx " color="#999"></uv-icon>
							</view>
						</view>
						<view class="address-input-row px-32 py-20 flex">
							<view class="fc left">
								详细地址
							</view>
							<view class="right flex1 fc">
								<input type="text" placeholder="如某某区、街道、小区、门牌号" v-model="addressForm.detailed">
							</view>
						</view>
					</scroll-view>
				</view>


				<view class="bg-fff py-10 px-40">
					<view class="btn-full  fcc" :class="{'op-20':!submitBtnShow}" @click="editAdress">
						保存
					</view>
					<view class="btn-area"></view>
				</view>

			</view>
		</view>



		<!-- 省市区 -->
		<uv-popup ref="showCitySelect" mode="bottom" round="30rpx">
			<t-city @close="popClose"></t-city>
		</uv-popup>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import * as UserApi from '@/api/user.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				addressForm: {
					address: "",
					detailed: '',
				},
			};
		},
		computed: {
			...mapState(['userInfo', 'addressInfo']),
			submitBtnShow() {
				return this.userInfo.sfz_name && this.userInfo.phone && this.addressForm.address && this.addressForm
					.detailed
			}
		},
		watch: {
			userInfo() {
				const addr = this.addressInfo.address_place.split('~')
				this.addressForm.address = addr[0] || ''
				this.addressForm.detailed = addr[1] || ''
			}
		},
		methods: {
			popClose(e) {
				this.$refs.showCitySelect.close()
				this.addressForm.address = e
			},
			editAdress() {
				if (this.submitBtnShow) {
					uni.showLoading()
					UserApi.editAdress({
						address: `${this.addressForm.address}~${this.addressForm.detailed}`
					}).then((res) => {
						if (res.code == 0) {
							setTimeout(() => {
								uni.showToast({
									title: '成功添加收货地址'
								})
								this.$store.dispatch('getAddressInfo')
								uni.navigateTo({
									url: '/pages/setting/person'
								})
							})
						}
					}).
					finally(() => {
						uni.hideLoading()
					})
				}
			}
		},
		onReady() {
			this.$nextTick(() => {
				const addr = this.addressInfo.address_place.split('~')
				this.addressForm.address = addr[0] || ''
				this.addressForm.detailed = addr[1] || ''
			})
		}
	}
</script>

<style lang="scss" scoped>
	.bg {
		background: #F6F9FC;
	}


	.address-input-row {
		background: #fff;
		position: relative;

		.left {
			min-width: 168rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #222222;
			line-height: 45rpx;
		}

		input {
			font-weight: 400;
			font-size: 30rpx;
			color: #222;
			line-height: 80rpx;
			height: 80rpx;
			display: block;
			width: 95%;
		}

		&:after {
			content: "";
			display: block;
			left: 30rpx;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #E6E7EB;
			position: absolute;
		}
	}



	.address-input-row:nth-last-of-type(1) {
		&:after {
			display: none;
		}
	}
</style>