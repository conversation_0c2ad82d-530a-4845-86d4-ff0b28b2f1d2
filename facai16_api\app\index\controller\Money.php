<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\MoneyService;
use think\facade\Request;
use think\response\Json;

/**
 * 资金
 */
class Money
{
    /**
     * 账变记录
     * @return Json
     */
    public function getMoneyLog(): Json
    {
        $params = Request::only([
            'class_id'      => 0,
            'start_time'    => '',
        ]);

        $MoneyService = new MoneyService();
        $data         = $MoneyService->getMoneyLog($params);

        return Ajax::message($data['code'], $data['msg'], $data['data']);

    }

    /**
     * 复投转账
     * @return Json
     */
    public function transfer(): Json
    {
        $params = Request::only([
            'money'       => 0,
            'payPassword' => ''
        ]);

        $MoneyService = new MoneyService();
        $data         = $MoneyService->transfer($params);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }
}