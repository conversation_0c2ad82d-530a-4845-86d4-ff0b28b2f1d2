<?php
namespace app\common\utils;
use app\common\define\Defaults;

/**
 * 数字处理
 * Class FormatNumber
 * @package app\common\utils
 */
class Numeral
{

    /**
     * 向下取N位小数
     * @param  $num
     * @param int $dec
     * @return float
     */
   public static function decimalPlacesDownwards($num, int $dec = 2): float
   {
       $factor    = pow(10, $dec);
       $truncated = floor($num * $factor) / $factor;

       // Use round() with PHP's default rounding mode to handle floating point precision
       return round($truncated, $dec);

//        switch ($dec)
//        {
//            case 3:
//                $i = 1000;
//                break;
//            case 4:
//                $i = 10000;
//                break;
//            default:
//                $i = 100;
//                break;
//        }
//
//        return number_format(floor($num * $i) / $i, $dec,'.', '');
   }


   /*
   * 正负数相互转换（支持小数）
   */
    public static function positiveToNegative($number = 0)
    {
        return $number > 0 ? -1 * $number : abs($number);
    }


    /**
     * 金额处理
     * @param  mixed  $data  金额
     * @return mixed
     */
    public static function smallNumber($data)
    {
        return sprintf('%.2f', $data);
    }


    /**
    * 金额保留2位小数截断
    * @param string $amount
    *
    * @return string
    */
    public static function twoDigitNumber($amount): string
    {
        return sprintf("%1\$.2f", substr(sprintf("%.4f", $amount), 0, -2));
    }


    /**
     * 对0的处理
     */
    public static function zeroFormat($number)
    {
        if ($number <= 0)
        {
            return 1;
        }
        else
        {
            return $number;
        }
    }

    /**
     * 场馆费率的处理
     */
    public static function venueFee($profit, $iaRate)
    {
        if($profit < 0)
        {
            return self::fourDigitNumber(  abs($profit) * ($iaRate / 100));
        }
        else
        {
            return  0;
        }
    }

    /**
     * 四位数取整
     */
    public static function fourDigitNumber($number)
    {
        return floor($number * 10000) / 10000;
    }


}


