<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * ThsFastPay
 */
class ThsFastPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'ThsFastPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {

        $params  = [
            'mchId'         => $this->config['mchId'],
            'wayCode'       => $data['channel'],
            'subject'       => $data['orderNo'],
            'amount'        => $data['money'] * 100,
            'outTradeNo'    => $data['orderNo'],
            'clientIp'      => Request::ip(),
            'notifyUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),
            'reqTime'       => time() . '000',
        ];

        $params['sign']   = $this->signature($params);


        $result          = curl_post($this->config['url'], $params);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $uri          = '';

        $result       = json_decode($result,true);


        if ($result['code'] == 0)
        {
            $uri = $result['data']['payUrl'] ?? '';
        }

        return $uri;



    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"amount":"50000","mchId":"M202504051945289365","tradeNo":"P17438638609624869073","subject":"PAYVBHEDIGDIGAHBJF","notifyTime":"1743864771554","outTradeNo":"PAYVBHEDIGDIGAHBJF","sign":"fc59d0e50cbf4818e232eb8b185270af","state":"1","originTradeNo":"75040522374032201"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];

        $param     = Arrays::withOut($param,['sign']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }


        $orderId  = $param['outTradeNo'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {


        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];;
        $sign           = md5($stringSignTemp);

        return ($sign);
    }

}