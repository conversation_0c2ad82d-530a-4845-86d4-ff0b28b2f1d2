<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\index\service\RaffleService;
use think\response\Json;


/**
 * 抽奖
 */
class Raffle
{
    /**
     * 抽奖列表
     * @return Json
     */
    public function lists(): J<PERSON>
    {
        $RaffleService = new RaffleService();
        $result        = $RaffleService->lists();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 抽大奖列表
     * @return Json
     */
    public function prizeLists(): J<PERSON>
    {
        $RaffleService = new RaffleService();
        $result        = $RaffleService->prizeLists();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 抽奖
     * @return Json
     */
    public function draw(): <PERSON><PERSON>
    {
        $UserRepo      = new UserRepository();
        $id            = $UserRepo->userByHeader('id');

        $RedisLock     = new RedisLock();
        $status        = $RedisLock->lock('itemBuy:' . $id);



        try {

            if (empty($status))
            {
                return Ajax::fail('请不要重复提交');
            }

            $RaffleService = new RaffleService();

            $result        = $RaffleService->draw();

            $RedisLock->unLock('draw:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $e)
        {

            $RedisLock->unLock('draw:' . $id);

            return Ajax::fail(Record::exception('http', $e));
        }

    }

    /**
     * 抽大奖
     * @return Json
     */
    public function grandPrize(): Json
    {
        $UserRepo      = new UserRepository();
        $id            = $UserRepo->userByHeader('id');

        $RedisLock     = new RedisLock();
        $status        = $RedisLock->lock('grandPrize:' . $id);


        try {

            if (empty($status))
            {
                return Ajax::fail('请不要重复提交');
            }

            $RaffleService = new RaffleService();

            $result        = $RaffleService->grandPrize();

            $RedisLock->unLock('grandPrize:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $e)
        {

            $RedisLock->unLock('grandPrize:' . $id);

            return Ajax::fail(Record::exception('http', $e));
        }

    }

    /**
     * 大奖记录
     * @return Json
     */
    public function prizeRecord(): Json
    {
        $RaffleService = new RaffleService();
        $result        = $RaffleService->prizeRecord();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 记录
     * @return Json
     */
    public function record(): Json
    {
        $RaffleService = new RaffleService();
        $result        = $RaffleService->record();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}