<template>
	<view>
		<view class="page-bg">

			<view class="h-408">
				<uv-navbar title="" @leftClick="back" fixed placeholder bgColor="none" :titleStyle="{color:'#fff'}">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48></image>
						</view>
					</template>

				</uv-navbar>

			</view>

			<view class="fcc">
				<view class="w-595 f-pr">
					<box-box :blue="true">
						<view class="pt-26 fcc-h">
							<view class="fw-600 t-50 lh-74 color-#fff">
								{{userInfo.invite}}
							</view>
							<view class="px-40 r-100 h-53 bg-#407CEE t-24 color-#fff fcc mb-32"
								@click="toCopy(userInfo.invite)">
								复制邀请码
							</view>
							<view class="size-427 mb-32">
								<uv-qrcode ref="qrcode" size="427rpx"
									:value="`${url}?invite=${userInfo.invite}`"></uv-qrcode>
							</view>
						</view>
						<view class="px-30">
							<view class="btn-full fcc !r-20" @click="toCopy(`${url}?invite=${userInfo.invite}`)">
								复制下载地址
							</view>
						</view>
						<view class="h-32"></view>
					</box-box>

					<view class="fcc f-pa left-0 right-0 top-[-137rpx]">
						<image src="/static/invite/tit.png" mode="aspectFit" class="block w-596 h-162"></image>
					</view>
				</view>
			</view>
			<view class="h-60"></view>
			<view class="btn-area"></view>

		</view>


		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				url: "https://h5.uvui.cn"
			};
		},
		computed: {
			...mapState(['userInfo']),
		},
		methods: {
			toCopy(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					},

					fail: () => {
						uni.showToast({
							title: "复制失败"
						})
					}

				})
			}
		},

	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/invite/bg.jpg) no-repeat #081138;
		background-size: 100% auto;
		min-height: 100vh;
	}
</style>