<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\index\service\ItemService;
use think\facade\Request;
use think\response\Json;

/**
 * 产品
 * Class Item
 * @package app\index\controller
 */
class Item
{

    /**
     * 分类
     * @return Json
     */
    public function classes(): Json
    {
        $ItemService  = new ItemService();
        $result       = $ItemService->classes();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 产品分类
     * @return Json
     */
    public function lists(): Json
    {
        $params       = Request::only(['type']);
        $ItemService  = new ItemService();
        $result       = $ItemService->lists($params['type']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 拼团详情
     * @return Json
     */
    public function info(): Json
    {
        $params          = Request::only(['id']);
        $ItemService     = new ItemService();
        $result          = $ItemService->info($params['id']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);

    }


    /**
     * 购买项目
     * @return Json
     */
    public function pay(): Json
    {
        $params  = Request::only([
            'id'            => 0,
            'coupon'        => 0,
            'pin'           => '',
        ]);

        $UserRepo     = new UserRepository();

        $id           = $UserRepo->userByHeader('id');


        $RedisLock    = new RedisLock();
        $status       = $RedisLock->lock('itemBuy:' . $id);

        $ItemService  = new ItemService();


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在签约上一个订单');
            }

            $result  = $ItemService->pay($params['id'], $params['coupon'], $params['pin']);

            $RedisLock->unLock('itemBuy:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('itemBuy:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }

    }



    /**
     * 参与拼团
     * @return Json
     */
    public function record(): Json
    {
        $params          = Request::only([
            'tab'    => 1,
        ]);

        $ItemService     = new ItemService();
        //余额
        $result          = $ItemService->record($params['tab']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 签名
     * @return Json
     */
    public function signature():Json
    {
        $params          = Request::only([
            'id'            => 0,
            'signature'     => ''
        ]);

        $ItemService     = new ItemService();

        $result          = $ItemService->signature($params);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

}