<?php
namespace app\admin\service;

use app\common\repository\UserBankRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Result;

/**
 * 用户地址
 */
class UserBankService
{

    /**
     * 列表
     * @param $params
     * @return array
     */
    public function getUserBankLists($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }


        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['name']))
        {
            $where[] = ['name', '=', $params['name']];
        }


        $UserBankRepo    = new UserBankRepository();
        $data            = $UserBankRepo->paginates($where);

        return Result::success($data);
    }


    /**
     * 信息
     * @param $id
     * @return array
     */
    public function getUserBankInfo($id): array
    {
        $UserBankRepo   = new UserBankRepository();
        $data           = $UserBankRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加
     * @param $phone
     * @param $type
     * @param $default
     * @param $bankName
     * @param $bankBranch
     * @param $bankAccount
     * @param $coinName
     * @param $coinBlockchain
     * @param $coinAccount
     * @param $alipayAccount
     * @param $alipayImg
     * @param $wxImg
     * @return array
     */
    public function addUserBank($phone, $type, $default, $bankName, $bankBranch, $bankAccount, $coinName, $coinBlockchain, $coinAccount, $alipayAccount, $alipayImg, $wxImg): array
    {

        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findByCondition(['phone' => $phone]);

        if (!$user)
        {
            return Result::fail('没有用户信息');
        }

        $UserStateRepo = new UserStateRepository();
        $isTest        = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $update = [
            'username'          => $user['username'],
            'uid'               => $user['id'],
            'phone'             => $user['phone'],
            'is_test'           => $isTest,
            'type'              => $type,
            'default'           => $default,
            'name'              => $user['sfz_name'],
            'bank_name'         => $bankName,
            'bank_branch'       => $bankBranch,
            'bank_account'      => $bankAccount,
            'coin_name'         => $coinName,
            'coin_blockchain'   => $coinBlockchain,
            'coin_account'      => $coinAccount,
            'alipay_account'    => $alipayAccount,
            'alipay_img'        => $alipayImg,
            'wx_img'            => $wxImg,
            'create_time'       => time(),
            'update_time'       => time()
        ];


        $UserBankRepo   = new UserBankRepository();
        $res            = $UserBankRepo->inserts($update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新用户银行
     * @param $id
     * @param $phone
     * @param $type
     * @param $default
     * @param $name
     * @param $bankName
     * @param $bankBranch
     * @param $bankAccount
     * @param $coinName
     * @param $coinBlockchain
     * @param $coinAccount
     * @param $alipayAccount
     * @param $alipayImg
     * @param $wxImg
     * @return array
     */
    public function updateUserBank($id, $phone, $type, $default, $name, $bankName, $bankBranch, $bankAccount,  $coinName, $coinBlockchain, $coinAccount, $alipayAccount, $alipayImg, $wxImg): array
    {

        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findByCondition(['phone' => $phone]);

        if (!$user)
        {
            return Result::fail('没有用户信息');
        }

        $UserStateRepo = new UserStateRepository();
        $isTest        = $UserStateRepo->valueByCondition(['uid' => $user['id']],'is_test');

        $update = [
            'username'          => $user['username'],
            'uid'               => $user['id'],
            'phone'             => $user['phone'],
            'is_test'           => $isTest,
            'type'              => $type,
            'default'           => $default,
            'name'              => $user['sfz_name'],
            'bank_name'         => $bankName,
            'bank_branch'       => $bankBranch,
            'bank_account'      => $bankAccount,
            'coin_name'         => $coinName,
            'coin_blockchain'   => $coinBlockchain,
            'coin_account'      => $coinAccount,
            'alipay_account'    => $alipayAccount,
            'alipay_img'        => $alipayImg,
            'wx_img'            => $wxImg,
            'update_time'       => time(),
        ];


        $UserBankRepo = new UserBankRepository();
        $res          = $UserBankRepo->updateById($id, $update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除银行
     * @param $id
     * @return array
     */
    public function deleteUserBank($id): array
    {
        $UserBankRepo = new UserBankRepository();
        $res          = $UserBankRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


}
