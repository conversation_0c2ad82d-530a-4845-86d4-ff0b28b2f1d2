<?php
namespace app\admin\service;


use app\common\repository\GoodsClassRepository;
use app\common\repository\PaymentAccountRepository;
use app\common\repository\PaymentChannelRepository;
use app\common\repository\PaymentClassRepository;
use app\common\repository\PaymentUpperRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 支付分类
 */
class PaymentChannelService
{

    /**
     *
     * @return array
     */
    public function getPaymentChannelLists(): array
    {
        $PaymentRepo = new PaymentChannelRepository();
        $data        = $PaymentRepo->paginates();

        foreach ($data['data'] as &$value)
        {
            $PaymentAccountRepo    = new PaymentAccountRepository();
            $info                  = $PaymentAccountRepo->findById((int)$value['accounts']);

            $value['account_name'] = $info['title'] ?? '';
        }

        return Result::success($data);
    }

    /**
     * @param $id
     * @return array
     */
    public function getPaymentChannelInfo($id): array
    {
        $PaymentRepo = new PaymentChannelRepository();
        $data        = $PaymentRepo->findById($id);

        return Result::success($data);
    }

    /**
     * @param $params
     * @return array
     */
    public function addPaymentChannel($params): array
    {


        $time                   = Request::time();
        $params['create_time']  = $time;
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $PaymentClassRepo       =  new PaymentClassRepository();
            $class                  =  $PaymentClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
            $params['class_status'] =  $class['status'] ?? 0;
            $params['class_img']    =  $class['img'] ?? '';
            $params['class_rate']   =  $class['rate'] ?? 1.00;

        }


        if (isset($params['upper_id']))
        {
            $PaymentUpperRepo       =  new PaymentUpperRepository();
            $upper                  =  $PaymentUpperRepo->findById($params['upper_id']);

            $params['upper']        =  $upper['code'] ?? '';
            $params['upper_status'] =  $upper['status'] ?? 0;
            $params['upper_img']    =  $upper['img'] ?? '';
        }


        if (isset($params['accounts']))
        {
            $PaymentAccountRepo        = new PaymentAccountRepository();
            $account                   = $PaymentAccountRepo->findById($params['accounts']);
            $params['accounts']        = $account['id'] ?? '';
            $params['accounts_name']   = $account['title'] ?? '';
        }


        is_null($params['accounts']) && $params['accounts'] = 0;


        $params     = Arrays::withOut($params,['id']);

        $PaymentRepo = new PaymentChannelRepository();
        $res         = $PaymentRepo->inserts($params);

        if (!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }

    /**
     * @param $params
     * @return array
     */
    public function updatePaymentChannel($params): array
    {

        $time                   = Request::time();
        $params['update_time']  = $time;

        if (isset($params['class_id']))
        {
            $PaymentClassRepo       =  new PaymentClassRepository();
            $class                  =  $PaymentClassRepo->findById($params['class_id']);
            $params['class_name']   =  $class['title'] ?? '';
            $params['class_status'] =  $class['status'] ?? 0;
            $params['class_img']    =  $class['img'] ?? '';
            $params['class_rate']   =  $class['rate'] ?? 1.00;

        }


        if (isset($params['upper_id']))
        {
            $PaymentUpperRepo       =  new PaymentUpperRepository();
            $upper                  =  $PaymentUpperRepo->findById($params['upper_id']);
            $params['upper']        =  $upper['code'] ?? '';
            $params['upper_name']   =  $upper['title'] ?? '';
            $params['upper_status'] =  $upper['status'] ?? 0;
            $params['upper_img']    =  $upper['img'] ?? '';

        }


        if (isset($params['accounts']))
        {
            $PaymentAccountRepo        = new PaymentAccountRepository();
            $account                   = $PaymentAccountRepo->findById($params['accounts']);
            $params['accounts']        = $account['id'] ?? '';
            $params['accounts_name']   = $account['title'] ?? '';
        }

        is_null($params['accounts']) && $params['accounts'] = 0;



        $PaymentRepo = new PaymentChannelRepository();

        $res         = $PaymentRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * @param $id
     * @return array
     */
    public function deletePaymentChannel($id): array
    {
        $PaymentRepo = new PaymentChannelRepository();

        $res         = $PaymentRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}