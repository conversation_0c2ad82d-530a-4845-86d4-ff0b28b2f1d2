<?php

namespace app\common\cache;

use app\common\define\Time;
use think\facade\Cache;

/**
 * 验证码
 * Class Verification
 * @package app\common\cache
 */
class Verification
{
    /**
     * 缓存key
     *
     * @param string $key
     * @return string
     */
    public static function key(string $key): string
    {
        return 'captcha:' . $key;
    }

    /**
     * 缓存设置
     *
     * @param string $key   字典key
     * @param string $data   数据
     * @param int $ttl     有效时间（秒）0永久
     *
     * @return bool
     */
    public static function set(string $key, string $data, int $ttl = Time::FIVE_MINUTE): bool
    {
        return Cache::set(self::key($key), $data, $ttl);
    }

    /**
     * 缓存获取
     * @param string $key 字典key
     * @return mixed
     */
    public static function get(string $key)
    {
        return Cache::get(self::key($key));
    }

    /**
     * 缓存删除
     *
     * @param string $key 字典key
     *
     * @return bool
     */
    public static function del(string $key): bool
    {
        return Cache::delete(self::key($key));
    }
}
