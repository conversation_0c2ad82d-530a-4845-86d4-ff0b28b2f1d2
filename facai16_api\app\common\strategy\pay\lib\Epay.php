<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * Epay
 */
class Epay implements PayInterface
{
    //支付类型
    const CHANNEL = 'Epay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     * @throws \Exception
     */
    public function send($data)
    {
        //发起支付
        $createOrder = function ($params)
        {
            return $this->execute('api/pay/create', $params);
        };

        $params  = [
            'method'       => 'jump',
            'type'         => $data['channel'],
            'out_trade_no' => $data['orderNo'],
            'notify_url'   => CompleteRequest('/api/callback/' . self::CHANNEL),
            'return_url'   => CompleteRequest('/api/callback/' . self::CHANNEL),
            'name'         => $data['orderNo'],
            'money'        => round($data['money'], 2),
            'clientip'     => Request::ip(),
        ];



        $uri  = '';

        try {

            $result  = $createOrder($params);

            Log::channel('pay')->info(self::CHANNEL . '[send]' . json_encode($result));

            if($result['code'] == 0)
            {
                $uri = $result['pay_info'] ?? '';
            }

        }catch (\Exception $e)
        {
            Log::channel('pay')->info(self::CHANNEL . '[send]' . $e->getMessage() .'@@'. $e->getLine());
        }

        return $uri;
    }



    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
//        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"status":"2","code":"drenjie-zfb","orderno":"17402125841410","amount":"500.00","sign":"d24e591f6c68a22a5b0a3c4a68c10965"}';
//        $param = json_decode($param,true);

//        $sign      = $param['sign'];
//
//        $param     = Arrays::withOut($param,['sign']);
//
//        $signature = $this->signature($param);
//
//        if ($signature != $sign)
//        {
//            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
//            return 'FAIL';
//        }

        $orderId  = $param['out_trade_no'] ?? '';
        $status   = $param['trade_status'] ?? '';

        if($status != 'TRADE_SUCCESS')
        {
            return 'FAIL';
        }

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'SUCCESS';
        }
        else
        {
            return 'FAIL';
        }
    }

    /**
     * 发起API请求
     * @throws \Exception
     */
    public function execute($path, $params)
    {

        $path       = ltrim($path, '/');
        $reqUrl     = $this->config['url'] . $path;
        $param      = $this->buildRequestParam($params);

        $response   = $this->getHttpResponse($reqUrl, http_build_query($param));

        $arr        = json_decode($response, true);

        if ($arr && $arr['code'] == 0)
        {
            if (!$this->verify($arr))
            {
                throw new \Exception('返回数据验签失败');
            }

            return $arr;

        } else
        {
            throw new \Exception($arr ? $arr['msg'] : '请求失败');
        }

    }

    // 回调验证
    public function verify($arr)
    {
        if (empty($arr) || empty($arr['sign']))
        {
            return false;
        }

        if (empty($arr['timestamp']) || abs(time() - $arr['timestamp']) > 300)
        {
            return false;
        }

        $sign = $arr['sign'];

        return $this->rsaPublicVerify($this->getSignContent($arr), $sign);
    }

    /**
     * 查询订单支付状态
     * @param $trade_no
     * @return bool
     * @throws \Exception
     */
    public function orderStatus($trade_no): bool
    {
        $params = [
            'trade_no' => $trade_no,
        ];

        $result = $this->execute('api/pay/query', $params);

        if ($result && $result['status'] == 1)
        {
            return true;
        } else
        {
            return false;
        }
    }


    private function buildRequestParam($params)
    {
        $params['pid']          = $this->config['pid'];
        $params['timestamp']    = time() . '';
        $mySign                 = $this->rsaPrivateSign($this->getSignContent($params));;
        $params['sign']         = $mySign;
        $params['sign_type']    = 'RSA';
        return $params;
    }


    /**
     * 获取待签名字符串
     * @param $params
     * @return false|string
     */
    private function getSignContent($params)
    {
        $isEmpty = function ($value)
        {
            return $value === null || trim($value) === '';
        };

        ksort($params);

        $signStr = '';

        foreach ($params as $k => $v)
        {
            if (is_array($v) || $isEmpty($v) || $k == 'sign' || $k == 'sign_type')
            {
                continue;
            }

            $signStr .= '&' . $k . '=' . $v;
        }

        return substr($signStr, 1);
    }


    /**
     * 商户私钥签名
     * @param $data
     * @return string
     * @throws \Exception
     */
    private function rsaPrivateSign($data): string
    {

        $key = "-----BEGIN PRIVATE KEY-----\n" .
            wordwrap($this->config['merchant_private_key'], 64, "\n", true) .
            "\n-----END PRIVATE KEY-----";

        $privateKey = openssl_get_privatekey($key);

        if (!$privateKey)
        {
            throw new \Exception('签名失败，商户私钥错误');
        }

        openssl_sign($data, $sign, $privateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($sign);
    }

    /**
     * 平台公钥验签
     * @param $data
     * @param $sign
     * @return string
     * @throws \Exception
     */
    private function rsaPublicVerify($data, $sign): string
    {

        $key = "-----BEGIN PUBLIC KEY-----\n" .
            wordwrap($this->config['platform_public_key'], 64, "\n", true) .
            "\n-----END PUBLIC KEY-----";

        $publicKey = openssl_get_publickey($key);

        if (!$publicKey)
        {
            throw new \Exception("验签失败，平台公钥错误");
        }

        $result = openssl_verify($data, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);

        return $result === 1;
    }


    /**
     * 请求外部资源
     * @param $url
     * @param bool $post
     * @return bool|string
     */
    private function getHttpResponse($url, $post)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $header[] = "Accept: */*";
        $header[] = "Accept-Language: zh-CN,zh;q=0.8";
        $header[] = "Connection: close";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        if ($post)
        {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        }
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
//        ksort($params);
//
//        $post_url = '';
//
//        foreach ($params as $key=>$value)
//        {
//            $post_url .= $key . '=' . $value.'&';
//        }
//
//        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];
//
//        $sign           = md5($stringSignTemp);

        return '';
    }
}