<?php
namespace app\admin\controller;

use app\admin\service\MessageService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 站内信
 */
class Message
{

    /**
     * 站内信列表
     * @return mixed
     */
    public function getMessageLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'phone',
            'username',
            'is_read',
            'limit' => 10,
        ]);

        $MessageService = new MessageService();
        $data           = $MessageService->getMessageLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 站内信信息
     * @return Json
     */
    public function getMessageInfo(): Json
    {
        $id             = Request::param('id',0);
        $MessageService = new MessageService();
        $data           = $MessageService->getMessageInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加站内信
     * @return Json
     */
    public function addMessage(): Json
    {
        $params         = Request::only([
            'title',
            'content',
            'users',
        ]);

        $MessageService = new MessageService();
        $data           = $MessageService->addMessage($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新站内信
     * @return Json
     */
    public function updateMessage(): Json
    {
        $param          = Request::param();
        $MessageService = new MessageService();
        $data           = $MessageService->updateMessage($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除站内信
     * @return Json
     */
    public function deleteMessage(): Json
    {
        $id             = Request::param('id',0);
        $MessageService = new MessageService();
        $data           = $MessageService->deleteMessage($id);
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
    
}