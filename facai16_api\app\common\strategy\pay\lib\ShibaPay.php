<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * ShibaPay
 */
class ShibaPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'ShibaPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {

        $param = [
            'mchId'         => $this->config['mch_id'],
            'appId'         => $this->config['app_id'],
            'productId'     => $data['channel'],
            'mchOrderNo'    => $data['orderNo'],
            'currency'      => 'cny',
            'amount'        => $data['money'] * 100,
            'subject'       => 'pay',
            'body'          => 'pay',
            'extra'         => 'pay',
            'notifyUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),

        ];

        $param['sign']  = $this->signature($param);

        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);

        return $result['payUrl'] ?? '';
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"income":"45250","payOrderId":"P0110202505042114533830004","amount":"50000","mchId":"20000308","productId":"8185","mchOrderNo":"17463644928543","paySuccTime":"1746364541000","sign":"2AEAF299BC969EEBB42BC89C28AA98DC","channelOrderNo":"","backType":"2","param1":"","param2":"","appId":"ce8c5fefc2374bc58d631a41601c471c","status":"2"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];


        $param     = Arrays::withOut($param,['sign','channelOrderNo','param1','param2',]);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['mchOrderNo'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return strtoupper($sign);
    }

}