<?php
namespace app\index\service;


use app\common\repository\MessageRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;

/**
 * 站内信
 */
class MessageService
{
    /**
     * 站内信列表
     * @return array
     */
    public function lists(): array
    {
        $UserRepo         = new UserRepository();
        $user             = $UserRepo->userByHeader();
        $where            = [];
        $where[]          = ['uid', '=', $user['id']];

        $MessageRepo = new MessageRepository();
        $data        = $MessageRepo->paginates($where);

        return  Result::success($data);
    }

    /**
     * 内容
     * @param int $id
     * @return array
     */
    public function content(int $id) : array
    {
        $UserRepo         = new UserRepository();
        $user             = $UserRepo->userByHeader();

        $MessageRepo = new MessageRepository();


        $content    = $MessageRepo->findById($id);


        if ($content['is_read'] == 0)
        {
            $update = [
                'is_read'       => 1,
                'update_time'   => time(),
            ];

            $where       = [];
            $where[]     = ['uid','=', $user['id']];
            $where[]     = ['id','=', $id];
            $res         = $MessageRepo->updateByCondition($where,$update);
        }


        return  Result::success($content);
    }
}