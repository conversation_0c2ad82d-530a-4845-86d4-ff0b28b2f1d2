<?php
namespace app\common\strategy\sms;
use app\common\strategy\sms\lib\AliYun;
use app\common\strategy\sms\lib\JiuDingGe;
use app\common\strategy\sms\lib\JuHe;
use app\common\strategy\sms\lib\SmsBao;
use think\facade\Config;

/**
 * 短信发送服务
 * Class SmsService
 * @package app\common\strategy\sms
 */
class SmsService
{
    public function send($phone, $code)
    {
        $config    = Config::get('serve_sms.default');

        switch ($config)
        {
            case 'SmsBao':
                $strategy = new SmsBao();
                break;
            case 'JuHe':
                $strategy = new JuHe();
                break;
            case 'AliYun':
                $strategy = new AliYun();
                break;
            case 'JiuDingGe':
            default:
                $strategy = new JiuDingGe();
                break;
        }


        $SmsContext = new SmsContext($strategy);

        return $SmsContext->sendMessage($phone, $code);
    }

}