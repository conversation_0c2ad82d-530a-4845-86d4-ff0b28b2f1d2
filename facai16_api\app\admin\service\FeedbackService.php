<?php
namespace app\admin\service;

use app\common\repository\FeedbackRepository;
use app\common\utils\Result;

/**
 * 反馈
 */
class FeedbackService
{
    /**
     * 反馈列表
     * @return array
     */
    public function getFeedbackLists(): array
    {
        $FeedbackRepo = new FeedbackRepository();
        $data         = $FeedbackRepo->paginates();

        return Result::success($data);
    }

    /**
     * 反馈信息
     * @param $id
     * @return array
     */
    public function getFeedbackInfo($id): array
    {
        $FeedbackRepo = new FeedbackRepository();
        $data      = $FeedbackRepo->findById($id);

        return Result::success($data);
    }


    /**
     * 删除反馈
     * @param $id
     * @return array
     */
    public function deleteFeedback($id): array
    {
        $FeedbackRepo = new FeedbackRepository();

        $res       = $FeedbackRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

}
