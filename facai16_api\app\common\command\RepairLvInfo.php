<?php

namespace app\common\command;

use app\common\repository\UserRepository;
use think\console\Command;
use think\console\Input;
use think\console\Output;


/**
 * 测试
 * Class Backup
 * @package app\common\command
 */
class RepairLvInfo extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('RepairLvInfo')->setDescription('the RepairLvInfo command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function execute(Input $input, Output $output)
    {
        $UserRepo = new UserRepository();
        $data     = $UserRepo->selectByCondition();

        foreach ($data as $datum)
        {
            $v1 = $datum['v1_id'];

            $v2 = $datum['v2_id'];

            $v3 = $datum['v3_id'];

            if($v1 != 0)
            {
                $user   = $UserRepo->findById($v1);

                $update = [
                    'v1_name'       => $user['phone'],
                    'update_time'   => time(),
                ];

                $UserRepo->updateById($datum['id'], $update);
            }

            if($v2 != 0)
            {
                $user   = $UserRepo->findById($v2);

                $update = [
                    'v2_name'       => $user['phone'],
                    'update_time'   => time(),
                ];

                $UserRepo->updateById($datum['id'], $update);
            }

            if($v3 != 0)
            {
                $user   = $UserRepo->findById($v3);

                $update = [
                    'v3_name'       => $user['phone'],
                    'update_time'   => time(),
                ];

                $UserRepo->updateById($datum['id'], $update);
            }

        }
    }

}

