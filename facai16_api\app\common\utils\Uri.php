<?php
namespace app\common\utils;

use think\facade\Request;
use think\helper\Str;

class Uri
{
    /**
     * 网址
     * @param string $url
     * @return string
     */
    public static function  file(string $url = ''): string
    {
        if (empty($url))
        {
            return '';
        }

        if (Str::contains($url,'http://') || Str::contains($url,'https://'))
        {
            return $url;
        }

        if (Request::port() == 80 || Request::port() == 443)
        {
            return  Request::domain(true) . $url;
        }
        else
        {
            return Request::domain(true)  . ':' . Request::port() . $url;
        }
    }

}