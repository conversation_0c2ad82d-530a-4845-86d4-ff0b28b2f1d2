<?php

namespace app\common\utils;

use app\common\define\Defaults;
use app\common\define\Status;
use think\response\Json;

/**
 * ajax返回
 * Class Ajax
 * @package app\common\utils
 */
class Ajax
{

    /**
     * 失败
     */
    const FAILED  = 1;

    /**
     * 成功
     */
    const SUCCESS = 0;

    /**
     * 成功返回
     * [已调试]
     * @param string $msg 成功提示
     * @param array $data 成功数据
     * @return Json
     */
   public static function success(array $data = [], string $msg = ''): Json
   {

        $res['code'] = self::SUCCESS;
        $res['msg']  = $msg ?: lang('ajax_success');
        $res['data'] = is_array($data) ? $data : (object)null;

       return json($res,200,[],['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
    }


    /**
     * 错误返回
     * [已调试]
     * @param string $msg
     * @param null|array $data 成功数据
     * @return Json
     */
    public static function fail( string $msg = '', array $data = []): Json
    {
        $res['code'] = self::FAILED;
        $res['msg']  = $msg;
        $res['data'] = is_array($data) ? $data : (object)null;

        return json($res,200,[],['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
    }


    /**
     * 错误返回
     * [已调试]
     * @param int $code
     * @param null|array $data 成功数据
     * @return Json
     */
   public static function error(int $code = self::FAILED, array $data = []): Json
   {

        $res['code'] = $code;
        $res['msg']  = lang('error.' . $code);
        $res['data'] = is_array($data) ? $data : (object)null;

       return json($res,200,[],['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
   }




    public static  function message($code=0, $msg='success', $data=array()): Json
    {
        $data = [
            'code'  => $code,
            'msg'   => $msg,
            'data'  => $data
        ];

        return json($data,200,[],['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
    }

    /**
     *
     * @param $url
     * @param $href
     * @param $alt
     * @return Json
     */
    public static function errorNo($url,$href,$alt): Json
    {
        $data = [
            'errno'   => 0,
            'data'      => [
                'url'      => $url,
                'href'     => $href,
                'alt'      => $alt
            ],
        ];

        return json($data,200,[],['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES]);
    }
}