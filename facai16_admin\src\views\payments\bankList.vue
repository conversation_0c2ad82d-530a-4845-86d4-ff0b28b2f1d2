<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    :hideSeachButton="true"
    @search="getList"
  >
    <!-- <template v-slot:form-inline-items>
        <el-form-item>
          <el-input
            v-model="searchForm.title"
            placeholder="请输入用户名"
            clearable
          />
        </el-form-item>
      </template> -->
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editUser('add', {})"
        v-permission
        >新增</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="银行名称" width="240" />
      <!-- <el-table-column prop="img" label="银行logo" width="220">
        <template #default="scope">
          <img
            :src="proxy.IMG_BASE_URL + scope.row.img"
            height="auto"
            width="100px"
            alt=""
          />
        </template>
      </el-table-column> -->
      <el-table-column prop="create_at" label="添加时间" width="220" />
      <el-table-column prop="update_at" label="更新时间" width="220" />
      <el-table-column
        fixed="right"
        width="200"
        label="操作"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editUser('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="编辑/新增" width="1100">
      <editPop
        :key="editRow"
        ref="editFormRef"
        :formType="formType"
        :item="editRow"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  fundingTypesEnum,
  clocksEnums,
  getLabelByVal,
  roleEnums,
} from "@/config/enums";
import editPop from "./components/bankList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  username: "",
});

onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Bank/getBankList",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};

const editFormRef = ref(null);
const dialogFlag = ref(false);
const editRow = ref({});
const formType = ref("add");
const adminTableRef = ref(null);

const editUser = (type, row) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url = formType.value == "add" ? "Bank/addBank" : "Bank/updateBank";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      // img: data.img,
      title: data.title,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "bank/deleteBank?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};
</script>

<style lang="less" scoped></style>
