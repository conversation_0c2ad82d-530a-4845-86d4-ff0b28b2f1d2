<?php
namespace app\admin\service;

use app\common\repository\PaymentAccountRepository;
use app\common\repository\PaymentChannelRepository;
use app\common\repository\PaymentClassRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 支付分类
 */
class PaymentClassService
{

    /**
     * 获取支付类型
     * @return array
     */
    public function getPaymentClassLists(): array
    {
        $PaymentRepo = new PaymentClassRepository();
        $data        = $PaymentRepo->paginates([],'*',10,['id'=> 'desc']);
        return Result::success($data);
    }

    /**
     * 或者支付类型信息
     * @param $id
     * @return array
     */
    public function getPaymentClassInfo($id): array
    {
        $PaymentRepo = new PaymentClassRepository();
        $data        = $PaymentRepo->findById($id);
        return Result::success($data);
    }

    /**
     * 添加支付类型
     * @param $params
     * @return array
     */
    public function addPaymentClass($params): array
    {
        $PaymentRepo = new PaymentClassRepository();
        $res         = $PaymentRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新支付类型
     * @param $params
     * @return array
     */
    public function updatePaymentClass($params): array
    {
        $PaymentRepo = new PaymentClassRepository();
        $res         = $PaymentRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        $update = [
            'class_rate' => $params['rate'],
            'update_time' => time(),
        ];

        $where              = [];
        $where[]            = ['class_id', '=',$params['id']];
        $PaymentChannelRepo = new PaymentChannelRepository();
        $PaymentChannelRepo->updateByCondition($where, $update);


        $update = [
            'rate'        => $params['rate'],
            'update_time' => time(),
        ];
        $where   = [];
        $where[] = ['type', '=',1];
        $PaymentAccountRepo = new PaymentAccountRepository();
        $PaymentAccountRepo->updateByCondition($where, $update);


        return Result::success();
    }


    /**
     * 删除支付类型
     * @param $id
     * @return array
     */
    public function deletePaymentClass($id): array
    {
        $PaymentRepo = new PaymentClassRepository();

        $res         = $PaymentRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}