<?php
namespace app\index\service;

use app\common\model\MoneyClass;
use app\common\repository\CouponRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\RaffleBigLogRepository;
use app\common\repository\RaffleBigRepository;
use app\common\repository\RaffleLogRepository;
use app\common\repository\RaffleRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserWordsLogsRepository;
use app\common\repository\UserWordsRepository;
use app\common\utils\Draw;
use app\common\utils\Record;
use app\common\utils\Result;
use app\common\utils\Uri;
use app\index\service\traits\RaffleTrait;
use think\facade\Db;
use think\facade\Request;


/**
 * 抽奖服务
 */
class RaffleService
{
    use RaffleTrait;


    /**
     * 抽奖列表
     * @return array
     */
    public function lists(): array
    {
        $RaffleRepo = new RaffleRepository();
        $data       = $RaffleRepo->selectByCondition();

        foreach ($data as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return Result::success($data);
    }


    /**
     * 抽大奖列表
     * @return array
     */
    public function prizeLists(): array
    {
        $RaffleRepo = new RaffleBigRepository();
        $data       = $RaffleRepo->selectByCondition();

        foreach ($data as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return Result::success($data);
    }


    /**
     * 抽奖
     * @return array
     */
    public function draw(): array
    {
        $UserRepo           = new UserRepository();
        $RaffleLogRepo      = new RaffleLogRepository();
        $RaffleRepo         = new RaffleRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $UserInfoRepo       = new UserInfoRepository();
        $user               = $UserRepo->userByHeader();
        $userState          = $UserRepo->userStateByHeader();

        $today   = strtotime(date('Y-m-d 00:00:00'));

        $where   = [];
        $where[] = ['uid', '=', $user['id']];
        $where[] = ['create_time', '>=', $today];

        $count   = $RaffleLogRepo->countByCondition($where);

        if ($user['level'] > 0)
        {
            if ($count > 2)
            {
                return Result::fail('vip会员超过不能超过2次');
            }
        }
        else
        {
            if ($count > 0)
            {
                return Result::fail('已经抽奖过');
            }
        }

        $drawLists = $RaffleRepo->selectByCondition();

        //概率算法
        $list = array_map(function ($v) {
            return $v['chance'];
        }, $drawLists);

        $draw   = $drawLists[Draw::rand($list)];



        switch ($draw['type'])
        {
            default:
                $money  = $draw['money'];

                Db::startTrans();

                try {


                    $insert = [
                        'uid'           => $user['id'],
                        'username'      => $user['username'],
                        'phone'         => $user['phone'],
                        'is_test'       => $userState['is_test'],
                        'amount'        => $money,
                        'raffle_id'     => $draw['id'],
                        'raffle_name'   => $draw['title'],
                        'desc'          => $draw['title'],
                        'img'           => $draw['img'],
                        'create_time'   => Request::time(),
                        'update_time'   => Request::time()
                    ];


                    $fromId = $RaffleLogRepo->insertsGetId($insert);

                    //写入日志
                    $desc  = "幸运抽奖：{$draw['title'] }，获得{$money}";

                    $res   = $MoneyLogRepo->fund($user['id'], $money,MoneyClass::RAFFLE, $fromId,$desc);

                    if($res['code'])
                    {
                        Db::rollback();
                        return Result::fail($res['msg']);
                    }


                    $res = $UserInfoRepo->statistic($user['id'],['raffle_money' => $money]);

                    if(!$res)
                    {
                        Db::rollback();
                        return Result::fail('抽奖失败');
                    }

                    Db::commit();

                    return  Result::success(['id' => $draw['id'],'money' => $money,'words' => '','rate'=> 0], $desc);

                } catch (\Exception $e)
                {
                    // 回滚事务
                    Db::rollback();

                    Record::exception('index', $e,'RaffleService->draw');

                    return Result::fail('抽奖失败');
                }
                break;

            case 1:

                Db::startTrans();

                try {

                    $insert = [
                        'uid'           => $user['id'],
                        'username'      => $user['username'],
                        'phone'         => $user['phone'],
                        'is_test'       => $userState['is_test'],
                        'amount'        => 0,
                        'raffle_id'     => $draw['id'],
                        'raffle_name'   => $draw['title'],
                        'desc'          => $draw['title'] . $draw['words'],
                        'img'           => $draw['img'],
                        'create_time'   => Request::time(),
                        'update_time'   => Request::time()
                    ];

                    $res = $RaffleLogRepo->insertsGetId($insert);

                    if (!$res)
                    {
                        Db::rollback();
                        return Result::fail('抽奖失败');
                    }

                    $_list = [
                        '智'  => 'zhi',
                        '启'  => 'qi',
                        '未'  => 'wei',
                        '来'  => 'lai',
                        '创'  => 'chuang',
                        '领'  => 'ling',
                        '无'  => 'wu',
                        '限'  => 'xian',
                    ];

                    if (isset($_list[$draw['words']]))
                    {
                        $insert = [
                            'uid'       => $user['id'],
                            'username'  => $user['username'],
                            'phone'     => $user['phone'],
                            'is_test'   => $user['is_test'],
                            'words'     => $draw['words'],
                            'create_time' => time(),
                            'update_time' => time(),
                        ];

                        $UserWordsRepo  = new UserWordsLogsRepository();
                        $res            = $UserWordsRepo->inserts($insert);

                        if (!$res)
                        {
                            Db::rollback();
                            return Result::fail('抽奖失败');
                        }

                        $UserWordsRepo = new UserWordsRepository();
                        $_words        = $_list[$draw['words']];

                        $update = [
                            $_words       => Db::raw('`'.$_words.'` + 1'),
                            'update_time' => time(),
                        ];

                        $where   = [];
                        $where[] = ['uid', '=', $user['id']];
                        $res     = $UserWordsRepo->updateByCondition($where, $update);

                        if (!$res)
                        {
                            Db::rollback();
                            return Result::fail('抽奖失败');
                        }

                    }

                    Db::commit();
                    //写入日志
                    $desc  = "幸运抽奖：{$draw['title'] }，获得集字" . $draw['words'];

                    return  Result::success(['id' => $draw['id'],'money' => 0,'words' => $draw['words'],  'rate'=> 0], $desc);

                } catch (\Exception $e)
                {
                    // 回滚事务
                    Db::rollback();

                    Record::exception('index', $e,'RaffleService->draw');

                    return Result::fail('抽奖失败1');
                }
                break;

            case 2:

                Db::startTrans();

                try {

                    $insert = [
                        'uid'           => $user['id'],
                        'username'      => $user['username'],
                        'phone'         => $user['phone'],
                        'is_test'       => $user['is_test'],
                        'amount'        => 0,
                        'raffle_id'     => $draw['id'],
                        'raffle_name'   => $draw['title'],
                        'desc'          => $draw['title'] . $draw['rate'],
                        'img'           => $draw['img'],
                        'create_time'   => Request::time(),
                        'update_time'   => Request::time()
                    ];

                    $res = $RaffleLogRepo->insertsGetId($insert);

                    if (!$res)
                    {
                        Db::rollback();
                        return Result::fail('抽奖失败');
                    }


                    $insert = [
                        'uid'         => $user['id'],
                        'username'    => $user['username'],
                        'phone'       => $user['phone'],
                        'is_test'     => $user['is_test'],
                        'allow'       => 1000,
                        'amount'      => $draw['rate'],
                        'create_time' => time(),
                        'update_time' => time(),
                        'expire_time' => time() + 86400 * 30,
                    ];

                    $CouponRepo  = new CouponRepository();
                    $res         = $CouponRepo->inserts($insert);


                    if (!$res)
                    {
                        Db::rollback();
                        return Result::fail('抽奖失败');
                    }

                    Db::commit();
                    //写入日志
                    $desc  = "幸运抽奖：{$draw['title']}，获得加息券" . $draw['rate'];

                    return  Result::success(['id' => $draw['id'],'money' => 0,'words' => $draw['words'], 'rate'=> $draw['rate']], $desc);

                } catch (\Exception $e)
                {
                    // 回滚事务
                    Db::rollback();

                    Record::exception('index', $e,'RaffleService->draw');

                    return Result::fail('抽奖失败11');
                }
                break;
        }







    }

    /**
     * 记录
     * @return array
     */
    public function record(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $where         = [];
        $where[]       = ['uid','=',$user['id']];

        $RaffleLogRepo = new RaffleLogRepository();
        $data          = $RaffleLogRepo->paginates($where);

        foreach ($data['data'] as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return Result::success($data);
    }

    /**
     * 抽大奖
     * @return array
     */
    public function grandPrize(): array
    {
        $UserRepo           = new UserRepository();
        $RaffleLogRepo      = new RaffleBigLogRepository();
        $RaffleRepo         = new RaffleBigRepository();
        $MoneyLogRepo       = new MoneyLogRepository();
        $UserInfoRepo       = new UserInfoRepository();
        $user               = $UserRepo->userByHeader();
        $userState          = $UserRepo->userStateByHeader();
        $UserWordsRepo      = new UserWordsRepository();
        $words              = $UserWordsRepo->findByCondition(['uid' => $user['id']]);

        //抽奖次数不足
        if($words['zhi'] <= 0 || $words['qi'] <= 0 || $words['wei'] <= 0 || $words['lai'] <= 0 || $words['chuang'] <= 0 || $words['ling'] <= 0 || $words['wu'] <= 0 || $words['xian'] <= 0)
        {
            return Result::fail('抽奖次数不足');
        }


        $drawLists = $RaffleRepo->selectByCondition();

        //概率算法
        $list       = [];

        foreach($drawLists as $k=> $v)
        {
            $list[$k] = $v['chance'];
        }


        $draw   = $drawLists[Draw::rand($list)];

        $money  = $draw['money'];

        Db::startTrans();

        try {


            $insert = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $userState['is_test'],
                'amount'        => $money,
                'raffle_id'     => $draw['id'],
                'raffle_name'   => $draw['title'],
                'desc'          => $draw['title'],
                'img'           => $draw['img'],
                'create_time'   => Request::time(),
                'update_time'   => Request::time()
            ];

            $fromId = $RaffleLogRepo->insertsGetId($insert);

            //写入日志
            $desc  = "幸运抽奖：{$draw['title'] }，获得{$money}";

            $res   = $MoneyLogRepo->fund($user['id'], $money,MoneyClass::RAFFLE, $fromId,$desc);

            if($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $where   = [];
            $where[] = ['uid','=',$user['id']];


            $update = [
                'zhi'           => Db::raw('`zhi` - 1'),
                'qi'            => Db::raw('`qi` - 1'),
                'wei'           => Db::raw('`wei` - 1'),
                'lai'           => Db::raw('`lai` - 1'),
                'chuang'        => Db::raw('`chuang` - 1'),
                'ling'          => Db::raw('`ling` - 1'),
                'wu'            => Db::raw('`wu` - 1'),
                'xian'          => Db::raw('`xian` - 1'),
                'update_time'   => Request::time(),
            ];

            $res     = $UserWordsRepo->updateByCondition($where,$update);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $res = $UserInfoRepo->statistic($user['id'],['raffle_money' => $money]);

            if(!$res)
            {
                Db::rollback();
                return Result::fail('抽奖失败');
            }

            Db::commit();

            return  Result::success(['id' => $draw['id'],'money' => $money], $desc);

        } catch (\Exception $e)
        {
            // 回滚事务
            Db::rollback();

            Record::exception('index', $e,'RaffleService->draw');

            return Result::fail('抽奖失败');
        }
    }

    /**
     * 大奖记录
     * @return array
     */
    public function prizeRecord(): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $where         = [];
        $where[]       = ['uid','=',$user['id']];

        $RaffleLogRepo = new RaffleBigLogRepository();
        $data          = $RaffleLogRepo->paginates($where);

        foreach ($data['data'] as &$item)
        {
            $item['img'] = Uri::file($item['img']);
        }

        return Result::success($data);
    }

}