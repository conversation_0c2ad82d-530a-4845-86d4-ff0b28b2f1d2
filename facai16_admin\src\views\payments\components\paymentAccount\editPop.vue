<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="标题" required>
      <el-input v-model="form.title" />
    </el-form-item>

    <el-form-item label="充值方式" required>
      <el-select v-model="form.type" placeholder="充值方式" clearable>
        <el-option
          v-for="item in withdrawTypeEnums"
          :label="item.label"
          :key="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="汇率" required>
      <el-input v-model="form.rate" />
    </el-form-item>

    <template v-if="form.type == 0">
      <el-form-item label="姓名"  required>
              <el-input v-model="form.bank_owner"  clearable />
          </el-form-item>
      <el-form-item label="开户行" required>
        <el-input v-model="form.bank_name" clearable />
      </el-form-item>
      <el-form-item label="支行" required>
        <el-input v-model="form.bank_branch" clearable />
      </el-form-item>
      <el-form-item label="卡号" required>
        <el-input v-model="form.bank_account" clearable />
      </el-form-item>
    </template>
    <template v-if="form.type == 1">
      <el-form-item label="虚拟币名称" required>
        <el-input v-model="form.coin_name" disabled clearable />
      </el-form-item>
      <el-form-item label="钱包地址" required>
        <el-input v-model="form.coin_account" clearable />
      </el-form-item>
      <el-form-item label="区块链" required>
        <el-input v-model="form.coin_blockchain" clearable />
      </el-form-item>
      
    </template>
    <template v-if="form.type == 2">
      <el-form-item label="账号" required>
        <el-input v-model="form.alipay_account" clearable />
      </el-form-item>
      <br />
      <!-- <el-form-item label="收款码" required>
        <img :src="form.alipay_img" alt="" />
      </el-form-item> -->
    </template>
    <template v-if="form.type == 4">
      <el-form-item label="收款人" required>
        <el-input v-model="form.bank_name" clearable />
      </el-form-item>
      <el-form-item label="账号" required>
        <el-input v-model="form.bank_account" clearable />
      </el-form-item>
      
    </template>

    <el-form-item label="开启状态" prop="status">
      <el-select v-model="form.status" placeholder="开启状态" clearable>
        <el-option
          v-for="item in openEnums"
          :label="item.label"
          :key="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="备注" required>
      <el-input v-model="form.remark" clearable />
    </el-form-item>
    <el-form-item
      v-if="form.type == 3"
      label="微信收款码"
      prop="img"
      :rules="[{ required: true, message: '请上传收款码' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="wxsuccessUpload"
        :on-error="handleErr1"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
    <el-form-item
      v-if="form.type == 2"
      label="支付宝收款码"
      prop="alipay_img"
      :rules="[{ required: true, message: '请上传收款码' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="alipaySuccessUpload"
        :on-error="handleErr2"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
    <el-form-item
      v-if="form.type == 4"
      label="收款码（如有）"
      prop="img"
      :rules="[{ required: true, message: '请上传收款码' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="alipaySuccessUpload"
        :on-error="handleErr2"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
      <el-form-item
       v-if="form.type == 1"
      label="钱包二维码"
      prop="img"
      :rules="[{ required: true, message: '请上传钱包二维码' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="wxsuccessUpload"
        :on-error="handleErr1"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { getCurrentInstance, nextTick, onMounted, ref } from "vue";
import { withdrawTypeEnums, getLabelByVal, openEnums } from "@/config/enums";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  type: "",
  rate: "",
  bank_name: "",
  bank_branch: "",
  bank_account: "",
  coin_name: "USDT",
  coin_account: "",
  coin_blockchain: "",
  alipay_account: "",
  img: "",
  status: "",
  bank_owner: "",
  remark: "",
});
const props = defineProps(["item"]);

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    form.value = Object.assign(form, props.item);
  });
});

const { proxy } = getCurrentInstance();

const alipaySuccessUpload = (res) => {
  form.value.img = res.data.url;
};

const handleErr1 = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

const handleErr2 = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

const wxsuccessUpload = (res) => {
  form.value.img = res.data.url;
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}
/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
