# VSCode开发环境配置指南

## 1. 本地VSCode配置步骤

### 1.1 安装扩展
```bash
# 在VSCode扩展市场安装以下扩展：
- SFTP (by Natizyskunk)
- PHP Debug (by Xdebug)
- PHP Intelephense (代码智能提示)
- GitLens (Git增强)
```

### 1.2 创建项目配置文件
在项目根目录创建 `.vscode` 文件夹，并添加以下文件：

1. **`.vscode/sftp.json`** - SFTP同步配置
2. **`.vscode/settings.json`** - 工作区设置
3. **`.vscode/launch.json`** - 调试配置

### 1.3 配置SFTP连接
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `SFTP: Config` 创建配置文件
3. 修改配置文件中的服务器信息

## 2. 开发服务器配置步骤

### 2.1 环境准备
```bash
# 执行服务器配置脚本
chmod +x dev_server_setup.sh
sudo ./dev_server_setup.sh
```

### 2.2 数据库配置
```bash
# 创建开发数据库
mysql -u root -p
CREATE DATABASE facai16_dev;
CREATE USER 'facai16_dev'@'localhost' IDENTIFIED BY 'your_dev_password';
GRANT ALL PRIVILEGES ON facai16_dev.* TO 'facai16_dev'@'localhost';
FLUSH PRIVILEGES;
```

### 2.3 项目部署
```bash
# 创建项目目录
sudo mkdir -p /var/www/html/facai16
sudo chown -R www-data:www-data /var/www/html/facai16
sudo chmod -R 755 /var/www/html/facai16
```

## 3. 开发工作流

### 3.1 日常开发流程
1. 在本地VSCode中编辑代码
2. 保存文件时自动同步到开发服务器
3. 在浏览器中访问开发服务器进行测试
4. 使用VSCode调试器进行远程调试

### 3.2 调试步骤
1. 在VSCode中设置断点
2. 启动调试监听 (F5 或点击调试按钮)
3. 在浏览器中访问需要调试的页面
4. VSCode会自动停在断点处

### 3.3 常用命令
```bash
# 手动同步所有文件
Ctrl+Shift+P -> SFTP: Sync Local -> Remote

# 下载远程文件
Ctrl+Shift+P -> SFTP: Download Folder

# 查看SFTP日志
Ctrl+Shift+P -> SFTP: Show Log
```

## 4. 故障排除

### 4.1 SFTP连接问题
- 检查服务器IP、端口、用户名密码
- 确认SSH服务正常运行
- 检查防火墙设置

### 4.2 调试连接问题
- 确认Xdebug已安装并配置正确
- 检查本地IP是否正确配置
- 确认端口9003未被占用

### 4.3 文件权限问题
```bash
# 修复文件权限
sudo chown -R www-data:www-data /var/www/html/facai16
sudo chmod -R 755 /var/www/html/facai16
```

## 5. 安全建议

1. 使用SSH密钥认证替代密码
2. 配置防火墙只允许必要的端口
3. 定期更新服务器软件包
4. 不要在生产环境启用Xdebug
