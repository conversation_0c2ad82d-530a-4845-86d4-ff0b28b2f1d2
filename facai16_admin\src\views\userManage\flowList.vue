<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.class_id"
          placeholder="流水类型"
          clearable
        >
          <el-option
            v-for="item in fundingTypeList"
            :label="item.title"
            :value="item.id"
            :key="item.title"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="130" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="amount" label="数量" width="130" />
      <el-table-column prop="before" label="变化前" width="130" />
      <el-table-column prop="after" label="变化后" width="130" />
      <el-table-column prop="class_name" label="类型" width="130" />
      <el-table-column prop="desc" label="说明" width="180" />
      <el-table-column prop="create_at" label="添加时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";

const searchForm = ref({
  username: "",
  phone: "",
  class_id: "",
  starttime: "",
  endtime: "",
});

onMounted(() => {
  getList();
  getFundingList()
});

const fundingTypeList = ref([])
const getFundingList = async () => {
  const res = await proxy.$http({
    method: 'get',
    url: 'money/getMoneyClassLists'
  })
  if (res.code == 0) {
    fundingTypeList.value = res.data
  }
}

const { proxy } = getCurrentInstance();

const tableData = ref([]);

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
    tableLoading.value = true;
  const res = await proxy.$http({
    method: "get",
    url: "/MoneyLog/getMoneyLogLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total; 
  }
};
</script>

<style lang="less" scoped></style>
