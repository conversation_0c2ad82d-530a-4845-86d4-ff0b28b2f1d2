<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							新手指南
						</view>
					</template>

				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="p-32">
						<box-box :blue="true" v-for="(item,index) in list" class="mb-24">
							<view class=" r-20 p-24 flex  color-#fff">
								<!-- <view class="mr-24">
									<image src="https://dummyimage.com/193x193" mode="" class="block size-193 r-12">
									</image>
								</view> -->
								<view class="flex1 pr-24 flex flex-col justify-between">
									<view>
										<view class="t-34 lh-48 ddd mb-8">
											A: {{item.title}}
										</view>
										<view class=" t-26 lh-37 ddd2">
											Q: {{item.desc}}
										</view>
									</view>
									<!-- <view>
										<view class=" t-24 lh-33">
											2022-08-08 12:00:00
										</view>
									</view> -->
								</view>
							</view>
						</box-box>


					</view>

					<view class="btn-area"></view>
				</scroll-view>
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import * as UserApi from '@/api/user.js'
	export default {
		data() {
			return {
				list: []
			};
		},
		onShow() {
			this.getQuestion()
		},
		methods: {
			getQuestion() {
				UserApi.question().then(res => {
					if (res.code == 0) {
						this.list = res.data
					}
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>