<template>
	<view>
		<uni-popup ref="popup" type="bottom" :safeArea="false" isMaskClick>
			<view :class="{showScreenshot:showScreenshot}">
				<view class="fcc-h flex1" v-if="showScreenshot">
					<slot name="screenshot"></slot>
				</view>
				<view class="pop-box" :class="{'bg-fff':bg}">
					<view class="fc-bet h-110 px-32">
						<view>
							{{title}}
						</view>
						<view class="fcc" @click="$refs.popup.close()">
							<image src="/static/pop-x.png" mode="aspectFit" size-40></image>
						</view>
					</view>
					<view class="bd">
						<scroll-view scroll-y="true" style="max-height: 80vh;" :class="{min:min}">
							<view><slot></slot></view>
						</scroll-view>
					</view>
					<view class="btn-area bgfff"></view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name:"pop-box",
		props: {
			title: {
				type: String,
				default: '标题'
			},
			min:{
				type:Boolean,
				default:false
			},
			bg:{
				type:<PERSON>olean,
				default:false
			},
			showScreenshot:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
			open() {
				this.$refs.popup.open()
			},
			close(){
				this.$refs.popup.close()
			}
		},
	}
</script>

<style lang="scss" scoped>
	
	.pop-box{
		background: #F6F9FC;
		border-radius: 32rpx 32rpx 0px 0px;
	}
	.bg-fff{
		background: #fff;
	}
	.min{
		min-height: 42vh;
	}
</style>