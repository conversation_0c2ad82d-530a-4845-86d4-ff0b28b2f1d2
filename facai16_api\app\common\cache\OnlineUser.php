<?php
namespace app\common\cache;
use think\facade\Cache;

class OnlineUser
{
    // 有序集合键名
    protected $onlineKey = 'online_user';

    // 用户心跳超时时间(秒)
    protected $timeout = 3600; // 60分钟


    protected  $redis;

    public function __construct()
    {
        $this->redis =  Cache::store('redis')->handler();
    }

    /**
     * 用户上线或更新活跃时间
     * @param int $userId 用户ID
     * @return bool
     */
    public function heartbeat($userId): bool
    {
        $timestamp = time();
        return $this->redis->zAdd($this->onlineKey, $timestamp, $userId);
    }

    /**
     * 获取在线会员数量
     * @return int
     */
    public function count()
    {
        $min = time() - $this->timeout;
        return $this->redis->zCount($this->onlineKey, $min, '+inf');
    }

    /**
     * 获取在线会员列表
     * @param int $start 开始位置
     * @param int $end 结束位置
     * @param bool $withScores 是否返回时间戳
     * @return array
     */
    public function list($start = 0, $end = -1, $withScores = false)
    {
        $min = time() - $this->timeout;

        // 先移除超时的会员
        $this->redis->zRemRangeByScore($this->onlineKey, '-inf', $min);

        $options = [];

        if ($withScores)
        {
            $options['withscores'] = true;
        }

        return $this->redis->zRevRange($this->onlineKey, $start, $end, $options);
    }

    /**
     * 检查用户是否在线
     * @param int $userId 用户ID
     * @return bool
     */
    public function isOnline($userId): bool
    {
        $min    = time() - $this->timeout;
        $score  = $this->redis->zScore($this->onlineKey, $userId);

        return $score && $score >= $min;
    }

    /**
     * 用户下线
     * @param int $userId 用户ID
     * @return int
     */
    public function offline($userId): int
    {
        return $this->redis->zRem($this->onlineKey, $userId);
    }

    /**
     * 清除所有在线会员
     * @return int
     */
    public function clear(): int
    {
        return $this->redis->del($this->onlineKey);
    }
}