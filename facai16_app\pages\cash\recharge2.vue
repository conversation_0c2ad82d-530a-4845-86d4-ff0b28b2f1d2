<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							充值
						</view>
					</template>
					<template #right>
						<navigator url="/pages/cash/record">
							<view class="color-#fff w-128 h-46 t-26 fcc f-pr" @click="">
								<image src="/static/cash/czmx.png" mode="aspectFit" class="w-128 h-49 block"></image>
								<view class="fcc f-pa inset">
									充值明细
								</view>
							</view>
						</navigator>
					</template>
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="f-pr">
						<image src="/static/cash/kbg.png" mode="widthFix" class="w-750 h-300 block"></image>
						<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
							<view class="flex pt-70 mb-40">
								<!-- <view class="w-280">
									<view class="mb-4">
										账户余额(USDT)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										{{userInfo.money}}
									</view>
								</view> -->
								<view class="w-280">
									<view class="mb-4">
										账户余额(元)
									</view>
									<view class="t-44 lh-51 DIN fw-700">
										{{userInfo.money}}
									</view>
								</view>
							</view>
							<view>
								充值请联系在线客服获取平台收款账号,请转账成功后提交订单
							</view>
						</view>
					</view>
					<view class="mx-34 mb-32">
						<box-box :blue="true">
							<view class="px-32 ">
								<view class="t-32 color-#fff lh-45 pt-26 pb-19">
									选择充值方式
								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>
								<view class="flex flex-wrap justify-between pb-20">
									<!-- <view class="mb-12 w-300 92" @click="tabIndex =0">
										<image src="/static/cash/cz-u-h.png" mode="widthFix" class="w-300 h-92 block"
											v-if="tabIndex==0"></image>
										<image src="/static/cash/cz-u.png" mode="widthFix" class="w-300 h-92 block"
											v-else></image>
									</view> -->
									<view class="mb-12 w-300 92" @click="tabIndex = 1">
										<image src="/static/cash/cz-b-h.png" mode="widthFix" class="w-300 h-92 block"
											v-if="tabIndex==1"></image>
										<image src="/static/cash/cz-b.png" mode="widthFix" class="w-300 h-92 block"
											v-else></image>
									</view>

								</view>

								<view v-if="tabIndex==1">
									<view v-for="(item,index) in paymentMethods" :key="item.id"
										@click="clickPayment(item)">
										<view class="flex color-#fff">
											<view class="mr-17">
												<image src="/static/pay-weixin.png" mode="aspectFit"
													class="size-48 block" v-if="item.class_id==1">
												</image>
												<image src="/static/pay-alipay.png" mode="aspectFit"
													class="size-48 block" v-if="item.class_id==2">
												</image>
												<image src="/static/pay-bank.png" mode="aspectFit" class="size-48 block"
													v-if="item.class_id==3">
												</image>
											</view>
											<view class="flex1 pb-23">
												<view class="mb-4 t-32 lh-45">
													{{item.title}}
												</view>
												<view class="t-24 lh-33">
													<view v-if="item.min&&item.max">支持单笔 {{item.min}}-{{item.max}}
													</view>
												</view>
											</view>
											<view>
												<image src="/static/check1-on.png" v-if="this.currentMethod.id==item.id"
													mode="aspectFit" class="size-40 block">
												</image>
												<image src="/static/check-1-g.png" v-else mode="aspectFit"
													class="size-40 block">
												</image>
											</view>
										</view>
										<uv-gap v-if="index<paymentMethods.length-1" height="1" bgColor="#3766C3"
											margin-bottom="40rpx"></uv-gap>
									</view>


								</view>
							</view>
						</box-box>
					</view>
					<view class="mx-34 mb-32" v-if="tabIndex==1">
						<box-box :blue="true">
							<view class="px-32">
								<view class="t-32 color-#fff lh-45 pt-26  fc-bet pb-19">
									<text>
										选择金额(元)
									</text>
									<text class="t-22">
										单次充值需大于100元
									</text>
								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>


								<view class="flex flex-wrap mr-[-30rpx]">
									<view class="fcc w-196 h-92 mr-12 mb-20 r-20 DIN  t-40"
										v-for="(item,index) in moneyList" :key="index" @click="moneyClick(item,index)"
										:class="[index == moneyIndex ? 'bg-#407CEE color-#fff' : 'bg-#fff c2']">
										{{item}}
									</view>
								</view>

								<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc">
									<view class="c2 t-26 lh-37 pl-36 pr-20 fw-600">
										自定义
									</view>
									<view class="w-1 bg-#979797 h-29"></view>
									<view class="input-row flex1 fc">
										<input type="tel" class="w-370 ml-20 h-80 lh-80 t-40 DIN" v-model="moneyVal">
									</view>
									<view class="c2 t-26 lh-37 pl-36 pr-20 ">
										元
									</view>
								</view>
								<view class="h-20"></view>
							</view>
						</box-box>
					</view>
					<view class="mx-34 mb-32" v-else>
						<box-box :blue="true">
							<view class="px-32">
								<view class="t-32 color-#fff lh-45 pt-26  fc-bet pb-19">
									<text>
										选择金额(USDT)
									</text>

								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="32rpx"></uv-gap>


								<view class="flex flex-wrap mr-[-30rpx]">
									<view class="fcc w-196 h-92 mr-12 mb-20 r-20 DIN  t-40"
										v-for="(item,index) in moneyList" :key="index" @click="moneyClick(item,index)"
										:class="[index == moneyIndex ? 'bg-#407CEE color-#fff' : 'bg-#fff c2']">
										{{item}}
									</view>
								</view>

								<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc">
									<view class="c2 t-26 lh-37 pl-36 pr-20 fw-600">
										自定义
									</view>
									<view class="w-1 bg-#979797 h-29"></view>
									<view class="input-row flex1 fc">
										<input type="number" class="w-300 ml-20 h-80 lh-80 t-40 DIN" v-model="moneyVal">
									</view>
									<view class="c2 t-26 lh-37 pl-36 pr-20 ">
										USDT
									</view>
								</view>
								<view class="h-20"></view>
							</view>
						</box-box>
					</view>

					<view class="mx-34 mb-32" v-if="needCertificate">
						<box-box :blue="true">
							<view class="px-32">
								<view class="t-32 color-#fff lh-45 pt-26  fc-bet pb-19">
									<text>
										信息填写
									</text>
								</view>
								<uv-gap height="1" bgColor="#3766C3" marginBottom="28rpx"></uv-gap>
								<template v-if="tabIndex==1">
									<PaymentInfo :info="currentMethod.account_info||{}" />
								</template>
								<view class="t-32 color-#fff lh-45  fc-bet pb-20">
									<text>
										汇款人名称
									</text>

								</view>
								<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
									<view class="input-row flex1 fc">
										<input type="text" v-model="account" class="w-full h-80 lh-80 t-32 c2"
											placeholder="请输入汇款人名称">
									</view>

								</view>

								<view class="t-32 color-#fff lh-45  pt-10 fc-bet pb-20">
									<text>
										付款凭证
									</text>

								</view>
								<!-- 上传图片 -->
								<uv-upload :fileList="fileList1" name="1" multiple :maxCount="1" width="160rpx"
									@afterRead="afterRead" @delete="deletePic" height="160rpx">
									<image src="/static/upload-icon.png" mode="aspectFit" class="block size-160">
									</image>
								</uv-upload>



								<view class="h-20"></view>
							</view>
						</box-box>
					</view>
					<view class="mx-34 mb-32">
						<box-box :blue="true">
							<view class="p-32">
								<view class="fc-bet mb-32">
									<image src="/static/sign/tit-arr.png" mode="aspectFit" class="block w-216 h-33">
									</image>
									<view class="fcc t-30 color-#fff lh-40 px-20">
										温馨提示
									</view>
									<image src="/static/sign/tit-arr.png" mode="aspectFit"
										class="block w-216 h-33 rotate-180deg"></image>
								</view>
								<view class="color-#ccc t-28 lh-40">
									温馨提示: <br>
									充值时间为9:00-20:00<br>
									银行卡最低充值额度为300USDT<br>
									最低充值额度为30USDT<br>
									充值成功后请提供交易成功凭证截图<br>
								</view>
							</view>
						</box-box>
					</view>

					<view class="h-40"></view>
				</scroll-view>

			</view>
			<view class="bg-#fff">
				<view class="py-10 px-40">
					<view class="btn-full fcc" @click="toRechart">
						立即充值
					</view>
					<view class="btn-area"></view>
				</view>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import * as CashApi from '@/api/cash.js'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	import PaymentInfo from './components/payment-info.vue'
	import apiUrl from '@/utils/request/env.js'
	export default {
		mixins: [needAuth],
		components: {
			PaymentInfo
		},
		data() {
			return {
				tabIndex: 1,
				moneyIndex: 0,
				moneyList: [
					500, 3000, 6000, 10000, 20000, 50000
				],
				moneyVal: null,
				fileList1: [],
				certificateImg: "",
				account: '',
				paymentMethods: [],
				timer: null,
				currentMethod: {},
			};

		},
		computed: {
			...mapState(['userInfo', 'moneyDetail']),
			disabled() {
				return false
			},
			seletMoneyVal() {
				if (this.moneyIndex > -1) {
					return this.moneyList[this.moneyIndex]
				} else {
					return null
				}
			},
			payMoney() {
				return this.moneyVal || this.seletMoneyVal
			},
			needCertificate() {
				return this.tabIndex != 1 || this.currentMethod.style == 2 || this.currentMethod.style == 3
			},
			rechrgeName() {
				if (this.tabIndex == 0) {
					return 'USDT充值'
				}
				if (this.currentMethod.style != 2 && this.currentMethod.style != 3) {
					return '三方充值'
				}
				return this.account
			},
			rechargeAccount() {
				if (this.currentMethod.style == 2 || this.currentMethod.style == 3) {
					if (this.currentMethod.account_info.type == 0 || this.currentMethod.account_info.type ==
						4) {
						return this.currentMethod.account_info.bank_account
					} else if (this.currentMethod.account_info.type == 1) {
						return this.currentMethod.account_info.coin_account
					} else {
						return this.currentMethod.account_info.alipay_account
					}
				} else {
					return ''
				}
			}
		},
		watch: {
			moneyVal(newValue, oldValue) {
				if (newValue) {
					this.moneyIndex = -1
				} else {
					this.moneyIndex = 0
				}
			},
			payMoney() {
				clearTimeout(this.timer)
				this.timer = setTimeout(() => {
					this.getPaymentMethods()
					clearTimeout(this.timer)
					this.timer = null
				}, 500)
			}
		},
		methods: {
			async toRechart() {
				let err = ''
				if (this.needCertificate) {
					if (!this.account) {
						err = '请填写汇款人名称'
					} else if (!this.certificateImg) {
						err = '请上传付款凭证'
					}
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await CashApi.recharge({
							money: this.payMoney,
							name: this.rechrgeName,
							id: this.currentMethod.id,
							account: this.rechargeAccount,
							img: this.certificateImg
						})
						if (resData.data.is_jump && resData.data.url) {
							window.open(resData.data.url, '_blank') // H5 中使用
						}

						uni.navigateTo({
							url: '/pages/cash/record'
						})
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			},
			moneyClick(item, index) {
				this.moneyIndex = index
				this.moneyVal = null
			},
			clickPayment(item) {
				const max = +item.max || Number.MAX_VALUE
				const min = +item.min || 1
				if (this.payMoney >= min || this.payMoney <= max) {
					this.currentMethod = item
				} else {
					uni.showToast({
						duration: 2000,
						message: '该支付方式不满足条件'
					})
				}
			},
			async getPaymentMethods() {
				try {
					const resData = await CashApi.getPaymentMethods({
						money: this.payMoney
					})
					if (resData.code == 0 && resData.data instanceof Array) {
						this.paymentMethods = resData.data.sort((a, b) => a.class_id - b.class_id)
						if (this.paymentMethods.length > 0) {
							this.currentMethod = this.paymentMethods[0]
						}
					}
				} catch (e) {

				}
			},
			// 删除图片
			deletePic(event) {
				this.fileList1.splice(event.index, 1)
				this.certificateImg = ''
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				try {
					let lists = [event.file]
					this.fileList1[0] = {
						...event.file,
						status: 'uploading',
						message: '上传中'
					}
					console.log(lists[0])
					const result = await this.uploadFilePromise(lists[0])
					this.fileList1[0] = Object.assign(this.fileList1[0], {
						status: 'success',
						message: '',
						url: `${apiUrl.baseUrl}${result}`
					})
				} catch (e) {
					this.fileList1 = []
				}
			},
			uploadFilePromise(file) {
				return new Promise((resolve, reject) => {
					let tempUrl = file[0].url;
					let a = uni.uploadFile({
						url: apiUrl.baseUrl + '/api/upload',
						header: {
							'accept-token': uni.getStorageSync('Accept-Token')
						},
						filePath: tempUrl,
						name: 'file',
						success: (res) => {
							uni.hideLoading()
							this.certificateImg = ''
							let data = JSON.parse(res.data)
							if (data.code == 0) {
								this.certificateImg = data.data.url
								resolve(data.data.url)
							} else {
								reject()
							}
						},
						fail: () => {
							uni.hideLoading()
							reject()
						}
					});
				})
			},
			toCopy(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					},

					fail: () => {
						uni.showToast({
							title: "复制失败"
						})
					}

				})
			}
		},
		onHide() {
			clearTimeout(this.timer)
			this.timer = null
		},
		onShow() {
			this.getPaymentMethods()
		}
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/cash/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>