<?php
/**
 * Created by PhpStorm.
 * User: cpu281
 * Date: 4/29/22
 * Time: 2:42 PM
 */


namespace app\common\utils;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use think\exception\ValidateException;
use think\facade\Filesystem;
use think\facade\Log;
use think\File;


/**
 * excel
 */
class Excel
{

    /**
     * 设置头部
     * @param string $type
     * @return array
     */
    public static function setHeader(string $type): array
    {
        $data = [];
        switch ($type)
        {
            case 'recharge':
                $data = [
                    'id'            => '编号',
                    'uid'           => '用户id',
                    'username'      => '用户名称',
                    'phone'         => '手机号码',
                    'is_test'       => '是否测试',
                    'status'        => '状态',
                    'channel_id'    => '渠道id',
                    'channel_name'  => '渠道名称',
                    'class_id'      => '渠道类型ID',
                    'class_name'    => '渠道类型',
                    'order_no'      => '订单号',
                    'account_id'    => '关联的账号id',
                    'account'       => '付款地址',
                    'name'          => '支付用户名称',
                    'amount'        => '金额',
                    'amount_real'   =>'实际金额',
                    'create_at'     => '创建时间',
                    'update_at'     => '更新时间',
                    'remark'        => '备注',
                    'rate'          => '汇率'
                ];
                break;

            case 'withdraw':
                $data = [
                    'id'              => '编号',
                    'uid'             => '用户id',
                    'username'        => '用户名称',
                    'phone'           => '手机号码',
                    'is_test'         => '是否测试',
                    'status'          => '状态',
                    'order_no'        => '订单号',
                    'name'            => '用户名称',
                    'amount'          => '金额',
                    'amount_real'     => '实际金额',
                    'create_at'       => '创建时间',
                    'update_at'       => '更新时间',
                    'remark'          => '备注',
                    'type'            => '提款类型',
                    'bank_name'       => '银行名称',
                    'bank_branch'     => '银行支行',
                    'bank_account'    => '银行账号',
                    'coin_name'       => '币名称',
                    'coin_blockchain' => '币区块链',
                    'coin_account'    => '币账号',
                    'alipay_account'  => '支付宝账号',
                    'exchange_rate'   => '汇率',
                    'handling_fee'    => '手续费',
                    'handling_rate'   => '手续费率'
                ];
                break;

            case 'goods':
                $data = [
                    'id'                => '编号',
                    'uid'               => '用户id',
                    'username'          => '用户名称',
                    'phone'             => '手机号码',
                    'is_test'           => '是否测试',
                    'status'            => '发货状态',
                    'order_no'          => '订单号',
                    'goods_id'          => '商品id',
                    'goods_title'       => '商品名称',
                    'money'             => '金额',
                    'deliver_title'     => '发货名称',
                    'deliver_order_no'  => '发货单号',
                    'deliver_time'      => '发货时间',
                    'address_name'      => '姓名',
                    'address_phone'     => '手机号',
                    'address_city'      => '市区',
                    'address_place'     => '地址',
                    'desc'              => '描述',
                    'create_at'         => '创建时间',
                    'update_at'         => '更新时间',
                ];
                break;
        }

        return $data;
    }

    /**
     * 导入excel
     * @param File $file
     * @return array|string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public static function importExcel(File $file)
    {
        $files[] = $file;
        try
        {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx,csv'])
                ->check($files);
            // 将文件保存到本地
            $saveUrl = Filesystem::disk('public')->putFile('topic', $file);
            // 截取后缀
            $fileExtendName = $file->extension();
            // 判断是哪种格式
            if ($fileExtendName == 'xlsx')
            {
                $objReader = IOFactory::createReader('Xlsx');
            }
            elseif($fileExtendName == 'csv')
            {
                $objReader = IOFactory::createReader('Csv');
            }
            else
            {
                $objReader = IOFactory::createReader('Xls');
            }
            // 设置文件为只读
            $objReader->setReadDataOnly(TRUE);
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load(public_path() . 'storage/' . $saveUrl);
            //excel中的第一张sheet
            $sheet = $objPHPExcel->getSheet(0);
            // 取得总行数
            $highestRow = $sheet->getHighestRow();
            // 取得总列数
            $highestColumn = $sheet->getHighestColumn();
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;

            if ($lines <= 0)
            {
                return false;
            }
            // 直接取出excel中的数据
            $data = $objPHPExcel->getActiveSheet()->toArray();
            // 删除第一个元素（表头）
            array_shift($data);
            // 释放表格
            unset($objReader);
            unset($objPHPExcel);
            // 返回结果
            return $data;
        }
        catch (ValidateException $e)
        {
            return $e->getMessage();
        }
    }


    public static function createCsvFile($fileName, $type)
    {
        $header      = self::setHeader($type);
        $header      = array_values($header);
        $filePath    = 'storage/'. $fileName . '.csv';
        $savePath    = public_path() . $filePath;



        $spreadsheet = new Spreadsheet();
        // 创建sheet
        $sheet       = $spreadsheet->getActiveSheet();
        // 循环设置表头数据
        foreach($header as $key => $value)
        {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        $writer   = IOFactory::createWriter($spreadsheet, "Csv");

        $writer->save($savePath);

        unset($spreadsheet);

        unset($writer);

        if (!file_exists($savePath))
        {
            return false;
        }

        return $filePath;
    }


    public static function formatDataAppend($data, $type, $fileName): bool
    {
        $appendStr       = '';
        $header          = self::setHeader($type);
        $intersection    = array_intersect_key($data, $header);
        foreach ($header  as $index => $item)
        {
            $appendStr   .= '"'. $intersection[$index]  . "\t" . '"' . ',';
        }
        $appendStr   = substr($appendStr, 0 , -1);
        $appendStr   = $appendStr . "\r\n";
        $res         = file_put_contents($fileName, $appendStr, FILE_APPEND);

        if (empty($res))
        {
            return false;
        }

        return true;
    }



}