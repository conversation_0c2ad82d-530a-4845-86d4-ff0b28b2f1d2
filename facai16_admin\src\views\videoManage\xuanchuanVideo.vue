<template>
    <div class="pages">
        <el-form ref="formRef" class="form" label-width="150px" :model="numberValidateForm" >
            <!-- <el-form-item label="图片" prop="imgUrl" :rules="[
                { required: true, message: '请上传图片' },
            ]">
                <el-upload class="upload-demo" drag action="" multiple>
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                        Drop file here or <em>click to upload</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            jpg/png files with a size less than 500kb
                        </div>
                    </template>
                </el-upload>
            </el-form-item> -->
            <el-form-item label="视频地址">
                <el-input style="width: 60%;" v-model="numberValidateForm.videoUrl" :disabled="currentUser.role != 0" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm(formRef)" :disabled="currentUser.role != 0">Submit</el-button>
            </el-form-item>
        </el-form>
    </div>

</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
const formRef = ref()

const { proxy } = getCurrentInstance()
const numberValidateForm = reactive({
    imgUrl: '',
    videoUrl: '',
})

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"))
const submitForm = (formEl) => {
    if (!formEl) return
    formEl.validate((valid) => {
        if (valid) {
        } else {
        }
    })
}


</script>


<style lang="less" scoped>
.form {
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: flex-start;
    width: 100%;
}
.upload-demo {
    width: 60%;
}
::v-deep {
    .el-form-item {
        width: 100%;
    }
}
</style>