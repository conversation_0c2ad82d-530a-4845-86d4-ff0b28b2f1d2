<template>
	<view v-if="isBank">
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				银行
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_name}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_name)"></uv-button>
			</view>
		</view>
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				姓名
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_owner}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_owner)"></uv-button>
			</view>
		</view>
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				账号
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_account}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_account)"></uv-button>
			</view>
		</view>
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				支行
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_branch}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_branch)"></uv-button>
			</view>
		</view>
	</view>
	<view v-else>
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				姓名
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_name}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_name)"></uv-button>
			</view>
		</view>
		<view class="t-32 color-#fff lh-45  fc-bet pb-20">
			<text>
				账号
			</text>
		</view>
		<view class=" h-92  mb-20 r-20 DIN  t-40 bg-#fff fc px-30">
			<view class="input-row flex1 fc ">
				<text type="text" class="w-full h-80 lh-80 t-32 c2">
					{{info.bank_account}}
				</text>
				<uv-button type="primary" text="复制" sh shape="circle" @click="toCopy(info.bank_account)"></uv-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['info'],
		computed: {
			isBank() {
				return this.info.type == 0
			}
		},
		methods: {
			toCopy(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					},

					fail: () => {
						uni.showToast({
							title: "复制失败"
						})
					}

				})
			}
		},

	}
</script>