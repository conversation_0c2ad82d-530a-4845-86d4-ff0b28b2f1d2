<template>
	<view>

		<view class="page-bg">
			<top-status-bar></top-status-bar>
			<view class="h-66"></view>
			<view class="pt-6 pb-24 px-40 fc-bet">
				<view></view>
				<view class="fc">
					<!-- <view>
						<image src="/static/my/kefu.png?v=1" mode="aspectFit" class="size-44 block "></image>
					</view> -->
					<navigator url="/pages/setting/person">
						<view ml-48>
							<image src="/static/my/set.png" mode="aspectFit" class="size-44 block"></image>
						</view>
					</navigator>
				</view>
			</view>


			<view class="mx-32 mb-36">

				<view class="fc">
					<view class="f-pr mr-30">
						<view class="size-132  r-69">
							<image src="/static/my/tx.png" mode="aspectFill" class="size-132 r-100 block"></image>
						</view>

					</view>
					<view>
						<view class="flex items-end mb-13">
							<view class="t-40 lh-56 color-#fff fw-600 mr-16">
								{{userInfo.phone2}}
							</view>

						</view>
						<view class="fc">
							<view
								class="ml-26 f-pr pr-16 py-6 pl-36 t-20 color-#FFDEA7 lh-33 border-1 border-solid border-color-#B6A58D r-90">
								用户等级
								<image src="/static/my/lv.png" mode="aspectFit"
									class="block f-pa left-[-26rpx] top-[-6rpx] w-58 h-44"></image>
							</view>
							<view
								class="f-pr ml-42 pr-16 py-6 pl-36 t-20 color-#FFDEA7 lh-33 border-1 border-solid border-color-#B6A58D r-90">
								{{userInfo.level_name}}
								<image src="/static/my/xz1.png" mode="aspectFit"
									class="block f-pa left-[-20rpx] top-[-8rpx] w-54 h-60"></image>
							</view>
						</view>
					</view>
				</view>

			</view>
			<view class="f-pr z-2">
				<image src="/static/my/kbg.png" mode="widthFix" class="w-750 h-330 block f-pr"></image>
				<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
					<view class="flex pt-60 mb-40 justify-between items-center">
						<view class="t-22 lh-30 color-#fff">
							<view>
								账户余额(USDT)
							</view>
							<view class="t-48 lh-56 DIN fw-600">
								{{userInfo.money || '0.00'}}
							</view>
						</view>
						<view>
							<image src="/static/my/zjmx.png" @click="toRouter('/pages/cash/money-record')"
								mode="aspectFit" class="block w-146 h-51"></image>
						</view>
					</view>

				</view>
				<view
					class="fc-bet bg-#fff bg-op-14 f-pa left-32 right-32 bottom-40 color-#fff px-32 py-12 rounded-b-[20rpx]">
					<view>
						<view class="t-22 lh-30">
							今日作业
						</view>
						<view class="DIN fw-600 t-38 lh-45">
							{{todayTeamTotalSum || '0.00'}}
						</view>
					</view>
					<view>
						<view class="t-22 lh-30">
							今日绩效
						</view>
						<view class="DIN fw-600 t-38 lh-45">
							{{todayIncomeSum || '0.00' }}
						</view>
					</view>
					<view>
						<view class="t-22 lh-30">
							累计收益
						</view>
						<view class="DIN fw-600 t-38 lh-45">
							{{incomeTotalSum || '0.00'}}
						</view>
					</view>
					<view>
						<view class="t-22 lh-30">
							待收收益
						</view>
						<view class="DIN fw-600 t-38 lh-45">
							{{itemNotFinishedSum || '0.00'}}
						</view>
					</view>
				</view>
			</view>


			<view class="mx-32 mb-14 mt-[-15rpx]">
				<box-box :blue="true">
					<view class="flex flex-wrap color-#fff py-15">
						<!-- <view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/cash/transfer')">
							<image src="/static/my/1.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								我要转账
							</view>
						</view> -->
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/invite/invite')">
							<image src="/static/my/2.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								邀请好友
							</view>
						</view>
						<!-- 	<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/cash/yuebao')">
							<image src="/static/my/3.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								余额宝
							</view>
						</view> -->
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/my/quan')">
							<image src="/static/my/4.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								我的卡券
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/project/project')">
							<image src="/static/my/5.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								项目专区
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/team/team')">
							<image src="/static/my/6.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								我的团队
							</view>
						</view>
						<!-- <view class="w-[25%] fcc-h py-17">
							<image src="/static/my/7.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								奖励制度
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17">
							<image src="/static/my/8.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								我的集卡
							</view>
						</view> -->
					</view>
				</box-box>
			</view>


			<view class="mx-32 mb-24 f-pr">
				<image src="/static/my/yq.png" @click="toRouter('/pages/invite/invite')" mode="widthFix"
					class="block w-[100%]"></image>
			</view>

			<view class="mx-32 mb-24">
				<box-box :blue="true">
					<view class="flex flex-wrap color-#fff py-15">
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/cash/record')">
							<image src="/static/my/img1.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								交易记录
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/invest/record')">
							<image src="/static/my/img2.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								投资记录
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/setting/realname')">
							<image src="/static/my/img3.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								身份认证
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/setting/about')">
							<image src="/static/my/img4.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								关于我们
							</view>
						</view>
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/my/bank')">
							<image src="/static/my/img5.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								绑定银行卡
							</view>
						</view>
						<!-- <view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/my/usdt')">
							<image src="/static/my/img6.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								绑定钱包地址
							</view>
						</view> -->
						<view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/notice/notice')">
							<image src="/static/my/img7.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								消息中心
							</view>
						</view>
						<!-- <view class="w-[25%] fcc-h py-17" @click="toRouter('/pages/notice/notice')">
							<image src="/static/my/img8.png" mode="aspectFit" size-88 block></image>
							<view class="t-24  lh-34 mt-8">
								我的客服
							</view>
						</view> -->
					</view>
				</box-box>
			</view>


			<view class="mx-32">
				<view class="btn-full fcc !r-20 bg-op-40" @click="layout">
					退出登录
				</view>
			</view>


			<view class="h-40"></view>
			<foot-nav-bar :fixed="true" index="3"></foot-nav-bar>
		</view>



		<fixed-kefu></fixed-kefu>

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {

			};
		},
		computed: {
			...mapState(['userInfo', 'moneyDetail', 'incomeTotalSum', 'itemNotFinishedSum', 'todayIncomeSum',
				'todayTeamTotalSum',
			]),
		},
		methods: {
			toRouter(url) {
				uni.navigateTo({
					url: url
				})
			},
			layout() {
				this.$store.dispatch('logout')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/index/bg.png) 0 0 no-repeat #000511;
		background-size: 100% auto;
	}
</style>