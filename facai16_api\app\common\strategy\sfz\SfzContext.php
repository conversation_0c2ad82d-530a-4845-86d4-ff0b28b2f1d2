<?php

namespace app\common\strategy\sfz;

/**
 * 实名认证
 * Class SmsContext
 * @package app\common\strategy\sms
 */
class SfzContext
{
    private  $strategy;

    // 构造函数通过传入策略实例来选择具体的短信发送策略
    public function __construct(SfzInterface $strategy)
    {
        $this->strategy = $strategy;
    }

    // 使用当前选择的策略来发送短信
    public function sendMessage($data)
    {
       return $this->strategy->send($data);
    }
}
