<template>
    <el-form label-width="180px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="手机号" required>
            <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="智" required>
            <el-input v-model="form.zhi" />
        </el-form-item>
        <el-form-item label="启" required>
            <el-input v-model="form.qi" />
        </el-form-item>
        <el-form-item label="未" required>
            <el-input v-model="form.wei" />
        </el-form-item>
        <el-form-item label="来" required>
            <el-input v-model="form.lai" />
        </el-form-item>
        <el-form-item label="创" required>
            <el-input v-model="form.chuang" />
        </el-form-item>
        <el-form-item label="领" required>
            <el-input v-model="form.ling" />
        </el-form-item>
        <el-form-item label="无" required>
            <el-input v-model="form.wu" />
        </el-form-item>
        <el-form-item label="限" required>
            <el-input v-model="form.xian" />
        </el-form-item>
        
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'
import { withdrawTypeEnums, getLabelByVal } from '@/config/enums'

const form = ref({
    phone: "",
    zhi: "",
    qi: "",
    wei: "",
    lai: "",
    chuang: "",
    ling: "",
    wu: "",
    xian: "",
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(() => {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({ form })

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/deep/ .el-radio-group {
    width: 220px;
}

.form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
    width: 100%;
}

/deep/ .el-form-item {
    align-items: flex-start;
}
</style>