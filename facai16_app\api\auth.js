// 引用网络请求中间件
import request from "../utils/request/request"
/**
 *    登陆请求
 */
export function login(data) {
	return request({
		url: '/login/login',
		method: 'POST',
		data,
		// isFormData:true
	})
}

export function getVerifyCode(data) {
	return request({
		url: '/sms/reset',
		method: 'POST',
		data
	})
}

export function getRegVerifyCode(data) {
	return request({
		url: '/sms/register',
		method: 'POST',
		data
	})
}

export function register(data) {
	return request({
		url: '/login/register',
		method: 'POST',
		data
	})
}

export function loginGoNext(data) {
	return request({
		url: '/login/goNext',
		method: 'POST',
		data
	})
}

export function sendEmail(email) {
	return request({
		url: '/auth/sendEmail',
		method: 'POST',
		data: {
			email: email
		},
		isFormData: true
	})
}
// 找回密码
export function retrievePassword(data) {
	return request({
		url: '/user/retrievePassword',
		method: 'POST',
		data
	})
}
// 修改密码
export function updatePassword(data) {
	return request({
		url: '/user/editPass',
		method: 'POST',
		data
	})
}

export function resetSms(data) {
	return request({
		url: '/sms/reset',
		method: 'POST',
		data
	})
}

export function updatePayPassword(data) {
	return request({
		url: '/user/editPin',
		method: 'POST',
		data
	})
}