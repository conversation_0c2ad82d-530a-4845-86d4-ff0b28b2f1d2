<?php
namespace app\index\service;

use app\common\repository\BankRepository;
use app\common\repository\RealNameRepository;
use app\common\repository\UserBankRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 银行卡管理
 */
class BankService
{
    /**
     * 再审核中的银行
     * @return array
     */
    public function bankInCheck(): array
    {
        $UserRepo               = new UserRepository();
        $user                   = $UserRepo->userByHeader();

        $RealNameRepo           = new RealNameRepository();

        $where      = [];
        $where[]    = ['uid','=',$user['id']];

        $info      = $RealNameRepo->findByCondition($where);

        if (empty($info))
        {
            return Result::success();
        }

        if ($info['status'] == 1)
        {
            return Result::success($info);
        }
        elseif ($info['status'] == 2)
        {
            return Result::success($info);
        }
        else
        {
            $UserBankRepo           = new UserBankRepository();
            $bank                   = $UserBankRepo->findByCondition(['uid' => $user['id']]);

            if (empty($bank))
            {
                return Result::success();
            }

            $data = [
                'bank_account'   => $bank['bank_account'],
                'bank_branch'    => $bank['bank_branch'],
                'bank_name'      => $bank['bank_name'],
                'create_at'      => $bank['create_at'],
                'update_at'      => $bank['update_at'],
                'create_time'    => $bank['create_time'],
                'update_time'    => $bank['update_time'],
                'sfz_name'       => $user['sfz_name'],
                'sfz_number'     => $user['sfz_number'],
                'phone'          => $user['phone'],
                'username'       => $user['username'],
                'uid'            => $user['id'],
                'is_test'        => $user['is_test'],
                'status'         => 0,
            ];

            return Result::success($data);
        }

    }


    /**
     * 编辑
     * @param array $params
     * @return array
     */
    public function edit(array $params): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
        $UserBankRepo       = new UserBankRepository();


        if ($params['type'])
        {
            $update = [
                'uid'               => $user['id'],
                'type'              => $params['type'],
                'bank_name'         => $params['bank_name'] ?? '',
                'bank_account'      => $params['bank_account'] ?? '',
                'bank_branch'       => $params['bank_branch'] ?? '',
                'update_time'       => Request::time(),
            ];

        }
        else
        {
            $update = [
                'uid'               => $user['id'],
                'coin_name'         => $params['coin_name'] ?? '',
                'coin_blockchain'   => $params['coin_blockchain'] ?? '',
                'coin_account'      => $params['coin_account'] ?? '',
                'update_time'       => Request::time(),
            ];

        }


        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['id', '=', $params['id']];
        $res        = $UserBankRepo->updateByCondition($where, $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }


    /**
     * 银行卡
     * @return array
     */
    public function banks(): array
    {
        $BankRepo           = new BankRepository();
        $data               = $BankRepo->selectByCondition();

        return Result::success($data);
    }

    /**
     * 银行卡管理
     * @param $type
     * @return array
     */
    public function lists($type): array
    {
        $UserRepo               = new UserRepository();
        $user                   = $UserRepo->userByHeader();
        $UserBankRepo           = new UserBankRepository();

        $where                  = [];
        $where[]                = ['uid', '=', $user['id']];
        $where[]                = ['type', '=', $type];
        $bankcard               = $UserBankRepo->selectByCondition($where);

        return Result::success($bankcard);
    }


    /**
     * 绑定银行卡
     * @param array $params
     * @return array
     */
    public function add(array $params): array
    {
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();
        $UserBankRepo       = new UserBankRepository();
        $userState          = $UserRepo->userStateByHeader();

        //请先进行实名认证
        if($userState['sfz_status'])
        {
            return Result::fail('请先进行实名认证');
        }


        $where          = [];
        $where[]        = ['uid', '=', $user['id']];

        switch ($params['type'])
        {
            case 0:

                //添加
                $where[]        = ['type', '=', 0];
                $count          = $UserBankRepo->countByCondition($where);

                //最多只能拥有五个账户
                if($count >= 2)
                {
                    return Result::fail('最多只能拥有2个账户');
                }

                $where       = [];
                $where[]     = ['bank_account', '=', $params['bank_account']];
                $isUse       = $UserBankRepo->findByCondition($where);

                if ($isUse)
                {
                    return Result::fail('该银行卡被使用');
                }

                //清空其他默认
                $where      = [];
                $where[]    = ['uid', '=', $user['id']];
                $where[]    = ['type', '=', 0];

                $UserBankRepo->updateByCondition($where,['default' => 0]);
                break;

                case 1:
                    //添加
                    $where[]        = ['type', '=', 1];

                    $count          = $UserBankRepo->countByCondition($where);

                    //最多只能拥有五个账户
                    if($count >= 2)
                    {
                        return Result::fail('最多只能拥有2个账户');
                    }

                    $where       = [];
                    $where[]     = ['coin_account', '=', $params['coin_account']];
                    $isUse       = $UserBankRepo->findByCondition($where);


                    if ($isUse)
                    {
                        return Result::fail('USDT地址被使用');
                    }

                    //清空其他默认
                    $where      = [];
                    $where[]    = ['uid', '=', $user['id']];
                    $where[]    = ['type', '=', 1];

                    $UserBankRepo->updateByCondition($where,['default' => 0]);
                    break;
                case 2:

                    //添加
                    $where[]        = ['type', '=', 2];

                    $count          = $UserBankRepo->countByCondition($where);

                    //最多只能拥有五个账户
                    if($count >= 2)
                    {
                        return Result::fail('最多只能拥有2个账户');
                    }

                    $where       = [];
                    $where[]     = ['alipay_account', '=', $params['alipay_account']];
                    $isUse       = $UserBankRepo->findByCondition($where);


                    if ($isUse)
                    {
                        return Result::fail('支付宝账户被使用');
                    }

                    //清空其他默认
                    $where      = [];
                    $where[]    = ['uid', '=', $user['id']];
                    $where[]    = ['type', '=', 2];

                    $UserBankRepo->updateByCondition($where,['default' => 0]);

                    break;
                    case 3:
                        //添加
                        $where[]        = ['type', '=', 3];
                        $count          = $UserBankRepo->countByCondition($where);

                        //最多只能拥有五个账户
                        if($count >= 2)
                        {
                            return Result::fail('最多只能拥有2个账户');
                        }

                        //清空其他默认
                        $where      = [];
                        $where[]    = ['uid', '=', $user['id']];
                        $where[]    = ['type', '=', 3];
                        $UserBankRepo->updateByCondition($where,['default' => 0]);
                    break;
        }


        $insert = [
            'uid'               => $user['id'],
            'type'              => $params['type'],
            'name'              => $user['sfz_name'],
            'bank_name'         => $params['bank_name'] ?? '',
            'bank_account'      => $params['bank_account'] ?? '',
            'bank_branch'       => $params['bank_branch'] ?? '',
            'coin_name'         => $params['coin_name'] ?? '',
            'coin_blockchain'   => $params['coin_blockchain'] ?? '',
            'coin_account'      => $params['coin_account'] ?? '',
            'alipay_account'    => $params['alipay_account'] ?? '',
            'alipay_img'        => $params['alipay_img'] ?? '',
            'wx_img'            => $params['wx_img'] ?? '',
            'default'           => 1,
            'username'          => $user['username'],
            'phone'             => $user['phone'],
            'is_test'           => $user['is_test'],
            'create_time'       => Request::time(),
            'update_time'       => Request::time(),
        ];

        $res = $UserBankRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }

    /**
     * 删除账户
     * @param int $id
     * @return array
     */
    public function delete(int $id): array
    {
        $UserBankRepo       = new UserBankRepository();
        $UserRepo           = new UserRepository();
        $user               = $UserRepo->userByHeader();

        $where      = [];
        $where[]    = ['uid', '=', $user['id']];
        $where[]    = ['id', '=', $id];

        $res        = $UserBankRepo->deleteByCondition($where);

        if (empty($res))
        {
            return Result::fail('删除失败');
        }

        return Result::success();
    }
}