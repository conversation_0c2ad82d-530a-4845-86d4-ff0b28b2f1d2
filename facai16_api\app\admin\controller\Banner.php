<?php

namespace app\admin\controller;

use app\admin\service\BannerService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;


/**
 * 横幅
 */
class Banner
{

    /**
     * 文章列表
     * @return mixed
     */
    public function getBannerLists(): J<PERSON>
    {
        $BannerService  = new BannerService();
        $data           = $BannerService->getBannerLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 文章信息
     * @return Json
     */
    public function getBannerInfo(): Json
    {
        $id             = Request::param('id',0);
        $BannerService  = new BannerService();
        $data           = $BannerService->getBannerInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加文章
     * @return Json
     */
    public function addBanner(): <PERSON><PERSON>
    {
        $param          = Request::param();
        $BannerService  = new BannerService();
        $data           = $BannerService->addBanner($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新文章
     * @return Json
     */
    public function updateBanner(): Json
    {
        $param          = Request::param();
        $BannerService  = new BannerService();
        $data           = $BannerService->updateBanner($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除文章
     * @return Json
     */
    public function deleteBanner(): Json
    {
        $id             = Request::param('id',0);
        $BannerService  = new BannerService();
        $data           = $BannerService->deleteBanner($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}