<template>
	<view>
		<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true" :refresher-triggered="refresherTriggered"
			@refresherrefresh='onRefresh' scroll-y="true" refresher-background='#ffffff00' style="height: 100vh;">
			<view class="page-">
				<view class="h-347 f-pr">
					<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
						<template #left>
							<view>
								<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
							</view>
						</template>
					</uv-navbar>
					<view class="fcc f-pa left-0 right-0 bottom-16">
						<image src="/static/choujiang/tit.png" mode="aspectFit" class="block w-470 h-190"></image>
					</view>
				</view>

				<view class="cjp">
					<view class="box">
						<LuckyWheel ref="myLucky" width="434rpx" height="434rpx" :blocks="blocks" :prizes="mapPrices"
							:buttons="buttons" @start="startCallBack" @end="endCallBack" :defaultConfig="defaultConfig"
							:defaultStyle="defaultStyle" />
					</view>
					<view class="f-pa left-0 right-0 bottom-30 fcc">
						<view class=" t-30 lh-42 color-#fff">
							剩余{{moneyDetail.raffle_num}}次
						</view>

					</view>
				</view>

				<view class="f-pr pt-38 mb-24">
					<view class="fcc f-pa left-0 top-0 right-0">
						<image src="/static/sign/sign-titbg.png" mode="aspectFit" class="block w-746 h-80"></image>
					</view>
					<view class="f-pr mx-32 bg-#fff py-44 px-40 c6 t-28 lh-40 r-20">
						<view class="fcc mb-32">
							<image src="/static/sign/tit-arr.png" mode="aspectFit" class="block w-216 h-33"></image>
							<view class="fcc t-28 c2 lh-40 px-20 fw-600">
								活动规则
							</view>
							<image src="/static/sign/tit-arr.png" mode="aspectFit"
								class="block w-216 h-33 rotate-180deg">
							</image>
						</view>
						<view v-if="ruleText">
							<rich-text space="nbsp" :nodes="ruleText"></rich-text>
						</view>
					</view>
				</view>



				<view class="f-pr pt-38 mb-24">
					<view class="fcc f-pa left-0 top-0 right-0">
						<image src="/static/sign/sign-titbg.png" mode="aspectFit" class="block w-746 h-80"></image>
					</view>
					<view class="f-pr mx-32 bg-#fff py-44 px-40 c6 t-28 lh-40 r-20">
						<view class="fcc mb-12">
							<image src="/static/sign/tit-arr.png" mode="aspectFit" class="block w-216 h-33"></image>
							<view class="fcc t-28 c2 lh-40 px-20 fw-600">
								抽奖记录
							</view>
							<image src="/static/sign/tit-arr.png" mode="aspectFit"
								class="block w-216 h-33 rotate-180deg">
							</image>
						</view>
						<view>
							<DrawItem v-for="item in list" :key="item.id" :item="item" />
							<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
						</view>
						<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#000" lineColor="#000" line
							@loadmore="scrollHandle" />
					</view>
				</view>
				<view class="h-40"></view>
				<view class="btn-area"></view>
			</view>
		</scroll-view>

		<fixed-kefu></fixed-kefu>
		<!-- 中奖弹出框 -->
		<uv-overlay :show="popShow" @click="popShow = false">
			<view class="fcc-h h-full">
				<view class="w-682">
					<box-box :blue="true">
						<view class="f-pr pt-119">
							<view class="f-pa left-0 right-0 fcc top-[-217rpx]">
								<image src="/static/choujiang/titimg.png" mode="aspectFit" class="block w-568 h-297">
								</image>
							</view>
							<view class="mx-36 r-16 bd pt-42 fcc-h mb-24">
								<image src="/static/choujiang/gxn.png" mode="aspectFit" class="block w-285 h-48 mb-50">
								</image>

								<view class="mb-14">
									{{this.bousInfo['msg']}}
								</view>
								<!-- <view class="c2 t-32 lh-47">
									礼品名称<text class="b">{{prizes[winIndex].fonts[0].text}}</text>
								</view> -->
								<view class="h-54"></view>
							</view>

							<view class="mx-36">
								<view class="btn-full fcc !r-20">
									祝您生活愉快
								</view>
							</view>
							<view class="h-36"></view>
						</view>
					</box-box>
				</view>

			</view>
		</uv-overlay>
	</view>
</template>

<script>
	import LuckyWheel from '@/components/@lucky-canvas/uni/lucky-wheel'
	import {
		mapState
	} from 'vuex'
	import {
		htmlDecodeByRegExp
	} from '@/utils/index.js'
	import * as UserApi from '/api/user.js'
	import DrawItem from './draw-item.vue'
	export default {
		components: {
			LuckyWheel,
			DrawItem,
		},
		data() {
			return {
				newMan: false,
				popShow: false,
				defaultStyle: {
					fontColor: "#2A61DD"
				},
				blocks: [{
					padding: '0px',
				}],
				winIndex: 0,
				defaultConfig: {
					gutter: 4,
					offsetDegree: 45
				},
				buttons: [{
					radius: '45%',
					imgs: [{
						src: '/static/choujiang/cjzz.png?v=1',
						width: '120%',
						top: '-115%'
					}]
				}],
				mapPrices: [],
				bousInfo: null,
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		computed: {
			...mapState(['userInfo', 'moneyDetail', 'prizes', 'lottoryDetailsInfo', 'lottoyDetailsType']),
			ruleText() {
				if (!this.lottoryDetailsInfo) {
					return ''
				} else {
					return htmlDecodeByRegExp(this.lottoryDetailsInfo.content).replace(
						"<img",
						'<img  mode="widthFix"'
					)
				}
			},
			priceInfo() {
				if (lottoyDetailsType) {
					const temp = lottoyDetailsType.split(',')
					if (temp.length == 2) {
						const temp1 = temp[0].spit('|')
						const temp2 = temp[1].spit('|')
						let arrayTemp = []
						arrayTemp = temp1.map((item, index) => {
							return {
								title: item,
								value: temp2[item, index] || 0
							}
						})
						return arrayTemp
					}
				} else {
					return []
				}
			},
		},
		watch: {
			prizes(newVal) {
				if (newVal.length > 0) {
					this.mapPrices = newVal.map((item) => {
						return {
							fonts: [{
								text: item.title,
								top: '10%',
								fontSize: '14px'
							}],
							background: '#fff'
						};
					})
				}
			},
		},
		onShow() {
			this.getRecord()
			// this.$refs.popup.open()
			this.mapPrices = this.prizes.map((item) => {
				return {
					fonts: [{
						text: item.title,
						top: '10%',
						fontSize: '14px'
					}],
					background: '#fff'
				};
			})
		},
		methods: {
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await UserApi.drawRecord({
						page: this.page
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
			// 点击抽奖按钮触发回调
			startCallBack() {
				if (!this.moneyDetail.raffle_num) {
					uni.showToast({
						title: '暂无抽奖机会'
					})
					return
				}
				// 先开始旋转
				try {
					this.bousInfo = null
					UserApi.getRaffleDraw().then(res => {
						if (res.code == 0) {
							this.bousInfo = res.data
							this.bousInfo['msg'] = res.msg
							this.$refs.myLucky.play()
							this.$store.dispatch('getUserMoneyDetail')
							// 使用定时器来模拟请求接口
							setTimeout(() => {
								// 假设后端返回的中奖索引是0
								const index = uni.$uv.random(0, 7)
								this.winIndex = index
								// 调用stop停止旋转并传递中奖索引
								this.$refs.myLucky.stop(index)
							}, 2000)
						}
					})
				} catch (e) {
					this.$refs.myLucky.stop(index)
				}
			},
			// 抽奖结束触发回调
			endCallBack(prize) {
				this.popShow = true
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page- {
		min-height: 100vh;
		background: url(/static/article/bg.jpg) 0 -250rpx no-repeat #022044;
		background-size: 100% auto;
	}

	.cjp {
		background: url(/static/choujiang/cjp.png) center no-repeat;
		width: 750rpx;
		height: 758rpx;
		background-size: cover;

		position: relative;
		margin-bottom: 91rpx;

		.box {
			width: 434rpx;
			height: 434rpx;
			// background: red;
			background: #2E64E0;
			// opacity: .5;
			border-radius: 50%;
			position: absolute;
			left: 152rpx + 5rpx;
			top: 115rpx+ 1rpx;
		}
	}

	.bd {
		background: linear-gradient(319deg, #E7F1FD 0%, #EEF6FF 100%);
	}
</style>