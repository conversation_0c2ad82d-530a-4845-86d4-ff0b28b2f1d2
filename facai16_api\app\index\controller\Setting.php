<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\SettingService;
use think\facade\Request;
use think\response\Json;


/**
 * 设置
 */
class Setting
{

    /**
     * 通用设置接口
     * @return Json
     */
    public function getOne(): Json
    {
        $params             = Request::only(['key']);

        $SettingService     = new SettingService();

        $result             = $SettingService->getOne($params['key']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 获取多个
     * @return Json
     */
    public function getMore(): Json
    {
        $params             = Request::only(['keys']);

        $SettingService     = new SettingService();

        $result             = $SettingService->getMore($params['keys']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

}