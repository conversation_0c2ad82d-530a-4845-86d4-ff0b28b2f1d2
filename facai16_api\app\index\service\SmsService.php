<?php
namespace app\index\service;

use app\common\define\Time;
use app\common\error\Common;
use app\common\error\User;
use app\common\repository\SmsCodeRepository;
use app\common\repository\TeamLogRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * Class PlugInService
 * @package app\home\service
 */
class SmsService
{

    /**
     * 短信验证码
     * @param $phone
     * @return array
     */
    public function register($phone): array
    {

        if(strlen($phone) < 10)
        {
            return Result::fail('请输入正确的手机号');
        }

        $UserRepo      = new UserRepository();
        $SmsCodeRepo   = new SmscodeRepository();

        $where    = [];
        $where[]  = ['phone', '=', $phone];

        $user     = $UserRepo->findByCondition($where);

        if($user)
        {
            return Result::fail('手机号已被使用');
        }

        $env         = env('APP_ENV');

        if ($env !='prod')
        {
            //return Result::success([],__('successfully_sent'));
        }

        $time    = Request::time();

        //查询验证码发送间
        $smsCode = $SmsCodeRepo->findByCondition(['phone' => $phone],'*', ['id' => 'desc']);


        //发送失败，距离上一次发送小于一分钟
        if($smsCode && ($smsCode['create_time'] > ($time - 300)))
        {
            return Result::fail('发送失败，距离上一次发送小于一分钟');
        }

        $smsCode    = rand(1111, 9999);

        //调用短信接口
        $SmsService = new \app\common\strategy\sms\SmsService();
        $result     = $SmsService->send($phone,$smsCode);

        //发送失败
        if ($result['code'])
        {
            return Result::fail('发送失败');
        }

        $insert = [
            'phone'         => $phone,
            'code'          => $smsCode,
            'create_time'   => $time,
            'update_time'   => $time,
        ];

        $res = $SmsCodeRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('发送失败');
        }

        //发送成功
        return Result::success();
    }


    /**
     * 重置
     * @param $phone
     * @return array
     */
    public function reset($phone)
    {

        if(strlen($phone) < 10)
        {
            return Result::fail('请输入正确的手机号');
        }

        $UserRepo      = new UserRepository();
        $SmsCodeRepo   = new SmscodeRepository();

        $where    = [];
        $where[]  = ['phone', '=', $phone];

        $user     = $UserRepo->findByCondition($where);

        if(empty($user))
        {
            return Result::fail('没有改用户信息');
        }

        $env         = env('APP_ENV');

        if ($env !='prod')
        {
            return Result::success();
        }

        $time    = Request::time();

        //查询验证码发送间
        $smsCode = $SmsCodeRepo->findByCondition(['phone' => $phone],'*', ['id' => 'desc']);


        //发送失败，距离上一次发送小于一分钟
        if($smsCode && ($smsCode['create_time'] > ($time - 300)))
        {
            return Result::fail('发送失败，距离上一次发送小于一分钟');
        }

        $smsCode    = rand(1111, 9999);

        //调用短信接口
        $SmsService = new \app\common\strategy\sms\SmsService();
        $result     = $SmsService->send($phone,$smsCode);

        //发送失败
        if ($result['code'])
        {
            return Result::fail('发送失败');
        }

        $insert = [
            'phone'         => $phone,
            'code'          => $smsCode,
            'create_time'   => $time,
            'update_time'   => $time,
        ];

        $res = $SmsCodeRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('发送失败');
        }

        //发送成功
        return Result::success();
    }

}