import { createRouter, createWebHashHistory } from "vue-router";
import { getTokenAUTH } from "@/utils/auth";
import { ElNotification } from "element-plus";

import home from "../views/home.vue";
import login from "../views/auth/login.vue";
// 用户管理
import userList from "../views/userManage/userList.vue";
import chargeList from "../views/userManage/chargeList.vue";
import UsdtChargeList from "../views/userManage/UsdtChargeList.vue";
import flowList from "../views/userManage/flowList.vue";
import yuebaoLogLists from "../views/userManage/yuebaoLogLists.vue";
import memberLevel from "../views/userManage/memberLevel.vue";
import teamLevel from "../views/userManage/teamLevel.vue";
import memberUpgrade from "../views/userManage/memberUpgrade.vue";
import realnameList from "../views/userManage/realnameList.vue";
import teamData from "../views/userManage/teamData.vue";
import userAddress from "../views/userManage/userAddress.vue";
import userTransfer from "../views/userManage/userTransfer.vue";
import userWallet from "../views/userManage/userWallet.vue";
import withdrawList from "../views/userManage/withdrawList.vue";
import transferLogLists from "../views/userManage/transferLogLists.vue";
import levelLogLists from "../views/userManage/levelLogLists.vue";
import moneyChangeList from "../views/userManage/moneyChangeList.vue";
import userLoginLog from "../views/userManage/userLoginLog.vue";
import teamLevelLogLists from "../views/userManage/teamLevelLogLists.vue";
import teamRewardLogLists from "../views/userManage/teamRewardLogLists.vue";

// 项目管理
import buyRecordList from "../views/projectManage/buyRecordList.vue";
import projectList from "../views/projectManage/projectList.vue";
import projectA from "../views/projectManage/projectA.vue";
import projectB from "../views/projectManage/projectB.vue";
import projectC from "../views/projectManage/projectC.vue";
import projectType from "../views/projectManage/projectType.vue";
import projectLogList from "../views/projectManage/projectLogList.vue";
import collectWords from "../views/projectManage/collectWords.vue";
import wordsLogLists from "../views/projectManage/wordsLogLists.vue";
import bigRaffleLists from "../views/projectManage/bigRaffleLists.vue";
import bigRaffleLogLists from "../views/projectManage/bigRaffleLogLists.vue";
import stockLists from "../views/projectManage/stockLists.vue";
import stockLogLists from "../views/projectManage/stockLogLists.vue";

// 商品管理
import goodsList from "../views/goodsManage/goodsList.vue";
import goodsRecords from "../views/goodsManage/goodsRecords.vue";
import goodsType from "../views/goodsManage/goodsType.vue";
import discountList from "../views/goodsManage/discountList.vue";

// 运营管理
import bannerList from "../views/operationManage/bannerList.vue";
import messageLists from "../views/operationManage/messageLists.vue";
import newsTypeList from "../views/operationManage/newsTypeList.vue";
import newsList from "../views/operationManage/newsList.vue";
import questionList from "../views/operationManage/questionList.vue";
import lottory from "../views/operationManage/lottory.vue";
import lottoryDetails from "../views/operationManage/lottoryDetails.vue";
import letters from "../views/operationManage/letters.vue";
import userFeedback from "../views/operationManage/userFeedback.vue";
import giftsList from "../views/operationManage/giftsList.vue";
import signInLists from "../views/operationManage/signInLists.vue";
import smsCodeLists from "../views/operationManage/smsCodeLists.vue";
import signRewardTypes from "../views/operationManage/signRewardTypes.vue";

// 系统设置
import paramsSetting from "../views/sysSetting/paramsSetting.vue";
import siteDesc from "../views/sysSetting/siteDesc.vue";
import googleKeys from "../views/sysSetting/googleKeys.vue";

// 支付模块
import rechargeType from "../views/payments/rechargeType.vue";
import paymentUpper from "../views/payments/paymentUpper.vue";
import paymentChannel from "../views/payments/paymentChannel.vue";
import paymentAccount from "../views/payments/paymentAccount.vue";
import bankList from "../views/payments/bankList.vue";

// 视频管理
import videoType from "../views/videoManage/videoType.vue";
import videoList from "../views/videoManage/videoList.vue";
import xuanchuanVideo from "../views/videoManage/xuanchuanVideo.vue";

// 后台管理
import backendUser from "../views/backendManage/backendUser.vue";
import loginLog from "../views/backendManage/loginLog.vue";

const routes = [
  {
    path: "/",
    redirect: "home",
  },
  {
    path: "/login",
    name: "login",
    component: login,
  },
  {
    path: "/home",
    name: "home",
    component: home,
  },
  {
    path: "/userList",
    name: "userList",
    component: userList,
  },
  {
    path: "/chargeList",
    name: "chargeList",
    component: chargeList,
  },
  {
    path: "/UsdtChargeList",
    name: "UsdtChargeList",
    component: UsdtChargeList,
  },
  {
    path: "/flowList",
    name: "flowList",
    component: flowList,
  },
  {
    path: "/yuebaoLogLists",
    name: "yuebaoLogLists",
    component: yuebaoLogLists,
  },
  {
    path: "/memberLevel",
    name: "memberLevel",
    component: memberLevel,
  },
  {
    path: "/memberUpgrade",
    name: "memberUpgrade",
    component: memberUpgrade,
  },
  {
    path: "/realnameList",
    name: "realnameList",
    component: realnameList,
  },
  {
    path: "/moneyChangeList",
    name: "moneyChangeList",
    component: moneyChangeList,
  },
  {
    path: "/userLoginLog",
    name: "userLoginLog",
    component: userLoginLog,
  },
  {
    path: "/teamLevelLogLists",
    name: "teamLevelLogLists",
    component: teamLevelLogLists,
  },
  {
    path: "/teamRewardLogLists",
    name: "teamRewardLogLists",
    component: teamRewardLogLists,
  },
  {
    path: "/teamData",
    name: "teamData",
    component: teamData,
  },
  {
    path: "/teamLevel",
    name: "teamLevel",
    component: teamLevel,
  },
  {
    path: "/userAddress",
    name: "userAddress",
    component: userAddress,
  },
  {
    path: "/userTransfer",
    name: "userTransfer",
    component: userTransfer,
  },
  {
    path: "/userWallet",
    name: "userWallet",
    component: userWallet,
  },
  {
    path: "/levelLogLists",
    name: "levelLogLists",
    component: levelLogLists,
  },
  {
    path: "/withdrawList",
    name: "withdrawList",
    component: withdrawList,
  },
  {
    path: "/transferLogLists",
    name: "transferLogLists",
    component: transferLogLists,
  },
  {
    path: "/buyRecordList",
    name: "buyRecordList",
    component: buyRecordList,
  },
  {
    path: "/projectList",
    name: "projectList",
    component: projectList,
  },
  {
    path: "/projectA",
    name: "projectA",
    component: projectA,
  },
  {
    path: "/projectB",
    name: "projectB",
    component: projectB,
  },
  {
    path: "/projectC",
    name: "projectC",
    component: projectC,
  },
  {
    path: "/projectType",
    name: "projectType",
    component: projectType,
  },
  {
    path: "/projectLogList",
    name: "projectLogList",
    component: projectLogList,
  },
  {
    path: "/collectWords",
    name: "collectWords",
    component: collectWords,
  },
  {
    path: "/wordsLogLists",
    name: "wordsLogLists",
    component: wordsLogLists,
  },
  {
    path: "/bigRaffleLists",
    name: "bigRaffleLists",
    component: bigRaffleLists,
  },
  {
    path: "/bigRaffleLogLists",
    name: "bigRaffleLogLists",
    component: bigRaffleLogLists,
  },
  {
    path: "/stockLogLists",
    name: "stockLogLists",
    component: stockLogLists,
  },
  {
    path: "/stockLists",
    name: "stockLists",
    component: stockLists,
  },
  {
    path: "/goodsList",
    name: "goodsList",
    component: goodsList,
  },
  {
    path: "/goodsRecords",
    name: "goodsRecords",
    component: goodsRecords,
  },
  {
    path: "/goodsType",
    name: "goodsType",
    component: goodsType,
  },
  {
    path: "/discountList",
    name: "discountList",
    component: discountList,
  },
  {
    path: "/bannerList",
    name: "bannerList",
    component: bannerList,
  },
  {
    path: "/messageLists",
    name: "messageLists",
    component: messageLists,
  },
  {
    path: "/newsTypeList",
    name: "newsTypeList",
    component: newsTypeList,
  },
  {
    path: "/newsList",
    name: "newsList",
    component: newsList,
  },
  {
    path: "/questionList",
    name: "questionList",
    component: questionList,
  },

  {
    path: "/lottory",
    name: "lottory",
    component: lottory,
  },
  {
    path: "/lottoryDetails",
    name: "lottoryDetails",
    component: lottoryDetails,
  },
  {
    path: "/letters",
    name: "letters",
    component: letters,
  },
  {
    path: "/userFeedback",
    name: "userFeedback",
    component: userFeedback,
  },
  {
    path: "/giftsList",
    name: "giftsList",
    component: giftsList,
  },
  {
    path: "/signInLists",
    name: "signInLists",
    component: signInLists,
  },
  {
    path: "/smsCodeLists",
    name: "smsCodeLists",
    component: smsCodeLists,
  },
  {
    path: "/signRewardTypes",
    name: "signRewardTypes",
    component: signRewardTypes,
  },
  {
    path: "/paramsSetting",
    name: "paramsSetting",
    component: paramsSetting,
  },
  {
    path: "/siteDesc",
    name: "siteDesc",
    component: siteDesc,
  },
  {
    path: "/rechargeType",
    name: "rechargeType",
    component: rechargeType,
  },
  {
    path: "/paymentUpper",
    name: "paymentUpper",
    component: paymentUpper,
  },
  {
    path: "/paymentChannel",
    name: "paymentChannel",
    component: paymentChannel,
  },
  {
    path: "/paymentAccount",
    name: "paymentAccount",
    component: paymentAccount,
  },
  {
    path: "/bankList",
    name: "bankList",
    component: bankList,
  },
  {
    path: "/googleKeys",
    name: "googleKeys",
    component: googleKeys,
  },
  {
    path: "/videoType",
    name: "videoType",
    component: videoType,
  },
  {
    path: "/videoList",
    name: "videoList",
    component: videoList,
  },
  {
    path: "/xuanchuanVideo",
    name: "xuanchuanVideo",
    component: xuanchuanVideo,
  },
  {
    path: "/backendUser",
    name: "backendUser",
    component: backendUser,
  },
  {
    path: "/loginLog",
    name: "loginLog",
    component: loginLog,
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.path == "/login") {
    next();
  } else {
    if (!getTokenAUTH()) {
      ElNotification({
        title: "Error",
        message: "您还没有登录，请先登录",
        type: "error",
      });
      next("/login");
    } else {
      next();
    }
  }
});

export default router;
