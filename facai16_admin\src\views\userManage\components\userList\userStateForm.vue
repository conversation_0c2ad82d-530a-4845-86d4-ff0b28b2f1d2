<template>
    <el-form label-width="120px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="用户名ID" required >
            <el-input v-model="form.uid" disabled clearable  />
        </el-form-item>
        <el-form-item label="用户名" required >
            <el-input v-model="form.username" disabled clearable  />
        </el-form-item>
        
        <el-form-item label="禁止购买" required>
            <el-select v-model="form.ban_buy" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止签到" required>
            <el-select v-model="form.ban_sigin" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止抽奖" required>
            <el-select v-model="form.ban_raffle" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止登入" required>
            <el-select v-model="form.ban_login" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止邀请" required>
            <el-select v-model="form.ban_invite" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止充值" required>
            <el-select v-model="form.ban_recharge" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止提款" required>
            <el-select v-model="form.ban_withdraw" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="禁止商品兑换" required>
            <el-select v-model="form.ban_exchange" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="身份证认证" required>
            <el-select v-model="form.sfz_status" placeholder="" clearable>
                <el-option v-for="item in userlistSfzStatusEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="踢用户下线" required>
            <el-select v-model="form.kick_out" placeholder="" clearable>
                <el-option v-for="item in limitEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="是否测试账号" required>
            <el-select v-model="form.is_test" placeholder="" clearable>
                <el-option v-for="item in accountTypeEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="是否有效账号" required>
            <el-select v-model="form.is_valid_user" placeholder="" clearable>
                <el-option v-for="item in accoutActiveEnums" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
       
    </el-form>
</template>

<script setup>
import { getCurrentInstance, nextTick, onMounted, ref } from 'vue'
import {limitEnums, accoutActiveEnums, accountTypeEnums, userlistSfzStatusEnums } from '@/config/enums'

const form = ref({
    uid: '',
    ban_buy: '',
    ban_sigin: '',
    ban_raffle: '',
    ban_login: '',
    ban_invite: '',
    ban_recharge: '',
    ban_withdraw: '',
    ban_exchange: '',
    sfz_status: '',
    kick_out: '',
    is_test: '',
    is_valid_user: '',
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form.value, props.item)
        getUserState()
    })
})

const { proxy } = getCurrentInstance()
const getUserState = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: 'user/getUserState?id=' + form.value.id
    })

    if (res.code == 0) {
        form.value = Object.assign(form.value, res.data)
    }
}

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 260px;
}

.demo-form-inline .el-select {
    --el-select-width: 260px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
/deep/  .el-form-item {
    align-items: flex-start;
}
</style>