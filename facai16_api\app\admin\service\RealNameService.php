<?php
namespace app\admin\service;

use app\common\jobs\RegisterGiftJob;
use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\RealNameRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserBankRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;
use think\facade\Request;


/**
 * 实名列表
 */
class RealNameService
{
    public function getRealNameLists($params): array
    {
        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }


        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }


        if (is_numeric($params['status']))
        {
            $where[] = ['status', '=', $params['status']];
        }


        $RealNameRepo = new RealNameRepository();
        $data        = $RealNameRepo->paginates($where);

        return Result::success($data);
    }

    public function getRealNameInfo($id): array
    {
        $RealNameRepo = new RealNameRepository();
        $data         = $RealNameRepo->findById($id);

        return Result::success($data);
    }


    /**
     * 实名审核
     * @param $id
     * @param $status
     * @return array
     */
    public function updateRealName($id, $status): array
    {

        $RealNameRepo = new RealNameRepository();
        $info         = $RealNameRepo->findById($id);

        if (empty($info))
        {
            return Result::fail('没有数据');
        }


        Db::startTrans();

        try {

            $update = [
                'status'        => $status,
                'update_time'   => time()
            ];


            $res   = $RealNameRepo->updateById($id, $update);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            //实名审核
            if ($status == 0)
            {

                $update = [
                    'sfz_status'  => 0,
                    'update_time' => time()
                ];

                $UserStateRepo  = new UserStateRepository();
                $res            = $UserStateRepo->updateByCondition(['uid' => $info['uid']], $update);


                if (!$res)
                {
                    Db::rollback();
                    return Result::fail('保存失败');
                }


                $update = [
                    'sfz_name'      => $info['sfz_name'],
                    'sfz_number'    => $info['sfz_number'],
                    'update_time'   => time()
                ];


                $UserRepo = new UserRepository();
                $res      = $UserRepo->updateById($info['uid'], $update);


                if (!$res)
                {
                    Db::rollback();
                    return Result::fail('保存失败');
                }

//
//                $SystemSetRepo  = new SystemSetRepository();
//                $raffle         = $SystemSetRepo->valueByCondition(['key' => 'realname_raffle'],'val');
//
//                if ($raffle)
//                {
//                    $res = $MoneyLogRepo->raffle($info['uid'], $raffle,MoneyClass::RAFFLE_ADD, 'realname',"实名认证赠送抽奖:{$raffle}次");
//
//                    if ($res['code'])
//                    {
//                        Db::rollback();
//                        return Result::fail($res['msg']);
//                    }
//                }
//
//
//                $SystemSetRepo  = new SystemSetRepository();
//                $gift           = $SystemSetRepo->valueByCondition(['key' => 'invite_gift'],'val');
//
//                if ($gift)
//                {
//                    $res          = $MoneyLogRepo->coupon($info['uid'], $gift,MoneyClass::COUPON_ADD, $id,'实名送执行卡'. $gift);
//
//                    if ($res['code'])
//                    {
//                        Db::rollback();
//                        return Result::fail('保存失败');
//                    }
//                }

            }



            Db::commit();


            queue(RegisterGiftJob::class, ['uid' =>$info['uid']]);



        } catch (\Exception $exception)
        {

            Db::rollback();

            //注册失败
            return Result::fail(Record::exception('admin', $exception,'LoginService->register'));
        }


        return Result::success();
    }
}
