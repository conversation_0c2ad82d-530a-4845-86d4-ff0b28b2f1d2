<template>
	<view>
		<view class="page-bg flex-page scroll-view">
			<view class="px-32 py-20">
				<top-status-bar></top-status-bar>
				<view class="fc-bet ">
					<view class="flex1 h-90 fc">
						<image src="/static/index/logo.png" mode="aspectFit" class="w-100 h-72 block mr-20"></image>
						<text class="fm color-#407CEE t-48 lh-63">
							Mistral AI
						</text>
					</view>
				</view>
			</view>
			<view class=" f-pr">
				<view class="f-pr z-2">
					<image src="/static/cash/kbg.png" mode="widthFix" class="w-750 h-300 block f-pr"></image>
					<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
						<view class="flex pt-60 mb-40 justify-between items-center">
							<view class="t-25 lh-40 fw-600">
								注册赠送股权
							</view>
							<view>
								<image src="/static/project/gqzs.png" mode="aspectFit" class="block w-146 h-51"></image>
							</view>
						</view>
						<view class="fc-bet">
							<view>
								<view class="t-22 lh-30">
									当前持有股权
								</view>
								<view class="DIN fw-600 t-44 lh-51">
									100
								</view>
							</view>
							<view>
								<view class="t-22 lh-30">
									每日释放
								</view>
								<view class="DIN fw-600 t-44 lh-51">
									2%
								</view>
							</view>
							<view>
								<view class="t-22 lh-30">
									项目周期(天)
								</view>
								<view class="DIN fw-600 t-44 lh-51">
									365
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="f-pr mx-32 bg-#407CEE bg-op-20 p-24 t-22 lh-30 !pt-77 r-20 mt-[-90rpx]">
					<view>
						<text class="color-#fff">股权说明:</text>
						<text class="color-#ddd">
							注册即赠送100股权，每日释放2.00元，连续释放365天，释放周期结束，等公司上市会按照上市当日价格凭股权证书回购所有股权。以上信息最终解释权为米斯特拉尔&nbsp;(Mistral&nbsp;Al)&nbsp;所有
						</text>
					</view>
				</view>
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30 overflow-y-hidden">
				<ProjectList type="1" ph='350px' ref="projectList" />
			</view>
			<view>
				<foot-nav-bar :fixed="true" index="1"></foot-nav-bar>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import ProjectList from "@/pages/shareholding/project-list.vue"
	export default {
		components: {
			ProjectList
		},
		data() {
			return {};
		},
		onShow() {
			this.$nextTick(() => {
				this.$refs.projectList.getRecord()
			})
		},
		methods: {

		},

	};
</script>
<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #000511;
		background-size: 100% auto;
	}
</style>