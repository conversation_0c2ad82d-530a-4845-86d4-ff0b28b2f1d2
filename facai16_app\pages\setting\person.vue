<template>
	<view>
		<view class="scroll-view page-bg">
			<view class="flex-page  scroll-view">
				<view class="flex-scroll-fixed ">


					<uv-navbar bgColor="none" @leftClick="back" fixed placeholder>
						<template #left>
							<view>
								<image src="/static/back.png" mode="aspectFit" class="size-48 block"></image>
							</view>
						</template>
						<template #center>
							<view class="color-#fff">
								个人设置
							</view>
						</template>
					</uv-navbar>

				</view>


				<view class="flex1 f-pr color-#fff">
					<scroll-view scroll-y="true" class="scroll-view ">


						<view class="p-32">
							<box-box :blue="true">
								<view class="p-60 fcc-h">
									<view class="size-124 r-100" @click="clk">
										<image :src="avatorPic" mode="aspectFill" class="size-full block r-100"></image>

									</view>
									<view class="color-#fff t-40 lh-56 mt-32 fcc">
										<text>
											{{eye ? userInfo.phone : userInfo.phone2}}
										</text>
										<view @click="eye = !eye" class="ml-16">
											<image src="/static/eye1.png" mode="aspectFit" class="block size-40"
												v-if="eye"></image>
											<image src="/static/eye2.png" mode="aspectFit" class="block size-40" v-else>
											</image>
										</view>
									</view>
								</view>
							</box-box>
							<view class="h-32">

							</view>
							<box-box :blue="true">
								<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
									<view class="fc-bet h-106">
										<view class="w-180">
											<view class=" ">
												在职岗位
											</view>
										</view>
										<view class=" fc-bet">
											<view>
												{{userInfo.level_name}}
											</view>

										</view>
									</view>
								</view>
							</box-box>


							<view class="h-32">

							</view>
							<box-box :blue="true">
								<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
									<navigator url="/pages/login/change-password">
										<view class="fc-bet h-106">
											<view class="w-380">
												<view class=" ">
													修改登录密码
												</view>
											</view>
											<view class=" fc-bet">
												<view>
													<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
												</view>

											</view>
										</view>
									</navigator>
								</view>
								<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
									<navigator url="/pages/login/change-safe-password">
										<view class="fc-bet h-106">
											<view class="w-380">
												<view class=" ">
													修改交易密码
												</view>
											</view>
											<view class=" fc-bet">
												<view>
													<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
												</view>

											</view>
										</view>
									</navigator>
								</view>
							</box-box>
							<view class="h-32"></view>
							<box-box :blue="true">
								<view class="px-32">
									<view class="t-32 color-#fff lh-45 pt-26  fc-bet pb-19">
										<text>
											收货地址
										</text>
									</view>
									<uv-gap height="1" bgColor="#3766C3" marginBottom="28rpx"></uv-gap>
									<view v-if='!addressInfo'>
										<no-data>暂无收货地址</no-data>
										<view class="btn-full fcc" @click="openUrl('/pages/address/add')">
											新增收货地址
										</view>
									</view>
									<view class="address-item px-30 py-40 flex" v-else>
										<!-- <view @click="checkedIndex = index">
											<view class="check" :class="{on:checkedIndex == index}"></view>
										</view> -->
										<view @click="checkedIndex = index" class="flex1">
											<view class="fc">
												<view class="name">
													{{userInfo.sfz_name}}
												</view>
												<view class="tel">
													{{userInfo.phone}}
												</view>
												<view class="default" v-if="addressInfo.default">
													默认
												</view>
											</view>
											<view class="p">
												{{addressInfo.address_place}}
											</view>
										</view>
										<view @click.stop=''>
											<view class="edit" @click="openUrl('/pages/address/edit')"></view>
										</view>
									</view>
									<view class="h-20"></view>
								</view>
							</box-box>

							<!-- 	<box-box :blue="true">
								<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
									<view class="fc-bet h-106">
										<view class="w-380">
											<view class=" ">
												软件更新
											</view>
										</view>
										<view class=" fc">
											<text class="mr-15">V1.0.0</text>
											<view>
												
												<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
											</view>
											
										</view>
									</view>
								</view>
								<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
									<view class="fc-bet h-106">
										<view class="w-380">
											<view class=" ">
												清除缓存
											</view>
										</view>
										<view class=" fc-bet">
											<view>
												<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
											</view>
											
										</view>
									</view>
								</view>
								
								<view class="mx-32 t-32 lh-45  ">
									<view class="fc-bet h-106">
										<view class="w-380">
											<view class=" ">
												隐私协议&用户协议
											</view>
										</view>
										<view class=" fc-bet">
											<view>
												<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
											</view>
											
										</view>
									</view>
								</view>
							</box-box>	 -->

						</view>
						<view class="h-30"></view>
						<view class="btn-area"></view>
					</scroll-view>
				</view>
			</view>
		</view>
		<yq-avatar @upload="myUpload" ref="avatar"></yq-avatar>
		<uv-modal confirm-color="#222" cancelColor="#999" ref="modal" title="标题名称" showCancelButton @confirm="confirm">
			<uv-textarea border="none" bgColor="red" v-model="textareaValue" count maxlength="15"
				placeholder="请输入内容"></uv-textarea>
		</uv-modal>
	</view>
</template>


<script>
	import moment from 'moment';
	import {
		mapState
	} from 'vuex'
	import {
		needAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [needAuth],
		data() {
			return {
				avatorPic: "/static/my/tx.png",
				nickname: "135****3789",
				textareaValue: "",
				birthday: null,
				eye: false,
				sexVal: '',
				sexList: [
					'男',
					'女',
				]
			};
		},
		computed: {
			...mapState(['userInfo', 'addressInfo']),
		},
		methods: {
			moment,
			sexSelect(e) {

			},
			openModal() {
				this.$refs.modal.open();
			},
			confirm() {
				this.nickname = this.textareaValue
				console.log('点击确认按钮');
			},
			birthdayConfirm(e) {
				this.birthday = e

				console.log("e", e.value)
			},
			clk() {

				this.$refs.avatar.fChooseImg(0, {
					selWidth: "500upx",
					selHeight: "500upx",
					expWidth: '500upx',
					expHeight: '500upx'
				});

				this.$refs.avatar.openUploadPopup()

			},

			myUpload(rsp) {
				this.avatorPic = rsp.path
				console.log("头像", rsp.path)
			},
		},
	}
</script>
<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) center 0 no-repeat #022044;
		background-size: 100% auto;
	}

	.address-item {
		background: #fff;
		position: relative;
		border-radius: 16px;

		.check {
			width: 40rpx;
			height: 40rpx;
			background: url(/static/check-1-g.png) center no-repeat;
			background-size: contain;
			margin-right: 8px;

			&.on {
				background-image: url(/static/check1-on.png);
			}
		}

		.edit {
			margin-left: 15px;
			width: 40rpx;
			height: 40rpx;
			background: url(/static/pen.png) center no-repeat;
			background-size: contain;
		}

		.p {
			font-weight: 400;
			font-size: 26rpx;
			color: #222222;
			line-height: 37rpx;

			margin-top: 8rpx;
		}

		.name {
			font-weight: 600;
			font-size: 30rpx;
			color: #222222;
			line-height: 42rpx;
			margin-right: 24rpx;
		}

		.tel {
			font-weight: 400;
			font-size: 30rpx;
			color: #222222;
			line-height: 42rpx;
			margin-right: 10rpx;
		}

		.default {
			padding: 0 8rpx;
			height: 38rpx;
			background: #407CEE;
			border-radius: 4rpx;
			font-weight: 400;
			font-size: 26rpx;
			color: #FFFFFF;
			line-height: 38rpx;
		}
	}



	.address-input-row {
		background: #fff;
		position: relative;

		.left {
			min-width: 168rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #222222;
			line-height: 45rpx;
		}

		input {
			font-weight: 400;
			font-size: 30rpx;
			color: #222;
			line-height: 80rpx;
			height: 80rpx;
			display: block;
			width: 95%;
		}

		&:after {
			content: "";
			display: block;
			left: 30rpx;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #E6E7EB;
			position: absolute;
		}
	}



	.address-input-row:nth-last-of-type(1) {
		&:after {
			display: none;
		}
	}
</style>