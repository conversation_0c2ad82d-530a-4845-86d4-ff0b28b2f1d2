<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							最新公告
						</view>
					</template>
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				<scroll-view @scrolltolower="scrollHandle" :refresher-enabled="true"
					:refresher-triggered="refresherTriggered" @refresherrefresh='onRefresh' scroll-y="true"
					refresher-background='#ffffff00' style="height: calc(100vh - 60px);">
					<view class="pt-32">
						<view class="bg-#fff r-20 p-24 !pb-0 mb-24" v-for="item in list" :key="item.id">
							<view class="c2 t-32 lh-45 ddd mb-12 fw-600">
								{{item.title}}
							</view>
							<view class="c6 t-24 lh-33 mb-12">
								{{item.create_at}}
							</view>
							<view class="c6 t-26 lh-37 mb-16">
								{{ htmlDecodeByRegExpHandle(item.content) }}
							</view>
							<uv-gap bgColor="#EEEEEE" height="1"></uv-gap>
							<!-- <view class="h-73 fc-bet">
								<view class="color t-26 lh-37">
									查看详情
								</view>
								<view>
									<uv-icon name="arrow-right" size="26rpx" color="#407CEE"></uv-icon>
								</view>
							</view> -->
						</view>
					</view>
					<no-data v-if="list.length==0 && loadStatus=='nomore'"></no-data>
					<uv-load-more :status="loadStatus" v-show="list.length!=0" color="#fff" lineColor="#fff" line
						@loadmore="scrollHandle" />
					<view class="btn-area"></view>
				</scroll-view>
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import {
		getArticle
	} from '@/api/home.js'
	import {
		htmlDecodeByRegExp
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				loadStatus: 'loadmore',
				page: 0,
				list: [],
				refresherTriggered: false,
			};
		},
		onShow() {
			this.getRecord()
		},
		methods: {
			htmlDecodeByRegExpHandle(content) {
				return htmlDecodeByRegExp(content).replace(
					"<img",
					'<img  mode="widthFix"'
				)
			},
			scrollHandle(e) {
				if (this.loadStatus == 'loading') {
					this.getRecord()
				}
			},
			async onRefresh() {
				if (this._freshing) return;
				await this.getRecord(1)
			},
			async getRecord(setPage = 0) {
				try {
					this.loadStatus = 'loading'
					if (setPage) {
						this.page = setPage
					} else {
						this.page++
					}
					const resData = await getArticle({
						page: this.page,
						class_id: '4',
					})
					if (resData.code == 0) {
						if (this.page == 1) {
							this.list = resData.data.data
						} else {
							this.list = [...this.list, ...resData.data.data]
						}
						if (resData.data.data.length < 10) {
							this.loadStatus = 'nomore'
						} else {
							this.loadStatus = 'loadmore'
						}
					} else {
						this.loadStatus = 'loadmore'
					}
				} catch (e) {
					this.page--;
					this.loadStatus = 'loadmore'
				} finally {
					this._freshing = true;
					this.refresherTriggered = true;
					this.$nextTick(() => {
						this._freshing = false;
						this.refresherTriggered = false;
						this.$forceUpdate?.();
					});
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>