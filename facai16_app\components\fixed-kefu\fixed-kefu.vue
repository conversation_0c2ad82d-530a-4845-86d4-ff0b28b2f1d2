<template>
	<view>
		<view class="fixed-kefu" @click="toCustom">
			<view class="f-pr">
				<view class="txt fcc">
					在线客服
				</view>
				<image src="/static/index/fix-kefu.png" mode="aspectFit" class="w-104 h-92 block f-pr "></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "fixed-kefu",
		methods: {
			toCustom() {
				uni.navigateTo({
					url: '/pages/index/custom'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.fixed-kefu {
		position: fixed;
		right: 27rpx;
		bottom: 20%;
		z-index: 999;

		.txt {
			width: 128rpx;
			height: 47rpx;
			background: #407CEE;
			box-shadow: 0px 6rpx 24rpx 0px rgba(74, 120, 255, 0.51);
			border-radius: 24px;
			position: absolute;

			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;

			top: 82rpx;
			left: -12rpx;
		}
	}
</style>