<?php
namespace app\common\repository;
use app\common\model\UserInfo;
use app\common\utils\Result;
use think\facade\Db;

/**
 * 用户信息
 */
class UserInfoRepository extends UserInfo
{
    use BaseRepository;


    /**
     * 统计
     * @param int $uid
     * @param array $params
     * @return array
     */
    public function statistic(int $uid, array $params): array
    {
        $user              = $this->findByCondition(['uid' => $uid],'*', [],true,true);

        if(empty($user))
        {
            return Result::fail('没有账号信息');
        }

        $jump   = ['uid','update_time','update_at','create_time','create_at'];

        $keys   = array_keys($user);

        $update = [
            'update_time' => time(),
            'update_at'   => date('Y-m-d H:i:s', time())
        ];

        foreach ($params as $key => $val)
        {

            if (in_array($key, $jump) || !in_array($key, $keys) || !is_numeric($val) || $val == 0)
            {
                continue;
            }

            $update[$key] = Db::raw('`' . $key . '` + ' . $val);

        }

        $res = $this->updateByCondition(['uid' => $uid], $update);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }

        return Result::success();
    }



    public function totalRechargeUsers($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->count('in.uid');
    }

    /**
     * 累积投资
     * @param $star
     * @param $end
     * @param $isTest
     * @param $members
     * @return mixed
     */
    public function totalItemMoney($star,$end,$isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->sum('in.invest_money');
    }


    /**
     * 累积投资
     * @param $star
     * @param $end
     * @param $isTest
     * @param $members
     * @return mixed
     */
    public function totalEarnMoney($star,$end,$isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];

        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        $result = $this->alias('in')
            ->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->field([
                'SUM(in.team_invite) as team_invite',
                'SUM(in.team_item) as team_item',
            ])
            ->find();

        // 分别获取三个值
        return $result['team_invite'] + $result['team_item'];

    }

    public function totalInvestUsers($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.invest_money', '>', 0];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->count('in.uid');
    }



    public function totalRaffleMoney($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->sum('in.raffle_money');
    }


    public function totalSignInUsers($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.signin_num', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->count('in.uid');
    }



    public function totalRecharge($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];


        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->sum('in.recharge_money');
    }


    /**
     * 累积提现
     * @param $star
     * @param $end
     * @param $isTest
     * @param $members
     * @return mixed
     */
    public function totalWithdraw($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];


        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->sum('in.withdraw_money');
    }

    public function firstRecharge($star, $end, $isTest = '', $members = [])
    {
        $where                           = [];
        $where[]                         = ['in.recharge_money', '>', 0];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
            ->where($where)
            ->count('in.uid');
    }

}
