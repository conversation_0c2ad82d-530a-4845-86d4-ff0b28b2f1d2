<?php
namespace app\admin\controller;

use app\admin\service\MoneyService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 钱模块
 */
class Money
{

    /**
     * 账变列表
     * @return Json
     */
    public function getMoneyLists(): J<PERSON>
    {
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->getMoneyLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 添加金额
     * @return Json
     */
    public function addMoney(): Json
    {
        $param          = Request::param();
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->addMoney($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 账变分类
     * @return mixed
     */
    public function getMoneyClassLists(): J<PERSON>
    {
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->getMoneyClassLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 账变分类信息
     * @return Json
     */
    public function getMoneyClassInfo(): Json
    {
        $id             = Request::param('id',0);
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->getMoneyClassInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加账变分信息
     * @return Json
     */
    public function addMoneyClass(): Json
    {
        $param          = Request::param();
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->addMoneyClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新分类
     * @return Json
     */
    public function updateMoneyClass(): Json
    {
        $param          = Request::param();
        $MoneyService   = new MoneyService();
        $data           = $MoneyService->updateMoneyClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}