<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * EggPay
 */
class EggPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'EggPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $param = [
            'mchId'         => $this->config['mch_id'],
            'productId'     => $data['channel'],
            'mchOrderNo'    => $data['orderNo'],
            'amount'        => $data['money'] * 100,
            'currency'      => 'cny',
            'notifyUrl'     => CompleteRequest('/api/callback/' . self::CHANNEL),
            'subject'       => $data['orderNo'],
            'body'          => $data['orderNo'],
            'reqTime'       => date('YmdHis') . '000',
            'version'       => '1.0',
        ];

        // {"retCode":"0","sign":"E70EDA05B6456F505BD0BBFA7486CDE4","payOrderId":"P01202504121120031441808","payMethod":"formJump","payUrl":"http://pay.shengningkj.cn/api/hmpay/pay_wap_wudi2?payOrderId=K01202504121120032289903","payJumpUrl":"http://pay.shengningkj.cn/api/hmpay/pay_wap_wudi2?payOrderId=K01202504121120032289903"}

        $param['sign']  = $this->signature($param);

        $result         = curlPost($this->config['url'], $param);

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result         = json_decode($result, true);


        $uri            = $result['payUrl'] ?? '';

        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"income":"100","payOrderId":"P01202504121120475241841","amount":"50000","mchId":"20000083","productId":"2004","mchOrderNo":"PAYVBHEEECIAEHGHHJ","paySuccTime":"1744428160000","sign":"0D4D3262AE4D09539068E27C12EE23DC","channelOrderNo":"","backType":"2","reqTime":"20250412112239","param1":"","param2":"","appId":"","status":"2"}';
//        $param = json_decode($param,true);

        $sign      = $param['sign'];

        $param     = Arrays::withOut($param,['sign', 'param1', 'param2', 'appId','channelOrderNo']);

        $signature = $this->signature($param);

        if ($signature != $sign)
        {

            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }

        $orderId  = $param['mchOrderNo'] ?? '';


        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {

            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        //键名从低到高进行排序
        ksort($params);

        $post_url = '';

        foreach ($params as $key=>$value)
        {
            $post_url .= $key . '=' . $value.'&';
        }

        $stringSignTemp = $post_url . 'key=' . $this->config['sign'];

        $sign           = md5($stringSignTemp);

        return strtoupper($sign);
    }

}