<?php

namespace app\common\utils;

/**
 * 订单类
 * Class Order
 * @package app\common\utils
 */
class Order
{

   /*
    * 唯一订单号
    */
    public static function uniqueNo($pre = ''): string
    {
        $uniqueStr = md5($pre . uniqid() . rand(1,9999));
        return strtoupper($pre . substr($uniqueStr, 0, 16));
    }


    /**
     * 订单解密
     * @param string $sString
     * @return float|int|string
     */
    public static function decode(string $sString)
    {
        //解密
        $aData = explode("V", $sString);

        if (!isset($aData[1]))
        {
            return 0;
        }

        for ($i = 0; $i < strlen($aData[1]); $i++)
        {
            $ascii = ord($aData[1][$i]);
            if ($ascii > 74)
            {
                $ascii = 42;
            }
            else
            {
                $ascii -= 17;
            }
            $aData[1][$i] = chr($ascii);
        }

        $aData[1] = str_replace("*", "", $aData[1]);

        return is_numeric($aData[1]) ? $aData[1] : 0;
    }

    /**
     * 加密
     * @param string $sString
     * @return array|string
     */
    public static function encode(string $sString)
    {
        //加密
        $tmp   = explode("-", $sString);
        $tmp2  = array_pop($tmp);
        $aData = [implode('-', $tmp), $tmp2];

        if (!isset($aData[1]))
        {
            return $aData;
        }

        for ($i = strlen($aData[1]); $i <= 8; $i++)
        {
            $aData[1] .= "*";
        }

        $zero = 0;

        for ($i = 0; $i < strlen($aData[1]); $i++)
        {
            $ascii = ord($aData[1][$i]);

            if ($ascii == 42)
            {
                $ascii += $zero + 33;
                $zero++;
            }
            else
            {
                $ascii += 17;
            }

            $aData[1][$i] = chr($ascii);
        }

        return $aData[0] . "V" . $aData[1];
    }
}