<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="标题" required>
      <el-input v-model="form.title" clearable />
    </el-form-item>
    <el-form-item label="金额" required>
      <el-input v-model="form.money" clearable />
    </el-form-item>
    <el-form-item label="集字" required>
      <el-input v-model="form.words" clearable />
    </el-form-item>
    <el-form-item label="加息" required>
      <el-input v-model="form.rate" clearable />
    </el-form-item>
    <el-form-item
      label="奖品类型"
      prop="type"
      :rules="[
        {
          required: true,
          message: '请选择奖品类型',
          trigger: ['change'],
        },
      ]"
    >
      <el-select v-model="form.type" placeholder="文章类型" clearable>
        <el-option
          v-for="item in lottoryTypeBEnums"
          :label="item.label"
          :key="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="概率" required>
      <el-input v-model="form.chance" clearable />
    </el-form-item>

    <el-form-item
      label="图片"
      prop="img"
      :rules="[{ required: true, message: '请上传图片' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="successUpload"
        :on-error="handleErr"
        :multiple="false"
      >
        <img v-if="form.img" :src="proxy.IMG_BASE_URL + form.img" width="100%" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { lottoryTypeBEnums, rolesEnums } from "@/config/enums";
import { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from "vue";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  money: "",
  words: "",
  type: "",
  chance: "",
  img: "",
});
const props = defineProps(["item"]);

const successUpload = (res) => {
  form.value.img = res.data.url;
};

const handleErr = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    form.value = Object.assign(form.value, props.item);
  });
});

const { proxy } = getCurrentInstance()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref("<p>hello</p>");
const mode = ref("default");

const toolbarConfig = {};
const editorConfig =  {
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024, // 10M
      server: proxy.BASE_API_URL + "index/uploadX",
      headers: {
        "Accept-Token": getTokenAUTH(),
      },
      customInsert(res, insertFn) {
        const url = proxy.IMG_BASE_URL + res.data.url;
        const alt = res.data.alt
        const href = res.data.href
        insertFn(url, alt, href);
      },
    },
  },
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}

.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/deep/ .el-radio-group {
  width: 220px;
}

.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}

/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
