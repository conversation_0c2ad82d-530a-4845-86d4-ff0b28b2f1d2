<template>
	<view>
		<view class=" page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="#F6F9FC">
					<template #left>
						<view>
							<image src="/static/back-g.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="c2">
							购买详情
						</view>
					</template>
					
				</uv-navbar>
			</view>
			<view class="flex1 f-pr">
				
				<view class="fcc-h">
					<view class="pt-89 c9 t-32 lh-45 mb-16">
						支付金额
					</view>
					<view class="lh-93 t-40 c2 mb-70">
						<text class="DIN t-80 fw-600">308.00</text><text class="fw-500">USDT</text>
					</view>
					
					
				</view>
					
				<view class="mx-54 fc-bet mb-68">
					<view class="c9 t-32 lh-45">
						购买数量
					</view>
					<view class="fc">
						<uv-number-box v-model="value" :border="false" bgColor="#F6F9FC" class="!b-0" max="99">
								<template v-slot:minus>
									<view class=" size-64 fcc bg-#407CEE r-5">
										<uv-icon name="minus"  color="#FFFFFF" size="12">
										</uv-icon>
									</view>
								</template>
								<template v-slot:input>
									<input type="number"  v-model="value"  class="w-122 h-64 lh-64  text-center !b-0 bg-#F6F9FC t-48 c2"/>
								</template>
								<template v-slot:plus>
									<view class=" size-64 fcc bg-#407CEE r-5">
										<uv-icon name="plus" color="#FFFFFF" size="12">
										</uv-icon>
									</view>
								</template>
							</uv-number-box>
					</view>
				</view>	
				
				<view class="mx-32 border-1 border-solid border-color-#407CEE p-8 r-100 flex t-28 lh-40 color lh-68">
					<view class="flex1 fcc r-100" :class="{'bg-#407CEE color-#fff' : tabIndex ==0}" @click="tabIndex = 0">
						现金券
					</view>
					<view class="flex1 fcc r-100" :class="{'bg-#407CEE color-#fff' : tabIndex ==1}" @click="tabIndex = 1">
						加息券
					</view>
					<view class="flex1 fcc r-100" :class="{'bg-#407CEE color-#fff' : tabIndex ==2}" @click="tabIndex = 2">
						折扣券
					</view>
				</view>
				
				<view class="p-12">
					<view  v-if="tabIndex==0">
						<view class="f-pr" v-for="(item,index) in 10">
							<image src="/static/details/quanbg.png" mode="aspectFit" class="w-726 h-220 block"></image>
							<view class="f-pa w-686 h-180 top-20 left-20 flex">
								<view class="w-236 fcc-h color">
									<view class="t-28">
										<text>￥</text><text class="DIN t-80">0.01</text>
									</view>
									<view class="t-28">
										现金券
									</view>
								</view>
								<view class="flex1 pt-24">
									<view class="mb-4 t-32 lh-45 color-#070E38">
										通用满减券
									</view>
									<view class="mb-15 color-#83869B t-24 lh-33">
										满500元可用
									</view>
									<view class="t-22 lh-30 color-#B5B7C9">
										2023.04.10-2023.04.23
									</view>
								</view>
								<view class="w-178 fcc">
									<view class="w-128 h-60 bg-#407CEE r-90 t-24 color-#fff fcc" v-if="index % 2">
										立即使用
									</view>
									<image src="/static/cash/ysy.png" mode="aspectFit" class="block w-174 h-130" v-else></image>
								</view>
							</view>
						</view>
					</view>
					
					
					
					<view v-if="tabIndex==1">
						
						<view class="f-pr">
							<image src="/static/details/quanbg.png" mode="aspectFit" class="w-726 h-220 block"></image>
							<view class="f-pa w-686 h-180 top-20 left-20 flex">
								<view class="w-236 fcc-h color">
									<view class="t-28">
									<text class="DIN t-80">2%</text>
									</view>
									<view class="t-28">
										加息券
									</view>
								</view>
								<view class="flex1 pt-24">
									<view class="mb-4 t-32 lh-45 color-#070E38">
										通用满减券
									</view>
									<view class="mb-15 color-#83869B t-24 lh-33">
										满500元可用
									</view>
									<view class="t-22 lh-30 color-#B5B7C9">
										2023.04.10-2023.04.23
									</view>
								</view>
								<view class="w-178 fcc">
									<view class="w-128 h-60 bg-#407CEE r-90 t-24 color-#fff fcc" v-if="index % 2">
										立即使用
									</view>
									<image src="/static/cash/ysy.png" mode="aspectFit" class="block w-174 h-130" v-else></image>
								</view>
							</view>
						</view>
						
					</view>
					
					<view  v-if="tabIndex==2">
						
					
						<view class="f-pr">
							<image src="/static/details/quanbg.png" mode="aspectFit" class="w-726 h-220 block"></image>
							<view class="f-pa w-686 h-180 top-20 left-20 flex">
								<view class="w-236 fcc-h color">
									<view class="t-28">
										<text>￥</text><text class="DIN t-80">20</text>
									</view>
									<view class="t-28">
										折扣券
									</view>
								</view>
								<view class="flex1 pt-24">
									<view class="mb-4 t-32 lh-45 color-#070E38">
										通用满减券
									</view>
									<view class="mb-15 color-#83869B t-24 lh-33">
										满500元可用
									</view>
									<view class="t-22 lh-30 color-#B5B7C9">
										2023.04.10-2023.04.23
									</view>
								</view>
								<view class="w-178 fcc">
									<view class="w-128 h-60 bg-#407CEE r-90 t-24 color-#fff fcc">
										立即使用
									</view>
								</view>
							</view>
						</view>
					</view>
					
				</view>
				
				
				
			</view>
			
			<fixed-footer-view height="136rpx">
				<view class="bg-#fff color-#fff">
					<view class=" px-30 py-10">
						
						<view class="btn-full fcc">
							确认购买
						</view>
						
					</view>
					<view class="btn-area"></view>
				</view>
			</fixed-footer-view>
			
		</view>
		
		<!-- <fixed-kefu></fixed-kefu> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value:1,
				tabIndex:0
			};
		},
		
		methods: {
			
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		min-height: 100vh;
		background: #F6F9FC;
	}
</style>