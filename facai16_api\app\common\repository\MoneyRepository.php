<?php
namespace app\common\repository;
use app\common\model\Money;
use app\common\model\MoneyClass;
use app\common\utils\Result;


class MoneyRepository extends Money
{
    use BaseRepository;



    public function totalMoney($star, $end, $isTest, $members)
    {
        $where                           = [];
        $where[]                         = ['in.create_time', '>=', $star];
        $where[]                         = ['in.create_time', '<', $end];
        $members && $where[] = ['in.uid', 'in', $members];
        is_numeric($isTest) &&  $where[] = ['us.is_test', '=', $isTest];

            // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('in')->join('user_state us', 'in.uid = us.uid')
        ->where($where)
        ->sum('in.money');
    }


}
