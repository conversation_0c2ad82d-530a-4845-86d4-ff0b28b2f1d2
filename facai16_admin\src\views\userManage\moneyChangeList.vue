<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>
    </template>
    <template v-slot:query-button-right>
      <el-button
        type="primary"
        icon="CirclePlus"
        @click="editUser('add', {})"
        v-permission
        >新增</el-button
      >
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="详情" width="160" />
      <el-table-column prop="create_at" label="创建时间" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.role, roleEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="email" label="邮箱" width="130" />
      <el-table-column prop="invite" label="账号状态" width="130">
        <template #default="scope">
          {{ getLabelByVal(scope.row.clock, clocksEnums) }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="180" />
      <el-table-column prop="login_ip" label="登录ip" width="130" />
      <el-table-column prop="ip_address" label="登录ip地址" width="130" />
      <el-table-column prop="login_time" label="登录时间" width="130" />
      <el-table-column
        fixed="right"
        prop="address"
        v-if="currentUser.role == 0"
        width="200"
        label="操作"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            v-permission
            @click="editUser('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            v-permission
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="编辑/新增" width="1100">
      <editPop
        :key="editRow"
        ref="editFormRef"
        :formType="formType"
        :item="editRow"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  fundingTypesEnum,
  clocksEnums,
  getLabelByVal,
  roleEnums,
} from "@/config/enums";
import editPop from "./components/moneyChangeList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const searchForm = ref({
  username: "",
});

onMounted(() => {
  getList();
});

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const { proxy } = getCurrentInstance();
const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/MoneyLog/classes",
    params: {
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};

const editFormRef = ref(null);
const dialogFlag = ref(false);
const editRow = ref({});
const formType = ref("add");
const adminTableRef = ref(null);

const editUser = (type, row) => {
  formType.value = type;
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url =
    formType.value == "add"
      ? "SystemUser/addSystemUser"
      : "SystemUser/updateSystemUser";
  const data = editFormRef.value.form;
  if (formType.value == "add") {
    data.id = null;
  }
  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      username: data.username,
      password: data.password,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const deleteAction = (row) => {
  ElMessageBox.confirm("是否删除?", "Warning", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await proxy.$http({
        method: "get",
        url: "SystemUser/deleteSystemUser?id=" + row.id,
      });
      if (res.code == 0) {
        getList();
      }
      ElMessage({
        type: "success",
        message: res.msg,
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除",
      });
    });
};
</script>

<style lang="less" scoped></style>
