<template>
  <adminTable
    ref="adminTableRef"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    :tableLoading="tableLoading"
    :totalList="totalList"
    exportLink="payment/export"
    v-model:page="page"
    v-model:limit="limit"
    v-model:selectedIds="selectedIds"
    @search="searchList"
  >
    <template v-slot:form-inline-items>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.is_test"
          placeholder="账号类型"
          clearable
        >
          <el-option
            v-for="item in accountTypeEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.order_no"
          placeholder="订单编号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchForm.status" placeholder="状态" clearable>
          <el-option
            v-for="item in rechargeStatusEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- <template v-slot:query-button-right>
      <el-button type="primary" @click="multipleHandle(0)" v-permission
        >一键审批</el-button
      >
      <el-button type="danger" @click="multipleHandle(2)" v-permission
        >一键拒绝</el-button
      >
    </template> -->
    <template v-slot:table>
      <!-- <el-table-column
        type="selection"
        :selectable="(row) => row.status == 1"
        width="55"
      /> -->
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="130" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="class_name" label="充值类型" width="100" />
      <el-table-column prop="channel_name" label="支付名称" width="140" />
      <el-table-column prop="account_id" label="姓名" width="100" />
      <el-table-column prop="order_no" label="订单编号" width="200" />
      <el-table-column prop="amount" label="金额" width="100" />

      <el-table-column label="图片" width="180">
        <template #default="scope">
          {{ scope.row.img ? "" : "--" }}
          <el-image
            v-if="scope.row.img"
            class="previewImg"
            :preview-teleported="true"
            :src="proxy.IMG_BASE_URL + scope.row.img"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[proxy.IMG_BASE_URL + scope.row.img]"
            show-progress
            :initial-index="0"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="添加时间" width="180" />
      <!-- <el-table-column label="操作管理" width="160">
                <template #default="scope">
                    ({{ scope.row.admin_id }}) {{ scope.row.admin_name }}
                </template>
            </el-table-column> -->
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <span
            :class="{
              red: scope.row.status == 2,
              green: scope.row.status == 0,
            }"
            >{{ getLabelByVal(scope.row.status, rechargeStatusEnums) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        prop="address"
        v-if="currentUser.role == 0"
        width="200"
        label="操作"
      >
        <template #default="scope">
          <div class="table-handle-td">
            <el-button
              type="primary"
              v-permission
              @click="multipleHandle(0, [scope.row.id])"
              :disabled="scope.row.status != 1"
              >通过</el-button
            >
            <el-button
              type="danger"
              v-permission
              @click="multipleHandle(2, [scope.row.id])"
              :disabled="scope.row.status != 1"
              >拒绝</el-button
            >
            <!-- <el-icon
              @click="editUserInfo(scope.row)"
              :disabled="scope.row.status != 0"
            >
              <EditPen />
            </el-icon>
            <el-icon @click="deleteAction(scope.row)">
              <Delete />
            </el-icon> -->
          </div>
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="新增/编辑" width="1050">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import editPop from "./components/UsdtChargeList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getLabelByVal,
  rechargeStatusEnums,
  withdrawTypeEnums,
  accountTypeEnums,
} from "@/config/enums";
import { useRoute } from 'vue-router'

const route = useRoute()

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  // style: '0,1',
  username: "",
  phone: "",
  is_test: "",
  order_no: "",
  status: "",
  starttime: "",
  endtime: "",
  class_id: 4,
});
const tableData = ref([]);
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);

const { proxy } = getCurrentInstance();

onMounted(() => {
  if (route.query && route.query.phone) {
    searchForm.value.phone = route.query.phone
  }
  getList();
});

const selectedIds = ref([]);
const multipleHandle = (type, ids = []) => {
  if (ids.length > 0) {
    selectedIds.value = ids;
  }
  if (selectedIds.value == 0) {
    ElMessage({
      type: "error",
      message: "请选择需要操作的列表",
    });
    return;
  }
  ElMessageBox.prompt(`请输入${type == 0 ? "通过" : "拒绝"}理由`, "提示", {
    confirmButtonText: "提交",
    cancelButtonText: "取消",
  })
    .then(async ({ value }) => {
      const res = await proxy.$http({
        method: "post",
        url: "payment/userRecharge",
        data: {
          ids: selectedIds.value,
          status: type,
          remark: value,
        },
      });
      if (res.code == 0) {
        ElMessage({
          type: "success",
          message: `批量${type == 0 ? "通过" : "拒绝"}成功，${
            type == 0 ? "通过" : "拒绝"
          }理由是${value}`,
        });
        getList();
      } else {
        ElMessage({
          type: "error",
          message: res.msg,
        });
      }
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消操作！",
      });
    });
};

const searchList = () => {
  getList();
};

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/Payment/getPaymentLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped>
.table-handle-td {
  display: flex;
  gap: 5px;
  font-size: 20px;
  cursor: pointer;
}

.red {
  color: red;
}

.green {
  color: green;
}
</style>
