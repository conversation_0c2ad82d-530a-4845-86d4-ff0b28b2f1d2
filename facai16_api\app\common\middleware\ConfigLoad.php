<?php

namespace app\common\middleware;

use app\common\utils\File;
use Closure;
use think\facade\Config;
use think\Request;
use think\Response;

/**
 * 加载多余配置
 * Class AllowCross
 * @package app\common\middleware
 */
class ConfigLoad
{
    /**
     * 处理请求
     * @param  Request  $request
     * @param Closure $next
     * @return Response
     */
    public function handle( Request $request, Closure $next): Response
    {
        $settings = File::listFiles(config_path('more'));

        foreach ($settings as $val)
        {
            Config::load('more/'. $val['name'],  $val['name']);
        }


        return $next($request);
    }
}
