<?php
namespace app\common\repository;
use app\common\cache\OnlineUser;
use app\common\model\User;
use think\facade\Request;


class UserRepository extends User
{

    use BaseRepository;

    /**
     * 根据id生成邀请码
     * @description
     * @return string
     */
    public function createCode(): string
    {

        while (true)
        {
            $random  = rand(1000000,8000000);

            $where   = [];
            $where[] = ['invite', '=', $random];

            $user    = $this->findByCondition($where,'id');

            if (!$user)
            {
                break;
            }

            usleep(10000);
        }

        return $random;
    }

    /**
     * 获取用户信息
     * @param string $field
     * @param string $token
     * @return array|string
     */
    public function userByHeader(string $field = '*', string $token = '')
    {

        $token      = Request::header('Accept-Token', $token);

        if(empty($token))
        {
            return [];
        }

        $where   = [];

        $where[] = ['token', '=', $token];

        if ($field == '*')
        {
            $user    = $this->findByCondition($where, $field,[],true);
        }
        elseif (count(explode(',',$field)) > 1)
        {
            $user    = $this->findByCondition($where, $field, [],true);
        }
        else
        {
            $user    = $this->valueByCondition($where, $field, true);
        }

        if(empty($user))
        {
            return [];
        }
        else
        {

            $OnlineUser = new OnlineUser();

            if (is_array($user))
            {
                $OnlineUser->heartbeat($user['id']);
            }
            else
            {
                $OnlineUser->heartbeat($user);
            }

//            $this->updateById((int)$user['id'],['online_time' => time()]);
//            dd(2);
        }

        return $user;
    }

    /**
     * 用户信息
     * @param string $field
     * @return array
     */
    public function userInfoByHeader(string $field = '*'): array
    {
        $uid = $this->userByHeader('id');

        if(empty($uid))
        {
            return [];
        }

        $UserInfoRepo  = new UserInfoRepository();
        return  $UserInfoRepo->findByCondition(['uid' => $uid],$field);
    }


    /**
     * 用户状态
     * @param string $field
     * @return array
     */
    public function userStateByHeader(string $field = '*'): array
    {
        $uid = $this->userByHeader('id');

        if(empty($uid))
        {
            return [];
        }

        $UserStateRepo  = new UserStateRepository();
        return  $UserStateRepo->findByCondition(['uid' => $uid],$field);
    }



    /**
     * 用户钱包
     * @return array
     */
    public function userMoneyByHeader(): array
    {
        $uid = $this->userByHeader('id');

        if(empty($uid))
        {
            return [];
        }

        $MoneyRepository  = new MoneyRepository();
        return  $MoneyRepository->valueByCondition(['uid' => $uid],'money');
    }

    /**
     * 没有作业的客户
     * @return mixed
     */
    public function noRecharge(int $uid)
    {
        $filed = '
            u.id,
            u.username,
            u.phone,
            u.email,
            u.v1_id,
            u.v2_id,
            u.v1_name,
            u.v2_name,
            ui.*
        ';

        $UserRepo   = new UserRepository();
        $where      = [];
        $where[]    = ['u.v1_id|u.v2_id', '=', $uid];
        $where[]    = ['ui.invest_money', '=', 0];
        $data       = $UserRepo->alias('u')->join('user_info ui', 'u.id = ui.uid')
            ->where($where)
            ->field($filed)
            ->paginate()->toArray();
        return $data;
    }


    /**
     * count
     * @param $uid
     * @return mixed
     */
    public function  investNotFinish($uid)
    {
        $where      = [];
        $where[]    = ['i.invest_not_finish', '>', 0];
        $where[]    = ['u.v1_id', '=', $uid];

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        return $this->alias('u')->join('user_info i','u.id = i.uid')
            ->where($where)
            ->count('u.id');
    }

}
