<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							转账
						</view>
					</template>
					
				</uv-navbar>
				
				
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30">
				<scroll-view scroll-y="true" class="scroll-view">
					
					<view class="f-pr z-2">
						<image src="/static/my/kbg.png" mode="widthFix" class="w-750 h-330 block f-pr"></image>
						<view class="f-pa inset pl-70 pr-50 color-#fff t-22 lh-30">
							<view class="flex pt-60 mb-40 justify-between items-center">
								<view class="t-32 lh-45 fw-500 color-#fff">
									<view>
										账户余额
									</view>
									
								</view>
								<view>
									<image src="/static/my/zjmx.png" mode="aspectFit" class="block w-146 h-51"></image>
								</view>
							</view>
							
						</view>
						<view class="fc-bet  f-pa left-32 right-32 bottom-40 color-#fff px-32 py-12  !pb-48">
							<view class="flex1">
								<view class="t-24 lh-33 mb-7">
									USDT
								</view>
								<view class="DIN fw-600 t-58 lh-67">
									100.00
								</view>
							</view>
							<view class="flex1">
								<view class="t-24 lh-33 mb-7">
									RMB
								</view>
								<view class="DIN fw-600 t-58 lh-67">
									234556.00
								</view>
							</view>
						
						</view>
					</view>
					
					<view class="p-32 !pt-0">
						
						
						
						<box-box :blue="true">
							<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20" @click="$refs.popSelect.open()">
								<view class="fc-bet h-106">
									<view class="w-180">
										<view class=" ">
											货币切换
										</view>
									</view>
									<view class="flex1 fc-bet">
										<view>
											{{list[index]}}
										</view>
										<view>
											<uv-icon name="arrow-right" color="#fff" size="30rpx"></uv-icon>
										</view>
									</view>
								</view>
							</view>
							
							
							<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
								<view class="fc-bet h-106">
									<view class="w-180">
										<view class=" ">
											转账金额
										</view>
									</view>
									<view class="flex1 fc-bet">
										
										<input type="number" class="w-full h-106 lh-106" value="12345" placeholder="请输入" placeholder-style="color:#ccc">
										
									</view>
								</view>
							</view>
							
							<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
								<view class="fc-bet h-106">
									<view class="w-180">
										<view class=" ">
											对方账号
										</view>
									</view>
									<view class="flex1 fc-bet">
										
										<input type="text" class="w-full h-106 lh-106"  placeholder="请输入" placeholder-style="color:#ccc">
										
									</view>
								</view>
							</view>
							
							<view class="mx-32 t-32 lh-45  border-b border-b-solid border-white border-op-20">
								<view class="fc-bet h-106">
									<view class="w-180">
										<view class=" ">
											对方账号
										</view>
									</view>
									<view class="flex1 fc-bet">
										
										<input type="text" class="w-full h-106 lh-106"  placeholder="请输入对方姓名" placeholder-style="color:#ccc">
										
									</view>
								</view>
							</view>
							
							<view class="mx-32 t-32 lh-45   border-op-20">
								<view class="fc-bet h-106">
									<view class="w-180">
										<view class=" ">
											交易密码
										</view>
									</view>
									<view class="flex1 fc-bet">
										
										<input type="text" password class="w-full h-106 lh-106"  placeholder="请输入您的交易密码" placeholder-style="color:#ccc">
										
									</view>
								</view>
							</view>
							
							
							
						</box-box>
						<view class="h-32"></view>
						
						<box-box :blue="true">
							<view class="p-32">
								<view class="fc-bet mb-32">
									<image src="/static/cash/arrow-tit.png" mode="aspectFit" class="block w-216 h-33"></image>
									<view class="fcc t-30 color-#fff lh-40 px-20">
										温馨提示
									</view>
									<image src="/static/cash/arrow-tit.png" mode="aspectFit" class="block w-216 h-33 rotate-180deg"></image>
								</view>
								<view class="color-#ccc t-28 lh-40">
									温馨提示: <br>
									充值时间为9:00-20:00<br>
									银行卡最低充值额度为300USDT<br>
									最低充值额度为30USDT<br>
									充值成功后请提供交易成功凭证截图<br>
								</view>
							</view>
						</box-box>
						
						
					</view>
					<view class="h-20"></view>
					<view class="btn-area"></view>
				</scroll-view>
			</view>
			
		</view>

		<fixed-kefu></fixed-kefu>
		
		
		<pop-select ref="popSelect" title="切换货币" :list="list" @select="select"></pop-select>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				index:0,
				list:['USDT','RMB']
			};
		},
		
		methods: {
			select(e){
				this.index = e	
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>