<?php
namespace app\common\utils;

use app\common\define\Time;

/**
 * Class Times
 * @package app\common\utils
 */
class Times
{
    /**
     * 转换时间
     * @param  $time
     * @return mixed|string
     */
    public static function conversion($time)
    {
        $now_time       = Times::Date();
        $now_time       = strtotime($now_time);
        $show_time      = strtotime($time);
        $dur            = $now_time - $show_time;

        if ($dur < 0)
        {
            return  $time;

        } else
        {
            if ($dur < Time::ONE_MINUTE)
            {

                return __('second_ago',['time'=>$dur]);
            } else
            {
                if ($dur < Time::ONE_HOUR)
                {
                    return __('minutes_ago',['time'=>$dur]);
                } else
                {
                    if ($dur < Time::ONE_DAY)
                    {
                        return __('hours_ago',['time'=> floor($dur / Time::ONE_HOUR)]);
                    } else
                    {
                        if ($dur < 259200)
                        {
                            //3天内
                            return __('days_ago',['time'=> floor($dur / Time::ONE_DAY)]);

                        } else
                        {
                            return  $time;
                        }
                    }
                }
            }
        }
    }


    /**
     * 两个时间戳相差天数
     * @param int $start
     * @param int $end
     * @return int
     */
    public static function diffBetween(int $start, int $end): int
    {

        $diff_seconds = ($end - $start) > 0 ? ($end - $start) : ($start - $end);

        return floor($diff_seconds/86400);
    }


    /**
     * 时间
     * @param string $str
     * @param int $time
     * @return false|string
     */
    public static function Date(string $str = 'Y-m-d H:i:s', int $time = 0)
    {
        if (empty($time))
        {
            $times = date($str, time());
        }
        else
        {
            $times = date($str, $time);
        }

        return $times;
    }
}