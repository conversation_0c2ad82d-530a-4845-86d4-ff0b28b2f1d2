<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\SharesService;
use think\facade\Request;
use think\response\Json;

/**
 * 股权
 */
class Shares
{

    /**
     * 我的股权
     * @return Json
     */
    public function myShare(): Json
    {

        $SettingService     = new SharesService();
        $result             = $SettingService->myShare();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 金额
     * @return Json
     */
    public function amount(): Json
    {
        $params             = Request::only(['id' => 1]);

        $SettingService     = new SharesService();
        $result             = $SettingService->amount($params['id']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 全部股权
     * @return Json
     */
    public function shares(): <PERSON><PERSON>
    {
        $SettingService     = new SharesService();
        $result             = $SettingService->shares();
        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 股权信息
     * @return Json
     */
    public function info(): Json
    {
        $params             = Request::only(['id' => 1]);

        $SettingService     = new SharesService();
        $result             = $SettingService->info($params['id']);

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }

    /**
     * 默认股权
     * @return Json
     */
    public function default(): Json
    {

        $SettingService     = new SharesService();
        $result             = $SettingService->default();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }


    /**
     * 股权证书
     * @return Json
     */
    public function certificate(): Json
    {
        $SettingService     = new SharesService();
        $result             = $SettingService->certificate();

        return Ajax::message($result['code'],$result['msg'], $result['data']);
    }
}