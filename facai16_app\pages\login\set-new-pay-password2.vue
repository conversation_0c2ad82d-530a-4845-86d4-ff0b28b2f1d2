<template>
	<view>
		<view class=" page-bg scroll-view flex-page password">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="#F6F9FC">
					<template #left>
						<view>
							<image src="/static/back-g.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="c2">
							<!-- 支付 -->
						</view>
					</template>
					
				</uv-navbar>
			</view>
			<view class="flex1 f-pr fcc-h !justify-start">
				<view class="h-124"></view>
				<view class="fcc-h">
					<view class=" c2 t-40 lh-56 mb-28">
						再次输入确认密码
					</view>
					<view class=" t-32 c9 lh-45 pb-[5vh]">
						再次输入6位纯数字的支付密码
					</view>
					
					
				</view>
					
				<view class="code">
					<view class="">
						<view class="code-box flex-row">
							<view class="code-box-item">
								{{password[0] && '*' || ''}}
							</view>
							<view class="code-box-item">{{password[1] && '*' || ''}}</view>
							<view class="code-box-item">{{password[2] && '*' || ''}}</view>
							<view class="code-box-item">{{password[3] && '*' || ''}}</view>
							<view class="code-box-item">{{password[4] && '*' || ''}}</view>
							<view class="code-box-item">{{password[5] && '*' || ''}}</view>
						</view>
				
					</view>
				</view>
				
				<!-- <input type="number" v-model="password" /> -->
				
				
				
			</view>
			
			<view>
				
				<view class="keyboard flex-row flex-wrap">
					<button class="keyboard-item" @click="key(1)">
						<text>1</text>
						<text class="zimu">-</text>
					</button>
					<button class="keyboard-item" @click="key(2)">
						<text>2</text>
						<text class="zimu">ABC</text>
					</button>
					<button class="keyboard-item" @click="key(3)">
						<text>3</text>
						<text class="zimu">DEF</text>
					</button>
					<button class="keyboard-item" @click="key(4)">
						<text>4</text>
						<text class="zimu">GHI</text>
					</button>
					<button class="keyboard-item" @click="key(5)">
						<text>5</text>
						<text class="zimu">JKL</text>
					</button>
					<button class="keyboard-item" @click="key(6)">
						<text>6</text>
						<text class="zimu">MNO</text>
					</button>
					<button class="keyboard-item" @click="key(7)">
						<text>7</text>
						<text class="zimu">PQRS</text>
					</button>
					<button class="keyboard-item" @click="key(8)">
						<text>8</text>
						<text class="zimu">TUV</text>
					</button>
					<button class="keyboard-item" @click="key(9)">
						<text>9</text>
						<text class="zimu">WXYZ</text>
					</button>
					<button class="keyboard-item hide">-</button>
					<button class="keyboard-item" @click="key(0)">
						<text>0</text>
					</button>
					<button class="keyboard-item delte" @click="del()">
						<image class="" src="/static/delte.png" mode="aspectFill" :lazy-load="true"></image>
					</button>
				</view>
				<view class="btn-area"></view>
			</view>
			
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				password: ""
			};
		},
		
		methods: {
			key(key) {
				if (this.password.length < 6) {
					this.password += key
					if (this.password.length == 6) {
						// 尝试校验密码
						uni.showToast({
							title: "提交中",
							icon: "none"
						})
						
					}
				}
			},
			enter() {
			
					if (this.password.length < 6) {
						// 尝试校验密码
						uni.showToast({
							title: "密码不足6位",
							icon: "error"
						})
					}else{
						uni.showToast({
							title: "支付成功",
							icon: "success"
						})
						
					}
				
			},
			close() {
				this.$emit('close', false)
			},
			del() {
				if (this.password.length > 0) {
					this.password = this.password.substring(0, this.password.length - 1)
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		min-height: 100vh;
		background: #F6F9FC;
	}
	
	
	
	
	

	
	
	/* 使用伪类模仿border  */
	
	.border-bottom-after::after {
		content: "";
		display: block;
		width: 100%;
		height: 1px;
		background: #eee;
		position: absolute;
		left: 0;
		bottom: 0;
		transform: scaleY(0.5);
	}
	
	button::after {
		border: 0;
	}
	
	image {
		max-width: 100%;
	}
	

	
	.flex-wrap {
		flex-wrap: wrap;
	}
	
	.flex-nowrap {
		flex-wrap: nowrap;
	}
	
	.flex-row {
		display: flex;
		flex-direction: row;
	}
	
	.flex-col {
		display: flex;
		flex-direction: column;
	}
	
	.justify-content-around {
		justify-content: space-around;
	}
	
	
	.password .code {
		width: 100%;
		// height: 200upx;
		
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
	}
	
	.password .code-box {
		justify-content: center;
		align-items: center;
	
	}
	
	.password .code-box-item {
		width: 96rpx;
		height: 115rpx;
		background: #fff;
		border-radius: 16rpx;
	
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 48upx;
		font-family: 'DIN';
		line-height: 115rpx;
	}
	
	.password .code-box-item+.code-box-item {
		margin-left: 18rpx;
	}
	
	.password .forget {
		text-align: right;
		font-size: 26upx;
		line-height: 60upx;
		color: #666;
	}
	
	
	.password .keyboard {
		width: 100%;
		min-height: 432upx;
		background: #D2D5DB;
		padding-bottom: 24upx;
	}
	
	.password .keyboard-item {
		width: calc(100vw / 3 - 32upx);
		margin: 0 0 0 24upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background: #fff;
		border-radius: 20upx;
		margin-top: 24upx;
		font-size: 50upx;
		color: #333;
		box-shadow: 0 2upx 3upx rgba(0, 0, 0, .5);
		height: 100upx;
	}
	
	.password .keyboard-item.button-hover {
		opacity: .5;
	}
	
	.password .keyboard-item text {
		line-height: 50upx;
	}
	
	.password .keyboard-item text.zimu {
		font-size: 20upx;
		line-height: 30upx;
	}
	
	.password .keyboard .hide {
		opacity: 0;
	}
	
	.password .delte {
		background: none;
		box-shadow: none;
	}
	
	.password .delte image {
		width: 46upx;
		height: 36upx;
	}
	

	@media (max-height: 750px) {
		.password .code-box-item {
			
			height: 105rpx;
			
			line-height: 105rpx;
		}
	}



</style>


