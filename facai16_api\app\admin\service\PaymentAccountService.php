<?php
namespace app\admin\service;

use app\common\repository\PaymentAccountRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 支付账号
 */
class PaymentAccountService
{
    /**
     * 支付账号列表
     * @return array
     */
    public function getPaymentAccountLists(): array
    {
        $PaymentRepo = new PaymentAccountRepository();
        $data        = $PaymentRepo->paginates([],'*',10,['id'=> 'desc']);
        return Result::success($data);
    }

    /**
     * 获取支付账号信息
     * @param $id
     * @return array
     */
    public function getPaymentAccountInfo($id): array
    {
        $PaymentRepo = new PaymentAccountRepository();
        $data        = $PaymentRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加支付账号
     * @param $params
     * @return array
     */
    public function addPaymentAccount($params): array
    {
        $PaymentRepo = new PaymentAccountRepository();
        $res         = $PaymentRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新支付账号
     * @param $params
     * @return array
     */
    public function updatePaymentAccount($params): array
    {
        $PaymentRepo = new PaymentAccountRepository();

        $res         = $PaymentRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除支付账号
     * @param $id
     * @return array
     */
    public function deletePaymentAccount($id): array
    {
        $PaymentRepo = new PaymentAccountRepository();

        $res         = $PaymentRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }
}