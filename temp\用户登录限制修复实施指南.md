# 用户登录限制修复实施指南

## 实施前准备

### 1. 备份相关文件
在开始修改前，请备份以下文件：
- `facai16_api/app/index/service/LoginService.php`
- `facai16_api/app/index/middleware/LoginMiddleware.php`
- `facai16_api/app/admin/service/UserService.php`

### 2. 确认依赖项
确保以下类和方法可用：
- `UserStateRepository` 类及其 `valueByCondition` 方法
- `UserRepository` 类及其 `userStateByHeader` 方法
- `Html` 类（用于响应构建）
- `HttpResponseException` 类

## 详细实施步骤

### 步骤一：修改LoginService.php

#### 1.1 在login()方法中添加状态检查

**位置**: 第57行后（账号密码验证通过后）
**原代码**:
```php
//账号或者密码错误
if(empty($user))
{
    return Result::fail('账号或者密码错误');
}
```

**修改后**:
```php
//账号或者密码错误
if(empty($user))
{
    return Result::fail('账号或者密码错误');
}

// 检查用户登录限制状态
$UserStateRepo = new UserStateRepository();
$banLogin = $UserStateRepo->valueByCondition(['uid' => $user['id']], 'ban_login');

if ($banLogin == 1) {
    return Result::fail('账号已被限制登录，请联系客服');
}
```

#### 1.2 在register()方法中添加状态检查

**位置**: 第338行后（获取isTest后）
**原代码**:
```php
$UserStateRepo = new UserStateRepository();

$isTest        = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']],'is_test');
```

**修改后**:
```php
$UserStateRepo = new UserStateRepository();

$isTest        = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']],'is_test');

// 检查用户登录限制状态
$banLogin = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']], 'ban_login');

if ($banLogin == 1) {
    return Result::fail('账号已被限制登录，请联系客服');
}
```

### 步骤二：修改LoginMiddleware.php

#### 2.1 添加必要的引入
在文件顶部确保引入了必要的类：
```php
use think\response\Html;
use think\exception\HttpResponseException;
```

#### 2.2 在handle()方法中添加状态检查

**位置**: 第57行前（return $next($request)之前）
**原代码**:
```php
        if (empty($res))
        {
            $info     = json_encode(['code' => 444, '登录失效,请重新登录','data'=>[]],320);
            $type     = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);

            throw new HttpResponseException($response);
        }

        return $next($request);
```

**修改后**:
```php
        if (empty($res))
        {
            $info     = json_encode(['code' => 444, '登录失效,请重新登录','data'=>[]],320);
            $type     = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);

            throw new HttpResponseException($response);
        }

        // 检查用户登录限制状态
        $userState = $UserRepo->userStateByHeader('ban_login');
        if (!empty($userState) && $userState['ban_login'] == 1) {
            $info = json_encode(['code' => 445, 'msg' => '账号已被限制登录，请联系客服','data'=>[]], 320);
            $type = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);
            
            throw new HttpResponseException($response);
        }

        return $next($request);
```

### 步骤三：修改UserService.php

#### 3.1 在updateUserState()方法中添加强制退出逻辑

**位置**: 第328行后（第一个保存成功检查后）
**原代码**:
```php
        $UserStateRepo = new UserStateRepository();
        $res           = $UserStateRepo->updateByCondition(['uid' => $params['uid']], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }
```

**修改后**:
```php
        $UserStateRepo = new UserStateRepository();
        $res           = $UserStateRepo->updateByCondition(['uid' => $params['uid']], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        // 如果设置为禁止登录，立即清除用户token
        if ($params['ban_login'] == 1) {
            $UserRepo = new UserRepository();
            $UserRepo->updateByCondition(['id' => $params['uid']], ['token' => '']);
        }
```

## 测试验证

### 测试场景1：新登录限制
1. 在管理后台设置某用户的 `ban_login = 1`
2. 使用该用户账号尝试登录
3. **预期结果**: 返回"账号已被限制登录，请联系客服"错误信息

### 测试场景2：已登录用户踢出
1. 用户正常登录获取token
2. 在管理后台设置该用户的 `ban_login = 1`
3. 用户使用现有token访问任意API接口
4. **预期结果**: 返回错误码445，提示"账号已被限制登录，请联系客服"

### 测试场景3：状态恢复
1. 在管理后台设置用户的 `ban_login = 0`
2. 用户尝试重新登录
3. **预期结果**: 可以正常登录

### 测试场景4：注册自动登录限制
1. 设置某用户的 `ban_login = 1`
2. 该用户尝试注册（如果注册后自动登录）
3. **预期结果**: 注册成功但自动登录被阻止

## 错误排查

### 常见问题及解决方案

#### 问题1：UserStateRepository类未找到
**解决方案**: 确保在文件顶部添加正确的use语句：
```php
use app\common\repository\UserStateRepository;
```

#### 问题2：Html类未找到
**解决方案**: 在LoginMiddleware.php顶部添加：
```php
use think\response\Html;
use think\exception\HttpResponseException;
```

#### 问题3：userStateByHeader方法返回空
**解决方案**: 检查UserRepository中的userStateByHeader方法实现，确保正确返回用户状态数据

#### 问题4：ban_login字段不存在
**解决方案**: 确认数据库user_state表中存在ban_login字段，如不存在需要先创建

## 部署注意事项

### 1. 代码部署顺序
建议按以下顺序部署：
1. 先部署UserService.php（强制退出功能）
2. 再部署LoginService.php（登录检查）
3. 最后部署LoginMiddleware.php（中间件检查）

### 2. 回滚方案
如果出现问题，可以快速回滚到备份的原始文件

### 3. 监控建议
- 部署后监控错误日志
- 观察用户登录行为是否正常
- 检查被限制用户是否能正确被阻止

## 完成确认

修复完成后，请确认以下功能正常：
- [ ] 正常用户可以正常登录
- [ ] 被限制用户无法登录
- [ ] 已登录的被限制用户会被踢出
- [ ] 错误提示信息正确显示
- [ ] 管理后台状态设置功能正常
- [ ] 状态恢复功能正常

## 后续维护

### 日志记录
建议添加相关操作的日志记录：
- 用户登录被限制的日志
- 管理员设置用户状态的日志
- 强制退出操作的日志

### 性能考虑
- 考虑对频繁查询的用户状态进行缓存
- 监控数据库查询性能
- 必要时优化查询语句
