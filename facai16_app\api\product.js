
import {
	getUserId
} from "../utils/request/auth"
import request, {
	get,
	post,
	uploadFile
} from "../utils/request/request"



export function getAreaList(){
	return get(`/area/getList`)
}

export function getProductPage(data){
	return post('/product/getProductPage',data);
}

export function getProduct(id){
	return get(`/product/getProduct/${id}`)
}

///api/v1/buy/record/addProduct

export function buyProduct(data){
	return post('/buy/record/addProduct',data)
}

//积分商品
export function getPointsProducts(page,pageSize){
	return get(`/points/product/getPage/${page}/${pageSize}`)
}
//积分兑换
export function exchangePointsProducts(data){
	return post('/points/record/addPointsRecord',data);
}
//兑换记录
export function getPointsRecords(page,pageSize){
	return get(`/points/record/getPage/${page}/${pageSize}`)
}