<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							实名认证
						</view>
					</template>

				</uv-navbar>


			</view>
			<view class="flex1 f-pr c9 t-22 lh-30">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="mx-40 pt-24">
						<view class="mb-24 r-16" v-if="!disabled">
							<uv-upload :fileList="fileList1" name="1" multiple :maxCount="1" width="670rpx"
								@afterRead="afterRead" @delete="deletePic" height="398rpx">
								<image src="/static/settings/sfz-z.png" class="w-670 h-398"></image>

							</uv-upload>
						</view>
						<view class="mb-24 r-16" v-if="!disabled">
							<uv-upload :fileList="fileList2" name="2" multiple :maxCount="1" width="670rpx"
								@afterRead="afterRead" @delete="deletePic" height="398rpx">
								<image src="/static/settings/sfz-f.png" class="w-670 h-398"></image>
							</uv-upload>
						</view>

						<view class="">




							<box-box :blue="true">
								<view class="pt-25 px-40 pb-15">


									<view class="t-28 lh-48 color-#fff ">
										<text>姓名</text>

									</view>

									<view class="f-pr">
										<input type="text" placeholder="请输入" v-model="name"
											placeholder-style="color:#fff;font-size:30rpx" :disabled="disabled"
											class="h-92 lh-92 w-full block t-30 color-#fff" maxlength="15">

									</view>
									<uv-gap height="1" bgColor="#fff" marginBottom="30rpx"></uv-gap>

									<view class="t-28 lh-48 color-#fff ">
										<text>身份证号</text>

									</view>

									<view class="f-pr">
										<input type="text" placeholder="请输入" v-model="idcard"
											placeholder-style="color:#fff;font-size:30rpx" :disabled="disabled"
											class="h-92 lh-92 w-full block t-30 color-#fff" maxlength="15">

									</view>
									<uv-gap height="1" bgColor="#fff" marginBottom="30rpx"></uv-gap>
									<view class="t-28 lh-48 color-#fff ">
										<text>银行名称</text>

									</view>

									<view class="f-pr">
										<input type="text" placeholder="请输入" v-model="bank" :disabled="disabled"
											placeholder-style="color:#fff;font-size:30rpx"
											class="h-92 lh-92 w-full block t-30 color-#fff" maxlength="15">

									</view>
									<uv-gap height="1" bgColor="#fff" marginBottom="30rpx"></uv-gap>
									<view class="t-28 lh-48 color-#fff ">
										<text>银行账号</text>

									</view>

									<view class="f-pr">
										<input type="text" placeholder="请输入" v-model="address" :disabled="disabled"
											placeholder-style="color:#fff;font-size:30rpx"
											class="h-92 lh-92 w-full block t-30 color-#fff" maxlength="15">

									</view>
									<uv-gap height="1" bgColor="#fff" marginBottom="30rpx"></uv-gap>
									<view class="t-28 lh-48 color-#fff ">
										<text>支行名称</text>

									</view>

									<view class="f-pr">
										<input type="text" placeholder="请输入" v-model="branch" :disabled="disabled"
											placeholder-style="color:#fff;font-size:30rpx"
											class="h-92 lh-92 w-full block t-30 color-#fff" maxlength="15">

									</view>

								</view>
							</box-box>

						</view>

						<view class="h-40"></view>

						<view class=" t-26 lh-42 color-#fff">
							<view class="">
								<view>
									证件图片要求：
								</view>
								<view class="flex">
									<view class="w-20 fc">
										<view class="h-42 fc flex1">
											<view class="w-6 h-6 r-60 bg-#fff"></view>
										</view>
									</view>
									<view class="flex1">
										证件拍摄完整，不出现缺角
									</view>
								</view>
								<view class="flex">
									<view class="w-20 fc">
										<view class="h-42 fc flex1">
											<view class="w-6 h-6 r-60 bg-#fff"></view>
										</view>
									</view>
									<view class="flex1">
										各项信息及头像清晰可见，容易识别
									</view>
								</view>

								<view class="flex">
									<view class="w-20 fc">
										<view class="h-42 fc flex1">
											<view class="w-6 h-6 r-60 bg-#fff"></view>
										</view>
									</view>
									<view class="flex1">
										证件必须真实拍摄，不能使用复印件
									</view>
								</view>
								<view class="flex">
									<view class="w-20 fc">
										<view class="h-42 fc flex1">
											<view class="w-6 h-6 r-60 bg-#fff"></view>
										</view>
									</view>
									<view class="flex1">
										图片中无反光，无遮挡，无水印（如有水印，不能遮挡身 份信息）、logo等情况
									</view>
								</view>


							</view>

						</view>


					</view>

					<view class="h-20"></view>
					<view class="btn-area"></view>
				</scroll-view>
			</view>
			<view class="" v-if="!disabled">
				<view class="bg-#fff color-#fff">
					<view class=" px-30 py-10">
						<view class="btn-full fcc" @click="submit">
							立即提交
						</view>

					</view>
					<view class="btn-area"></view>
				</view>
			</view>
		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import apiUrl from '@/utils/request/env.js'
	import * as UserApi from '@/api/user.js'
	export default {
		data() {
			return {
				fileList1: [],
				fileList2: [],
				imageUrl1: '',
				imageUrl2: '',
				name: '',
				address: '',
				idcard: '',
				bank: '',
				branch: '',
				checkInfo: null,
			};
		},
		onShow() {
			this.bankInCheck()
		},
		computed: {
			disabled() {
				return !!this.checkInfo
			},
		},
		methods: {
			async bankInCheck() {
				UserApi.bankInCheck().then((res) => {
					if (res.code == 0 && !(res.data instanceof Array)) {
						this.checkInfo = res.data
						this.name = this.checkInfo.sfz_name
						this.address = this.checkInfo.bank_account
						this.idcard = this.checkInfo.sfz_number
						this.bank = this.checkInfo.bank_name
						this.branch = this.checkInfo.bank_branch
						uni.showToast({
							icon: 'success',
							title: '已经实名认证'
						})
					}
				})
			},
			async submit() {
				let err = ''
				if (!this.imageUrl1) {
					err = '请上传身份证正面照片'
				} else if (!this.imageUrl2) {
					err = '请上传身份证背面照片'
				} else if (!this.name) {
					err = '请填写真实姓名'
				} else if (!this.idcard || !/(^\d{15}$)|(^\d{17}[\dXx]$)/.test(this.idcard)) {
					err = '请填写正确的身份证号'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await UserApi.realNameAuth({
							name: this.name,
							idcard: this.idcard,
							address: this.address,
							bank: this.bank,
							branch: this.branch,
						})

						uni.navigateBack()
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			},
			// 删除图片
			deletePic(event) {
				console.log(event)
				this[`fileList${event.name}`] = []
				this[`imageUrl${event.name}`] = ''
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				try {
					let lists = [event.file]
					this[`fileList${event.name}`][0] = {
						...event.file,
						status: 'uploading',
						message: '上传中'
					}
					const result = await this.uploadFilePromise(lists[0], event)
					this[`fileList${event.name}`][0] = Object.assign(this[`fileList${event.name}`][0], {
						status: 'success',
						message: '',
						url: `${apiUrl.baseUrl}${result}`
					})
				} catch (e) {
					this[`fileList${event.name}`] = []
				}
			},
			uploadFilePromise(file, event) {
				return new Promise((resolve, reject) => {
					let tempUrl = file[0].url;
					let a = uni.uploadFile({
						url: apiUrl.baseUrl + '/api/upload',
						header: {
							'accept-token': uni.getStorageSync('Accept-Token')
						},
						filePath: tempUrl,
						name: 'file',
						success: (res) => {
							uni.hideLoading()
							this[`iimageUrl${event.name}`] = ''
							let data = JSON.parse(res.data)
							if (data.code == 0) {
								this[`imageUrl${event.name}`] = data.data.url
								resolve(data.data.url)
							} else {
								reject()
							}
						},
						fail: () => {
							uni.hideLoading()
							reject()
						}
					});
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg ::v-deep .uv-upload__wrap__preview {
		margin-right: 0 !important;
		margin-bottom: 0 !important;
		flex: 1;
	}

	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}

	.w-670 {
		backdrop-filter: blur(29rpx);
	}
</style>