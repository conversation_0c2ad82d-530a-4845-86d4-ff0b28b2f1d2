<template>
  <div class="pages">
    <el-form :inline="true" :model="searchForm" class="header-form">
      <div class="form-inline">
        <el-form-item>
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号码"
            prop="phone"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.lv"
            placeholder="请输入层级"
            prop="lv"
            type="number"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.is_test"
            placeholder="账号类型"
            clearable
          >
            <el-option
              v-for="item in accountTypeEnums"
              :label="item.label"
              :value="item.value"
              :key="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-col :span="11">
            <el-date-picker
              v-model="searchForm.start"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="开始时间"
              style="width: 100%"
              prop="start"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="searchForm.end"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              prop="end"
              placeholder="结束时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
      </div>
      <el-form-item class="header-form-buttons">
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="pages-section">
      <h4>今日数据</h4>
      <div class="pages-section--body">
        <el-row :gutter="10">
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日充值金额</p>
              <p class="pages-section--data">
                {{ tableData.today_recharge_money }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日提款金额</p>
              <p class="pages-section--data">
                {{ tableData.today_withdraw_money }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日收益总额</p>
              <p class="pages-section--data">
                {{ tableData.today_shouyi_money }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日注册人数</p>
              <p class="pages-section--data">{{ tableData.today_zhuceshu }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日实名人数</p>
              <p class="pages-section--data">{{ tableData.today_shiming }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日充值人数</p>
              <p class="pages-section--data">
                {{ tableData.today_chongzhi_shu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日订阅金额</p>
              <p class="pages-section--data">
                {{ tableData.today_dingyue_jiner }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日订阅订单数</p>
              <p class="pages-section--data">
                {{ tableData.today_dingyue_shu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日订阅人数</p>
              <p class="pages-section--data">
                {{ tableData.today_dingyue_reshu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日抽奖红包</p>
              <p class="pages-section--data">
                {{ tableData.today_choujiang_hongbao }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日签到人数</p>
              <p class="pages-section--data">
                {{ tableData.today_qiandao_renshu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日上分</p>
              <p class="pages-section--data">{{ tableData.today_shangfen }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">今日首存人数</p>
              <p class="pages-section--data">
                {{ tableData.today_first_recharge_ren }}
              </p>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="pages-section">
      <h4>总计</h4>
      <div class="pages-section--body">
        <el-row :gutter="10">
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总充值</p>
              <p class="pages-section--data">{{ tableData.total_chongzhi }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总提款</p>
              <p class="pages-section--data">{{ tableData.total_tixian }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总收益</p>
              <p class="pages-section--data">{{ tableData.total_shouyi }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总注册人数</p>
              <p class="pages-section--data">{{ tableData.total_zhuce }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总实名人数</p>
              <p class="pages-section--data">{{ tableData.total_shiming }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总充值人数</p>
              <p class="pages-section--data">
                {{ tableData.total_chongzhi_reshu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总订阅金额</p>
              <p class="pages-section--data">
                {{ tableData.total_dingyue_jiner }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总订阅订单数</p>
              <p class="pages-section--data">
                {{ tableData.total_dingyue_shu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总订阅人数</p>
              <p class="pages-section--data">
                {{ tableData.total_dingyue_ershu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总抽奖红包</p>
              <p class="pages-section--data">
                {{ tableData.total_choujiang_hongbao }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总签到人数</p>
              <p class="pages-section--data">
                {{ tableData.total_qiandao_renshu }}
              </p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总上分</p>
              <p class="pages-section--data">{{ tableData.total_shangfen }}</p>
            </div>
          </el-col>
          <el-col :lg="4" :sm="8" :md="6">
            <div class="pages-section--unit">
              <p class="pages-section--text">总余额</p>
              <p class="pages-section--data">{{ tableData.total_yuer }}</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { accountTypeEnums } from "@/config/enums";

onMounted(() => {
  getList();
});

const searchForm = ref({
  start: "",
  end: "",
  lv: 2,
  is_test: "",
  phone: "",
});

const { proxy } = getCurrentInstance();

const onQuery = () => {
  getList();
};

const tableData = ref({});
const getList = async () => {
  const res = await proxy.$http({
    method: "post",
    url: "/index/welcome",
    data: searchForm.value,
  });

  if (res.code == 0) {
    tableData.value = res.data;
  }
};
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
.pages {
  width: calc(100% - 24px);
  display: block;
  background-color: #f1f1f1;
  padding: 0;
  &-section {
    margin-bottom: 10px;
    background-color: #fff;
    width: 100%;
    padding: 10px;

    h4 {
      font-size: 16px;
      line-height: 32px;
      border-bottom: 1px solid #eee;
      text-align: left;
      font-weight: 600;
      font-size: 15px;
    }

    &--body {
      padding: 8px 10px;
      display: flex;
      gap: 8px;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      padding: 10px 0;
    }
    &--unit {
      background-color: #f8f8f8;
      border-radius: 4px;
      transition: all 0.25s;
      cursor: pointer;
      padding: 15px 10px;
      &:hover {
        background-color: #cfcfcf;
      }
      text-align: center;
      font-size: 14px;
      line-height: 1.8;
      margin-bottom: 10px;
    }
    &--data {
      color: #009688;
    }
    ::v-deep .el-row {
      width: 100%;
    }

    ::v-deep .el-col {
    }
  }
}
.header-form {
  display: flex;
  align-items: center;
  height: max-content;
  padding-bottom: 10px;
  border-bottom: 1px solid #ececec;
  /deep/ .el-input {
    --el-input-width: 190px;
  }
  /deep/ .el-select {
    --el-select-width: 190px;
  }
  .form-inline {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .header-form-buttons {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    height: 100%;
    padding-top: 10px;
  }
}
</style>
