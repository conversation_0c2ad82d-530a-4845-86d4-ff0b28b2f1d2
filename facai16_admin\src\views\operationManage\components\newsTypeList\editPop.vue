<template>
    <el-form label-width="80px" :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="类型名称" required >
            <el-input v-model="form.title"   />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue'

const form = ref({
    title: '',
})
const props = defineProps(['item'])

onMounted(() => {
    nextTick(()=> {
        form.value = Object.assign(form, props.item)
    })
})

defineExpose({form})

</script>

<style lang="less" scoped>
.demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
/deep/ .el-radio-group {
    width: 220px;
}
.form-title {
 text-align: left;
 padding-left: 30px;
 margin: 20px auto 10px;
 height: 44px;
 background-color: #f2f2f2;
 border-radius: 5px;
 line-height: 44px;
}
</style>