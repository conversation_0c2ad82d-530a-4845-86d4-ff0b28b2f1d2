<?php

namespace app\common\command;

use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserInfoRepository;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;


/**
 * 项目结束
 */
class ItemFinish extends  Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('ItemFinish')->setDescription('the ItemFinish command');
    }


    /**
     * 数据备份
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function execute(Input $input, Output $output)
    {
        // 获取当前时间戳
        $currentTime   = time();

        $ItemOrderRepo = new ItemOrderRepository();
        $UserInfoRepo  = new UserInfoRepository();

        $data          = $ItemOrderRepo->where('item_status','=',2)
            ->whereRaw("cycle_start >= cycle_end")
            ->where("end_time",'<', date("Y-m-d H:i:s", $currentTime))
            ->limit(3000)
            ->select()->toArray();

//        0.每期返息到期返本,1一次性到期反本息,2每期返息不返本3每期返息到期双倍本金
        foreach ($data as $value)
        {

            Db::startTrans();

            try {

                if ($value['item_type'] == 2)
                {

                    $update = [
                        'item_status' => 1,
                        'update_time' => time()
                    ];

                    $res = $ItemOrderRepo->updateById($value['id'], $update);

                    if(!$res)
                    {
                        Record::log('command','项目结束失败', $update);
                        Db::rollback();
                        continue;
                    }
                }
                else
                {
                    $amount = ($value['item_type'] == 3) ? $value['amount'] * 2 : $value['amount'];

                    $update = [
                        'item_status' => 1,
                        'update_time' => time()
                    ];

                    $res = $ItemOrderRepo->updateById($value['id'],$update);


                    if (!$res)
                    {
                        Db::rollback();
                        continue;
                    }

                    $MoneyLogRepo = new MoneyLogRepository();

                    $res          = $MoneyLogRepo->fund($value['uid'],(float) $amount,MoneyClass::RETURN_PRINCIPAL, $value['id'],'项目周期结束: +' . $amount  .'元');

                    if ($res['code'])
                    {
                        Record::log('command','项目周期结束失败1');
                        Db::rollback();
                        continue;
                    }

                    $where   = [];
                    $where[] = ['uid','=',$value['uid']];
                    $info    = $UserInfoRepo->findByCondition($where);

                    if ($info['invest_not_finish']  - $value['amount'] < 0)
                    {
                        $left = $info['invest_not_finish'];
                    }
                    else
                    {
                        $left = $value['amount'];
                    }

                    $res     = $UserInfoRepo->statistic($value['uid'],['invest_not_finish' => - (float) $left]);

                    if ($res['code'])
                    {
                        Record::log('command','项目周期结束失败2');
                        Db::rollback();
                        continue;
                    }

                }

                Db::commit();

            }catch (\Exception $exception)
            {
                Record::exception('command', $exception, '项目周期结束失败3');
                Db::rollback();
            }

        }
    }

}