<template>
    <el-form
      label-width="80px"
      :inline="true"
      :model="form"
      class="demo-form-inline"
    >
      <el-form-item label="类型名称" required>
        <el-input v-model="form.title" />
      </el-form-item>
  
      <el-form-item label="支付编码" required>
        <el-input v-model="form.code" />
      </el-form-item>
      <el-form-item
        label="开启状态"
        prop="status"
        :rules="[
          {
            required: true,
            message: '请选择开启状态',
            trigger: ['change'],
          },
        ]"
      >
        <el-select v-model="form.status" placeholder="开启状态" clearable>
          <el-option
            v-for="item in openEnums"
            :label="item.label"
            :key="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </template>
  
  <script setup>
  import { nextTick, onMounted, ref } from "vue";
  import { openEnums, getLabelByVal } from "@/config/enums";
  
  const form = ref({
    title: "",
    code: "",
    status: "",
  });
  const props = defineProps(["item"]);
  
  onMounted(() => {
    nextTick(() => {
      form.value = Object.assign(form, props.item);
    });
  });
  
  const successUpload = (res) => {
    form.value.img = res.data.url;
  };
  
  defineExpose({ form });
  </script>
  
  <style lang="less" scoped>
  .demo-form-inline {
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
  }
  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }
  
  .demo-form-inline .el-select {
    --el-select-width: 220px;
  }
  /deep/ .el-radio-group {
    width: 220px;
  }
  .form-title {
    text-align: left;
    padding-left: 30px;
    margin: 20px auto 10px;
    height: 44px;
    background-color: #f2f2f2;
    border-radius: 5px;
    line-height: 44px;
  }
  </style>
  