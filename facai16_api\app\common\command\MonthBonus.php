<?php
declare (strict_types = 1);

namespace app\common\command;


use app\common\model\MoneyClass;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;


/**
 * 用户月奖金
 */
class MonthBonus extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('MonthBonus')->setDescription('the MonthBonus command');
    }


    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {

        $UserRepo       = new UserRepository();

        $UserRepo->where('level','>',0)->chunk(100, function ($members)
        {
            $MoneyLogRepo   = new MoneyLogRepository();
            $LevelRepo      = new LevelRepository();

            foreach ($members as $member)
            {

                $where      = [];
                $where[]    = ['id', '=', $member['level']];
                $levels     = $LevelRepo->findByCondition($where);

                if (empty($levels))
                {
                    continue;
                }

                Db::startTrans();

                try {


                    $res = $MoneyLogRepo->fund($member['id'], $levels['bonus'],MoneyClass::MEMBER_MONTHLY_BONUS,'salary','用户月奖励 +' . $levels['salary']);

                    if (!$res)
                    {
                        Db::rollback();
                        return;
                    }

                    // 提交事务
                    Db::commit();

                } catch (\Exception $exception)
                {
                    // 回滚事务
                    Db::rollback();
                    Record::exception('command', $exception);
                }

            }


        });






    }


}
