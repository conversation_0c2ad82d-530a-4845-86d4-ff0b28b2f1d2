<?php
namespace app\index\service;

use app\common\jobs\LevelUpJob;
use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\UserRepository;
use app\common\utils\BCalculator;
use app\common\utils\Order;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;
use think\facade\Request;

/**
 * 资金模块
 */
class MoneyService
{

    /**
     *  资金记录
     * @param $params
     * @return array
     */
    public function getMoneyLog($params): array
    {
        $UserRepo     = new UserRepository();
        $id           = $UserRepo->userByHeader('id');

        $where = [];

        if (!empty($params['class_id']))
        {
            $where[] = ['class_id', 'in', $params['class_id']];
        }

        if (!empty($params['start_time']))
        {
            $where[] = ['create_time', '>', strtotime($params['start_time'])];
        }

        $where[]      = ['uid', '=', $id];

        $MoneyLogRepo = new MoneyLogRepository();
        $data         = $MoneyLogRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * 转账
     * @param $params
     * @return array
     */
    public function transfer($params): array
    {
        $UserRepo     = new UserRepository();
        $user         = $UserRepo->userByHeader();
        $MoneyLogRepo = new MoneyLogRepository();

        if ($params['payPassword'] != $user['pin'])
        {
            return Result::fail( '支付密码错误！');
        }

        Db::startTrans();

        try {

            $odder        = Order::uniqueNo();
            $txt          = '复投转账(出):可提余额-' . $params['money']. '元';
            $res          = $MoneyLogRepo->fund($user['id'], $params['money'],MoneyClass::REINVESTMENT_TRANSFER,$odder, $txt);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $txt          = '复投转账(进):可用余额+' . $params['money']. '元';
            $res          = $MoneyLogRepo->fund($user['id'], $params['money'],MoneyClass::REINVESTMENT_TRANSFER_IN,$odder, $txt);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            $gift         = BCalculator::calc($params['money'])->mul(0.01)->result();
            $txt          = '复投奖励:可用余额+' . $gift. '元';
            $res          = $MoneyLogRepo->fund($user['id'], $gift,MoneyClass::REINVESTMENT_GIFT, $odder, $txt);

            if ($res['code'])
            {
                Db::rollback();
                return Result::fail($res['msg']);
            }

            // 提交事务
            Db::commit();

            queue(LevelUpJob::class, $user['id']);

            return Result::success();

        } catch (\Exception $e)
        {
            // 回滚事务
            Db::rollback();
            return Result::fail(Record::exception('index', $e,'WithdrawService->withdraw'));
        }

    }
}