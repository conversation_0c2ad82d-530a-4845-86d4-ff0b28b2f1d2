<?php
declare (strict_types = 1);

namespace app\common\command;

use app\common\model\MoneyClass;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\TeamRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Request;


/**
 * 用户升级
 */
class TeamLevelDown extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('UserLevelDown')->setDescription('the UserLevelDown command');

    }


    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $UserRepo  = new UserRepository();
        $where     = [];
        $where[]   = ['level_team', '>', 0];

        $UserRepo->where($where)->chunk(100, function ($member)
        {
            $UserRepo       = new UserRepository();
            $TeamRepo      = new TeamRepository();

            foreach ($member as $value)
            {

                $where            = [];
                $where[]          = ['top_id', '=', $value['id']];
                $where[]          = ['level', '<=', 99];

                $UserRelationRepo = new UserRelationRepository();
                $members          = $UserRelationRepo->selectByCondition($where,'uid');
                $members          = array_column($members, 'uid');
                $history          = strtotime(date('Y-m-d 00:00:00', 1));
                $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day', Request::time())));
                $UserInfoRepo     = new UserInfoRepository();

                //1总充值
                $recharge         = empty($members) ? 0 :  $UserInfoRepo->totalRecharge($history, $tomorrow,'', $members);
                //2总提现
                $withdraw         = empty($members) ? 0 :  $UserInfoRepo->totalWithdraw($history, $tomorrow,'', $members);

                $yeji             = $recharge - $withdraw;

                $where      = [];
                $where[]    = ['v1_id', '=', $value['id']];
                $team       = $UserRepo->countByCondition($where);

                $where      = [];
                $where[]    = ['id', '=', $value['level_team']];
                $levels     = $TeamRepo->findByCondition($where,'*',['id' => 'ASC']);

                if ($yeji >= $levels['invest'] && $team >= $levels['user'])
                {
                    continue;
                }
                else
                {

                    $where      = [];
                    $where[]    = ['id', '<', $value['level']];
                    $levels     = $TeamRepo->findByCondition($where,'*',['id' => 'DESC']);

                    if (empty($levels))
                    {
                        continue;
                    }

                    Db::startTrans();

                    try {

                        $update = [
                            'level_team'         => $levels['id'],
                            'level_team_name'    => $levels['title'],
                            'update_time'        => time(),
                            'level_up_time'      => time(),
                        ];

                        $res          = $UserRepo->updateById($value['id'], $update);

                        if (!$res)
                        {
                            Db::rollback();
                            continue;
                        }


                        $LevelLogRepo = new LevelLogRepository();

                        $teamName   = empty($value['level_team']) ? '普通会员' : $value['level_team_name'];

                        $insert  = [
                            'uid'           => $value['id'],
                            'username'      => $value['username'],
                            'phone'         => $value['phone'],
                            'is_test'       => $value['is_test'],
                            'lv_id'         => $levels['id'],
                            'lv_name'       => $levels['title'],
                            'desc'          => '从' . $teamName. '降级到' . $levels['title'],
                            'create_time'   => time(),
                            'update_time'   => time()
                        ];


                        $res = $LevelLogRepo->inserts($insert);

                        if (!$res)
                        {
                            Db::rollback();
                            continue;
                        }


                        // 提交事务
                        Db::commit();

                    } catch (\Exception $exception)
                    {
                        // 回滚事务
                        Db::rollback();

                        Record::exception('job', $exception);
                    }
                }




            }

        });
    }


}
