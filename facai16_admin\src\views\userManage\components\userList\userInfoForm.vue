<template>
  <el-form
    label-width="120px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="用户名ID" required>
      <el-input v-model="form.id" disabled clearable />
    </el-form-item>
    <el-form-item label="用户名" required>
      <el-input v-model="form.username" clearable />
    </el-form-item>
    <el-form-item label="昵称" required>
      <el-input v-model="form.nickname" clearable />
    </el-form-item>
    <el-form-item label="密码" required>
      <el-input v-model="form.password" clearable />
    </el-form-item>
    <el-form-item label="支付密码" required>
      <el-input v-model="form.pin" clearable />
    </el-form-item>
    <el-form-item label="手机号" required>
      <el-input v-model="form.phone" clearable />
    </el-form-item>
    <el-form-item label="邮箱" required>
      <el-input v-model="form.email" clearable />
    </el-form-item>
    <el-form-item label="实名姓名" required>
      <el-input v-model="form.sfz_name" clearable />
    </el-form-item>
    <el-form-item label="身份证号码" required>
      <el-input v-model="form.sfz_number" clearable />
    </el-form-item>
    
    <el-form-item label="会员等级" required>
      <el-select v-model="form.level" placeholder="" clearable>
        <el-option
          v-for="item in levelList"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="用户头像" prop="avatar">
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="successUpload"
        :on-error="handleErr"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { getCurrentInstance, nextTick, onMounted, ref } from "vue";
import {
  limitEnums,
  accoutActiveEnums,
  accountTypeEnums,
  userlistSfzStatusEnums,
} from "@/config/enums";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  uid: "",
  username: "",
  nickname: "",
  password: "",
  pin: "",
  phone: "",
  email: "",
  sfz_name: "",
  sfz_number: "",
  avatar: "",
  level: "",
});
const props = defineProps(["item"]);

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    form.value = Object.assign(form.value, props.item);
    getUserState();
  });
  getLevelList()
});

const successUpload = (res) => {
  form.value.avatar = res.data.url;
};

const handleErr = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

const { proxy } = getCurrentInstance();
const getUserState = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "user/getUser?id=" + form.value.id,
  });

  if (res.code == 0) {
    form.value = Object.assign(form.value, res.data);
  }
};

const levelList = ref([])
const getLevelList = async () => {
    const res = await proxy.$http({
        method: 'get',
        url: '/Level/getLevelLists'
    })
    if (res.code == 0) {
        levelList.value = res.data.data
    }
}

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}
.demo-form-inline .el-input {
  --el-input-width: 260px;
}

.demo-form-inline .el-select {
  --el-select-width: 260px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}
/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
