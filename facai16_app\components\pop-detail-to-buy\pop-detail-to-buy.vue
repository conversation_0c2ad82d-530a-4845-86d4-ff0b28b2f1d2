

<template>
	<view>
		<uni-popup ref="popup" type="bottom" :safeArea="false" isMaskClick>
			
				<view class="pop-box" :class="{'bg-fff':bg}">
					<view class="px-32">
						<view class="fcc h-127  f-pr">
							<view class="fcc w-324 h-63 r-30 bg-#fff t-26 color-#222">
								<view class="fcc flex1 h-63 tab r-100" @click="tabIndex=0" :class="{on:tabIndex==0}">
									物流配送
								</view>
								<view class="fcc flex1 h-63 tab r-100" @click="tabIndex=1" :class="{on:tabIndex==1}">
									门店自提
								</view>
							</view>
							<view class="fcc f-pa right-0 top-30" @click="$refs.popup.close()">
								<image src="/static/pop-x.png" mode="aspectFit" size-40></image>
							</view>
						</view>
					</view>
					
					<view class="bd">
						<scroll-view scroll-y="true" style="max-height:60vh;" >
							<view class="px-32">
								<view class="to-add-address-box px-24 r-12 fc" v-if="tabIndex==0">
									<view class="mr-24 ">
										<image src="/static/add-c.png" mode="aspectFit" size-48  block></image>
									</view>
									<view class="t-32 lh-45 color-#222 w-450">
										添加收货地址
									</view>
								</view>
								<view class="to-add-address-box px-24 r-12 fc" v-else>
									<view class="mr-24 ">
										<image src="/static/pin-c.png" mode="aspectFit" size-48  block></image>
									</view>
									<view class="t-32 lh-45 color-#222 w-450">
										请选择自提点
									</view>
								</view>
								
								<!-- 显示地址 -->
								<view class="address-box p-24 t-32 lh-45 r-12 c2 fc-bet" v-if="false">
									<view>
										<view class="mb-8 fw-500">
											<text class="mr-24">杨阳洋</text>
											<text>135 1234 5678</text>
										</view>
										<view class="t-26 lh-37 w-600">
											北京市北京市东城区四海一家1号楼108
										</view>
									</view>
									<view>
										<uv-icon name="arrow-right"></uv-icon>
									</view>
								</view>
								
								<uv-gap height="24rpx"></uv-gap>
								
								<view class="p-24 bg-#fff r-12">
									<view class="flex">
										<view class="mr-24">
											<image src="/static/index/341x321.png" mode="aspectFill" size-120 r-12 block></image>
										</view>
										<view class="flex1 flex flex-col justify-between">
											<view>
												<view class="t-32 lh-45 color-#222 ddd">
													商品名称商品名称商品名称商品…
												</view>
												<view class="t-22 color-#FF5E5E lh-30" v-if="tabIndex==1">
													该商品不支持当前所选配送方式
												</view>
											</view>
											<view class="fc-bet">
												<view class="color-#FF5E5E lh-40">
													<text class="t-20">￥</text>
													<text class="fm t-36">78200</text>
												</view>
												<view>
													<uv-number-box buttonSize="36rpx" :min="1" :max="100"></uv-number-box>
												</view>
											</view>
										</view>
									</view>
									<uv-gap height="24rpx"></uv-gap>
									<uv-gap height="1" bgColor="#E6E7EB"></uv-gap>
									<uv-gap height="24rpx"></uv-gap>
									
									<view class="fc-bet t-28 lh-40 color-#222">
										<view class="color-#999">
											优惠券
										</view>
										<view>
											已抵扣¥100
										</view>
									</view>
									<uv-gap height="24rpx"></uv-gap>
									<view class="fc-bet t-28 lh-40 color-#222">
										<view class="color-#999">
											合计
										</view>
										<view>
											¥2580
										</view>
									</view>
									
									
									
									
									
									
								</view>
								
								
								<uv-gap height="24rpx"></uv-gap>
								
								<view class="p-24 bg-#fff r-12 t-28 color-#222 lh-40">
									<view class="mb-12">
										买家留言
									</view>
									<view>
										<textarea name="" placeholder="请输入留言给买家" class="block h-80 w-[100%] t-28" ></textarea>
									</view>
								</view>	
								
								
								<uv-gap height="24rpx"></uv-gap>
								
								
								
								<!-- 支付方式 -->
								<view class="py-16 bg-#fff r-12 t-30 color-#222 lh-42">
									<view class="fc-bet py-16 px-32" @click="payIndex=0">
										<view class="mr-24">
											<image src="/static/pay-weixin.png" mode="aspectFit" block size-48></image>
										</view>
										<view class="flex1">
											微信支付
										</view>
										<view>
											<image src="/static/check1-on.png" mode="aspectFit" block size-40 v-if="payIndex==0"></image>
											<image src="/static/check1.png" mode="aspectFit" block size-40 v-else></image>
										</view>
									</view>
									<view class="fc-bet py-16 px-32" @click="payIndex=1">
										<view class="mr-24">
											<image src="/static/pay-alipay.png" mode="aspectFit" block size-48></image>
										</view>
										<view class="flex1">
											支付宝
										</view>
										<view>
											<image src="/static/check1-on.png" mode="aspectFit" block size-40 v-if="payIndex==1"></image>
											<image src="/static/check1.png" mode="aspectFit" block size-40 v-else></image>
										</view>
									</view>
									<view class="fc-bet py-16 px-32" @click="payIndex=2">
										<view class="mr-24">
											<image src="/static/pay-bank.png" mode="aspectFit" block size-48></image>
										</view>
										<view class="flex1 fc">
											<view class="mr-12">
												农业银行储蓄卡(2432)
											</view>
											<view class="btn-change fc" @click.stop="changeBank">
												 <text class="t-26 lh-37 color-#222 mr-2">更换</text>
												 <uv-icon name="arrow-right" size="32rpx" color="#999"></uv-icon>
											</view>
										</view>
										<view>
											<image src="/static/check1-on.png" mode="aspectFit" block size-40 v-if="payIndex==2"></image>
											<image src="/static/check1.png" mode="aspectFit" block size-40 v-else></image>
										</view>
									</view>
								</view>	
								
								
								
								<uv-gap height="62rpx"></uv-gap>
							</view>
						</scroll-view>
					</view>
					<view class="bg-#fff py-20 px-32">
						<view class=" border-2 r-100 border-color-#FF5E5E border-solid flex">
							<view class="flex1 pl-48 fc">
								<view class="color-#FF5E5E">
									<text class="t-24 lh-33 color-#222 mr-8">合计</text>
									<text class="t-22">￥</text>
									<text class="fm t-40">8240</text>
								</view>
							</view>
							<view class="h-96 w-286 fcc color-#fff t-32 bg-#ff5e5e raduis-right" @click="showPayPassword=true">
								提交订单
							</view>
						</view>
					</view>
					<view class="btn-area bgfff"></view>
				</view>
			
		</uni-popup>
		
		<pop-box  ref="popChangeBank" title="选择银行卡">
			
			<view class="bank-list-select t-32 lh-45">
				<view class="fc-bet py-24 px-32" v-for="(item,index) in bankList" :key="index" @click="bankClick(index)">
				
					<view class="flex1" :class="{'color-#7357FF':bankIndex == index}">
						<view>
							{{item.name}}
						</view>
						<view class="t-28 color-#999">
							{{item.cardId}}
						</view>
					</view>
					<view v-if="bankIndex == index">
						<image src="/static/checked.png" mode="aspectFit" w-28 h-23 block></image>
					</view>
				</view>
			</view>
		</pop-box>	
		
		
		<payPassword :show="showPayPassword" @close="closePayPasswrodBox" @enter="enter"></payPassword>
	</view>
</template>

<script>
	export default {
		name:"pop-detail-to-buy",
		props: {
			
		},
		data() {
			return {
				showPayPassword:false,
				tabIndex:0,
				payIndex:0,
				
				bankIndex:0,
				bankList:[
					{
						name:"中国银行卡储蓄卡",
						cardId:'666 6666 6666 6666'
					},
					{
						name:"工商银行",
						cardId:'777 6666 6666 6666'
					}
				]
			};
		},
		methods: {
			closePayPasswrodBox(e){
				this.showPayPassword = false
			},
			enter(e){
				this.showPayPassword = false
				console.log("pasword",e)
			},
			changeBank(){
				this.$refs.popChangeBank.open()
			},
			bankClick(i){
				this.bankIndex = i
				this.$refs.popChangeBank.close()
			},
			open() {
				this.$refs.popup.open()
			},
			close(){
				this.$refs.popup.close()
			}
		},
	}
</script>

<style lang="scss" scoped>
	
	.pop-box{
		background: #F6F9FC;
		border-radius: 32rpx 32rpx 0px 0px;
	}
	.tab.on{
		background: #7357FF;
		color: #fff;
	}
</style>