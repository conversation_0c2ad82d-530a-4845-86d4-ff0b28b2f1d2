<?php

namespace app\common\strategy\pay\lib;

use app\common\strategy\pay\PayInterface;
use app\common\strategy\pay\Recharge;
use app\common\utils\Arrays;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * HengTongsPay
 */
class HaiYangPay implements PayInterface
{
    //支付类型
    const CHANNEL = 'HaiYangPay';

    //配置
    protected $config;

    //自动化
    public function __construct()
    {
        $this->config    = Config::get('serve_pay.strategy.' . self::CHANNEL);
    }


    /**
     * 请求
     * @param $data
     * @return mixed|string
     */
    public function send($data)
    {
        $params  = [
            'version'       => 'V1.0',
            'mch_id'        => $this->config['mch_id'],
            'trade_type'    => $this->config['channel'],
            'out_trade_no'  => $data['orderNo'],
            'amount'        => $data['money'],
            'attach'        => 1,
            'body'          => 2,
            'mch_create_ip' => Request::ip(),
            'notify_url'    => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL),
            'return_url'    => CompleteRequest('/api/callback?ZhiFuTongDao=' . self::CHANNEL)
        ];

        $params['sign']  = $this->signature($params);

        $result  = getContent($this->config['url'],'POST', $params);

        $uri     = '';

        Log::channel('pay')->info(self::CHANNEL . '[send]' . $result);

        $result  = json_decode($result,true);

        if ($result['code'] == 0)
        {
            $uri = $result['data']['payUrl'];
        }


        return $uri;
    }

    /**
     * 回调
     * @return string
     */
    public function notify(): string
    {
        // 获取GET参数
        $param = Request::param();
        $param = Arrays::withOut($param,['ZhiFuTongDao']);

        Log::channel('pay')->info(self::CHANNEL . '[notify]' . json_encode($param));

//        $param = '{"amount":"500.00","out_trade_no":"17402054329350","sign":"0186D8664AF27CD0D8C6E62396B698C8","trade_type":"135889","mch_id":"HH-1","sys_order_no":"NY20250222142352A212F5C","pay_time":"20250222142638","status":"1"}';
//        $param = json_decode($param,true);

        $sign  = $param['sign'];

        $param = Arrays::withOut($param,['sign']);

        if ($this->signature($param) != $sign)
        {
            Log::info(self::CHANNEL . '[notify]' . "验签失败,订单:" . json_encode($param));
            return 'fail';
        }


        $orderId  = $param['out_trade_no'] ?? '';

        $Recharge = new Recharge();

        $res      = $Recharge->handle($orderId);

        if ($res)
        {
            Log::info(self::CHANNEL . '[notify]' . "回调成功,订单:" . json_encode($param));
            return 'success';
        }
        else
        {
            return 'fail';
        }
    }


    /**
     * 签名
     * @param $params
     * @return string
     */
    public function signature($params): string
    {
        // 按照ASCII码对参数名进行排序
        ksort($params);

        // 拼接待签名字符串
        $stringToSign = '';

        foreach ($params as $key => $value)
        {
            $stringToSign .= $key . '=' . $value . '&';
        }

        $stringToSign  = rtrim($stringToSign, '&');

        $stringToSign .= $this->config['sign'];
        // 使用MD5算法生成签名
        return strtoupper(md5($stringToSign));
    }

}