<template>
  <div class="layout">
    <router-view v-if="route.path === '/login'" class="full-page" />
    <template v-if="route.path != '/login'">
      <div class="layout-header">
        <div class="layout-header--logo" @click="router.push('/')">
          后台管理
        </div>
        <div class="layout-header--main">
          <el-icon @click="isCollapse = !isCollapse">
            <Menu />
          </el-icon>
        </div>
        <div
          style="padding-right: 20px; gap: 20px; display: flex; cursor: pointer"
        >
          <button
            @click="playAudio"
            ref="playAudioBtn"
            style="
              display: block;
              width: 0;
              height: 0;
              overflow: hidden;
              opacity: 0;
            "
          >
            播放声音
          </button>
          <audio
            controls
            ref="audioRef"
            style="display: block; width: 0; height: 0; overflow: hidden"
          >
            <source src="./v.mp3" type="audio/mpeg" />
            Your browser does not support this audio format.
          </audio>
          <el-badge
            title="在线人数"
            :value="onlineCount"
            @click="router.push('/userList')"
            class="item"
          >
            <el-icon><User /></el-icon>
          </el-badge>
          <el-badge
            title="实名"
            :value="notice.idcard"
            @click="router.push('/realnameList')"
            class="item"
          >
            <el-icon><Comment /></el-icon>
          </el-badge>
          <el-badge
            title="充值"
            :value="notice.recharge"
            @click="router.push('/chargeList')"
            class="item"
          >
            <el-icon><Wallet /></el-icon>
          </el-badge>
          <el-badge
            title="提款"
            :value="notice.withdraw"
            @click="router.push('/withdrawList')"
            class="item"
          >
            <el-icon><CreditCard /></el-icon>
          </el-badge>
        </div>

        <el-dropdown placement="bottom-start">
          <el-button>
            用户：{{
              store.state.currentUser.username || currentUser.username
            }}</el-button
          >
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="layout-body">
        <leftMenu :isCollapse="isCollapse" />
        <!-- {{  store.state  }} -->
        <div class="layout-main">
          <div></div>
          <el-breadcrumb
            class="layout-main--breadcrumb"
            :separator-icon="ArrowRight"
          >
            <el-breadcrumb-item
              v-for="(item, index) in store.state.breakCum"
              :to="{ path: item.path }"
              >{{ item.name }}</el-breadcrumb-item
            >
          </el-breadcrumb>
          <router-view class="layout-main--routerview" />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, onActivated } from "vue";
import leftMenu from "./leftMenu.vue";
import store from "@/store";
import { useRoute, useRouter } from "vue-router";
const isCollapse = ref(false);

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
onActivated(() => {
  currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
});
const route = useRoute();
const router = useRouter();
const notice = ref({
  recharge: 0, //充值
  withdraw: 0, //提款
  idcard: 0, //实名
});

const { proxy } = getCurrentInstance();
onMounted(() => {
  getNotice();
  setInterval(() => {
    getNotice();
  }, 10000);
  getOnlineCount();
  //  setInterval(() => {

  //   // music.loop =true;
  //   // music.playbackRate = 2;
  //   // music.pause();
  //   // triggerProgrammaticClick()
  //   console.log(audioRef.value)
  //   audioRef.value.play().catch((e) => console.error("播放失败:", e));
  //  }, 3000);
});

const triggerProgrammaticClick = () => {
  const clickEvent = new MouseEvent("click", {
    bubbles: true, // 允许事件冒泡
    cancelable: true, // 允许事件被取消
    view: window, // 关联的 window 对象
  });
  // playAudioBtn.value.click();
  playAudioBtn.value.dispatchEvent(clickEvent);
};

const playAudioBtn = ref(null);
const audioRef = ref(null);

const playAudio = () => {
  audioRef.value.play().catch((e) => console.error("播放失败:", e));
};

const onlineCount = ref(0);
const getOnlineCount = async () => {
  try {
    const res = await proxy.$http({
      url: "/index/onlineUser",
    });
    if (res.code == 0) {
      onlineCount.value = res.data.count;
    }
  } catch (error) {
    console.log(error);
  }
};

const getNotice = async () => {
  try {
    const res = await proxy.$http({
      url: "/index/notice",
    });
    if (res.code == 0) {
      if (notice.value.recharge < res.data.recharge) {
        // audioRef.value.play().catch((e) => console.error("播放失败:", e));
        triggerProgrammaticClick();
      }
      notice.value = res.data;
    }
  } catch (error) {
    console.log(error);
  }
};

const logout = () => {
  sessionStorage.removeItem("__TOKEN");
  router.push("/login");
};
</script>

<style lang="less" scoped>
.layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-header {
    background-color: #222222;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    padding: 0 12px;

    &--logo {
      width: 220px;
      text-align: left;
      cursor: pointer;
    }

    &--main {
      flex: 1;
      width: 100%;
      color: #fff;
      text-align: left;

      .el-icon {
        cursor: pointer;
      }
    }
  }

  &-body {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
  }

  &-menu {
    // width: 220px;
    height: 100%;
    background-color: #eee;
    transition: all 0.25s;
  }

  .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 220px;
    min-height: 100%;
    background-color: #eee;
  }

  &-main {
    width: 100%;
    flex: 1;
    height: 100%;
    overflow: auto;
    transition: all 0.25s;
    background-color: rgb(241, 241, 241);
    display: flex;
    flex-direction: column;

    &--breadcrumb {
      height: 44px;
      display: flex;
      width: 100%;
      background-color: #fff;
      padding: 0 12px;
    }

    &--routerview {
      display: flex;
      flex: 1;
      height: 100%;
      margin: 12px;
      padding: 12px;
      overflow: auto;
      background-color: #fff;
      border-radius: 3px;
    }
  }
}
</style>
