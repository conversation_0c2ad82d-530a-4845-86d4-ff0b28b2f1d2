# API后端 (facai16_api) 详细结构

## 目录结构

```
facai16_api/
├── app/                    # 应用目录
│   ├── admin/             # 管理后台应用
│   │   ├── controller/    # 控制器
│   │   ├── service/       # 业务逻辑层
│   │   └── middleware/    # 中间件
│   ├── index/             # 前台API应用
│   │   ├── controller/    # 控制器
│   │   ├── service/       # 业务逻辑层
│   │   └── middleware/    # 中间件
│   └── common/            # 公共模块
│       ├── model/         # 数据模型
│       ├── repository/    # 数据仓库层
│       ├── utils/         # 工具类
│       ├── strategy/      # 策略模式实现
│       ├── middleware/    # 公共中间件
│       ├── command/       # 命令行工具
│       └── jobs/          # 队列任务
├── config/                # 配置文件
├── route/                 # 路由定义
├── public/                # 入口文件
└── extend/                # 扩展库
```

## 核心控制器功能

### 管理后台控制器 (app/admin/controller/)

#### User.php - 用户管理
- `getUserLists()` - 获取用户列表
- `editUser()` - 编辑用户信息
- `userRemove()` - 用户转移
- `deleteUser()` - 删除用户
- `userMoney()` - 用户资金操作

#### Item.php - 项目管理
- `getItemLists()` - 获取项目列表
- `addItem()` - 添加项目
- `editItem()` - 编辑项目
- `deleteItem()` - 删除项目

#### Transfer.php - 转账管理
- `getTransferLists()` - 转账列表
- `userTransfer()` - 用户转账审核

#### Auth.php - 权限管理
- `login()` - 管理员登录
- `getMenus()` - 获取菜单权限

### 前台API控制器 (app/index/controller/)

#### Login.php - 登录注册
- `login()` - 用户登录
- `register()` - 用户注册
- `goNext()` - 二次验证

#### User.php - 用户中心
- `info()` - 获取用户信息
- `detail()` - 用户资金详情
- `realName()` - 实名认证

#### Item.php - 项目投资
- `lists()` - 项目列表
- `info()` - 项目详情
- `pay()` - 项目投资
- `record()` - 投资记录

#### Bank.php - 银行卡管理
- `lists()` - 银行卡列表
- `add()` - 添加银行卡
- `bankInCheck()` - 银行卡验证

## 服务层架构 (Service)

### UserService.php - 用户服务
- 用户CRUD操作
- 用户关系管理
- 用户等级计算
- 用户转移逻辑

### ItemService.php - 项目服务
- 项目管理
- 投资逻辑
- 收益计算
- 风险控制

### TransferService.php - 转账服务
- 转账处理
- 资金流水
- 审核流程

## 数据仓库层 (Repository)

### UserRepository.php - 用户数据仓库
- 用户数据查询
- 用户关系查询
- 统计数据获取

### ItemRepository.php - 项目数据仓库
- 项目数据操作
- 投资记录管理

### MoneyLogRepository.php - 资金流水仓库
- 资金变动记录
- 流水查询统计

## 工具类 (Utils)

### Ajax.php - 响应工具
- 统一API响应格式
- 成功/失败响应封装

### RedisLock.php - Redis锁
- 分布式锁实现
- 防止并发操作

### Result.php - 结果封装
- 业务结果统一格式
- 错误码管理

## 策略模式 (Strategy)

### SMS策略 - 短信服务
- JuHe (聚合数据)
- JiuDingGe (九鼎阁)
- 统一短信接口

### SFZ策略 - 身份验证
- Tencent (腾讯云)
- AliYun (阿里云)
- JuHe (聚合数据)

## 中间件 (Middleware)

### AdminAuth.php - 管理员认证
- JWT token验证
- 权限检查

### UserAuth.php - 用户认证
- 用户登录验证
- 接口访问控制

### Cors.php - 跨域处理
- CORS头设置
- 预检请求处理
