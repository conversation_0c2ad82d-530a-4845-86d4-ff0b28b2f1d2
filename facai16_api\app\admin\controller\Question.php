<?php
namespace app\admin\controller;

use app\admin\service\QuestionService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;


/**
 * 问题
 */
class Question
{
    /**
     * 奖品列表
     * @return Json
     */
    public function getQuestionLists(): J<PERSON>
    {
        $QuestionService  = new QuestionService();
        $data             = $QuestionService->getQuestionLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 添加奖品
     * @return Json
     */
    public function addQuestion(): Json
    {
        $param            = Request::param();
        $QuestionService  = new QuestionService();
        $data             = $QuestionService->addQuestion($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新奖品
     * @return Json
     */
    public function updateQuestion(): <PERSON><PERSON>
    {
        $param              = Request::param();
        $QuestionService    = new QuestionService();
        $data               = $QuestionService->updateQuestion($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除奖品
     * @return Json
     */
    public function deleteQuestion(): Json
    {
        $id               = Request::param('id',0);
        $QuestionService  = new QuestionService();
        $data             = $QuestionService->deleteQuestion($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}