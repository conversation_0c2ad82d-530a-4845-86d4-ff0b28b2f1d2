<?php
namespace app\admin\service;

use app\common\repository\BankRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 银行服务
 */
class BankService
{

    /**
     * 获取银行列表
     * @return array
     */
    public function getBankList():array
    {
        $BankRepo = new BankRepository();
        $data     = $BankRepo->paginates();

        return Result::success($data);
    }

    /**
     * 获取银行信息
     * @param $params
     * @return array
     */
    public function getBankInfo($params): array
    {
        $BankRepo = new BankRepository();
        $data     = $BankRepo->findById($params['id']);
        return Result::success($data);
    }

    /**
     * 添加银行
     * @param $params
     * @return array
     */
    public function addBank($params): array
    {
        $BankRepo = new BankRepository();

        $insert   = [
            'create_time' => Request::time(),
            'update_time' => Request::time(),
            'title'       => $params['title'],
//            'img'         => $params['img'],
        ];

        $res = $BankRepo->inserts($insert);

        if(!$res)
        {
            return Result::fail('添加失败');
        }

        return Result::success();
    }

    /**
     * 更新银行
     * @param $params
     * @return array
     */
    public function updateBank($params): array
    {
        $BankRepo = new BankRepository();

        $update   = [
            'update_time' => Request::time(),
            'title'       => $params['title'],
//            'img'         => $params['img'],
        ];

        $res = $BankRepo->updateById($params['id'], $update);

        if(!$res)
        {
            return Result::fail('更新失败');
        }

        return Result::success();
    }

    /**
     * 删除银行
     * @param $params
     * @return array
     */
    public function deleteBank($params): array
    {
        $BankRepo   = new BankRepository();
        $res        = $BankRepo->deleteById($params['id']);

        if(!$res)
        {
            return Result::fail('删除银行');
        }

        return Result::success();
    }
}

