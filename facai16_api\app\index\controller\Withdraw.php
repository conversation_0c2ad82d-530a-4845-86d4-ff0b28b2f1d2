<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Ajax;
use app\common\utils\Record;
use app\index\service\WithdrawService;
use think\facade\Request;
use think\response\Json;


/**
 * 提现
 */
class Withdraw
{
    /**
     * 提现
     * @return Json
     */
    public function withdraw(): Json
    {

        $params       = Request::only(['id' => 0,'money' => 0,'pin' => 0]);

        $UserRepo     = new UserRepository();

        $id           = $UserRepo->userByHeader('id');

        $RedisLock    = new RedisLock();
        $status       = $RedisLock->lock('withdraw:' . $id);


        try {

            if (empty($status))
            {
                return Ajax::fail('系统正在处理订单');
            }

            $WithdrawService    = new WithdrawService();
            $result             = $WithdrawService->withdraw($params);

            $RedisLock->unLock('withdraw:' . $id);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $exception)
        {

            $RedisLock->unLock('withdraw:' . $id);

            return Ajax::message(Record::exception('index',$exception));
        }

    }



    /**
     * 提现记录
     * @return Json
     */
    public function record(): Json
    {
        $WithdrawService = new WithdrawService();
        $result          = $WithdrawService->record();

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }
}