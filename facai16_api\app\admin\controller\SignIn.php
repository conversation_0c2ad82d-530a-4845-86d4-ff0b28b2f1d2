<?php
namespace app\admin\controller;

use app\admin\service\SignInService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 签到
 */
class SignIn
{

    /**
     * 签到列表
     * @return Json
     */
    public function getSignInLists(): Json
    {
        $SignInService  = new SignInService();
        $data           = $SignInService->getSignInLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 签到礼品领取记录
     * @return Json
     */
    public function getSignInGiftLogLists(): Json
    {
        $RaffleService = new SignInService();
        $data          = $RaffleService->getSignInGiftLogLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 签到礼品
     * @return Json
     */
    public function getSignInGiftLists(): <PERSON>son
    {
        $RaffleService = new SignInService();
        $data          = $RaffleService->getSignInGiftLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 添加签到礼品
     * @return Json
     */
    public function addSignInGift(): Json
    {
        $param         = Request::param();

        $RaffleService = new SignInService();
        $data          = $RaffleService->addSignInGift($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 更新签到礼品
     * @return Json
     */
    public function updateSignInGift(): Json
    {
        $param         = Request::param();

        $RaffleService = new SignInService();
        $data          = $RaffleService->updateSignInGift($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 删除签到礼品
     * @return Json
     */
    public function deleteSignInGift(): Json
    {
        $id            = Request::param('id',0);

        $RaffleService = new SignInService();
        $data          = $RaffleService->deleteSignInGift($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }
}