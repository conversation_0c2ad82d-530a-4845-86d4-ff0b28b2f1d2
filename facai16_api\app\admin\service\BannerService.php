<?php
namespace app\admin\service;

use app\common\repository\BannerRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

class BannerService
{

    /**
     * 文章列表
     * @return array
     */
    public function getBannerLists(): array
    {
        $BannerRepo  = new BannerRepository();
        $data        = $BannerRepo->paginates();

        return Result::success($data);
    }

    /**
     * 文章信息
     * @param $id
     * @return array
     */
    public function getBannerInfo($id): array
    {
        $BannerRepo  = new BannerRepository();
        $data        = $BannerRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加文章
     * @param $params
     * @return array
     */
    public function addBanner($params): array
    {
        $BannerRepo  = new BannerRepository();
        $res         = $BannerRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新文章
     * @param $params
     * @return array
     */
    public function updateBanner($params): array
    {
        $BannerRepo  = new BannerRepository();
        $res         = $BannerRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除文章
     * @param $id
     * @return array
     */
    public function deleteBanner($id): array
    {
        $BannerRepo  = new BannerRepository();
        $res         = $BannerRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


}