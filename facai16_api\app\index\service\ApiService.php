<?php
namespace app\index\service;

use app\common\repository\BannerRepository;
use app\common\repository\LevelRepository;
use app\common\repository\QuestionRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\UserRepository;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Filesystem;


/**
 * api 服务
 */
class ApiService
{

    /**
     * 公告
     * @return array
     */
    public function notice(): array
    {
        $SystemSetRepo      = new SystemSetRepository();
        $notice             = $SystemSetRepo->valueByCondition(['key' => 'notice'],'val');
        $data['notice']     = $notice;

        return Result::success($data);
    }

    /**
     * 客服
     * @return array
     */
    public function customer(): array
    {
        $data               = [];

        $SystemSetRepo      = new SystemSetRepository();

        $UserRepo           = new UserRepository();

        $keFu               = $SystemSetRepo->valueByCondition(['key' => 'kefu'],'val');

        $user               = $UserRepo->userByHeader();

        if($user)
        {
            $keFu .= '?name=' . $user['phone'] . '@' . urlencode($user['username']);
        }

        $data['src'] = $keFu;

        return Result::success($data);
    }



    /**
     * 轮播图详情
     * @return array
     */
    public function banner(): array
    {
        $BannerRepo     = new BannerRepository();
        $data           = $BannerRepo->selectByCondition();

        foreach ($data as &$v)
        {
            $v['img']= Uri::file($v['img']);
        }

        return Result::success($data);
    }


    /**
     * 用户等级
     * @return array
     */
    public function levels(): array
    {
        $LevelRepo = new LevelRepository();
        $data      = $LevelRepo->selectByCondition();
        return Result::success($data);
    }

    /**
     * 设置
     * @param string $key
     * @return array
     */
    public function setting(string $key): array
    {
        $SystemSetRepo   = new SystemSetRepository();
        $where           = [];
        $where[]         = ['key','=', $key];
        $data            = $SystemSetRepo->valueByCondition($where,'val');

        return  Result::success(['val' => $data]);
    }


    /**
     * 上传
     * @param $file
     * @return array
     */
    public function upload($file): array
    {
        $info           = Filesystem::putFile('', $file);

        $info           = str_replace('\\','/',$info);

        $info           = '/upload/' . $info;

        return  Result::success(['url' => $info]);
    }

    /**
     * 问答列表
     * @return array
     */
    public function question(): array
    {
        $QuestionRepo = new QuestionRepository();
        $data          = $QuestionRepo->selectByCondition();
        return  Result::success($data);
    }
}