<?php
namespace app\admin\controller;

use app\admin\service\MoneyLogService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 用户流水
 */
class MoneyLog
{

    /**
     * 流水记录
     * @return Json
     */
    public function getMoneyLogLists(): Json
    {
        $params             = Request::only([
            'starttime',
            'endtime',
            'username',
            'phone',
            'order_id',
            'class_id'
        ]);


        $MoneyLogService   = new MoneyLogService();
        $data              = $MoneyLogService->getMoneyLogLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加金额
     * @return Json
     */
    public function addMoney(): Json
    {
        $params             = Request::only([
            'uid',
            'money',
            'class_id'
        ]);

        $MoneyLogService   = new MoneyLogService();
        $data              = $MoneyLogService->addMoney($params['uid'], $params['money'], $params['class_id']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 类型
     * @return Json
     */
    public function classes(): Json
    {
        $MoneyLogService   = new MoneyLogService();
        $data              = $MoneyLogService->classes();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}