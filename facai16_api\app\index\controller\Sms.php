<?php
namespace app\index\controller;

use app\common\utils\Ajax;
use app\index\service\SmsService;
use think\facade\Request;
use think\response\Json;

/**
 * 短信服务
 * Class SmsScene
 * @package app\index\controller
 */
class Sms
{
    /**
     * 注册
     * @return Json
     */
    public function register(): Json
    {
        $params          = Request::only([
            'phone'    => '',
            'code'     => '',
            'captcha'  => ''
        ]);

        $SmsSceneService = new SmsService();

        $result          = $SmsSceneService->register($params['phone']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 充值
     * @return Json
     */
    public function reset() : Json
    {
        $params          = Request::only([
            'phone'    => '',
            'code'     => '',
            'captcha'  => ''
        ]);

        $SmsSceneService = new SmsService();

        $result          = $SmsSceneService->reset($params['phone']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

}
