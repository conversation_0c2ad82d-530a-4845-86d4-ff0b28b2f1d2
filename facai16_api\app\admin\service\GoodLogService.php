<?php
namespace app\admin\service;

use app\common\repository\GoodsLogRepository;
use app\common\repository\GoodsRepository;
use app\common\repository\UserAddressRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\repository\WithdrawRepository;
use app\common\utils\Arrays;
use app\common\utils\Excel;
use app\common\utils\Order;
use app\common\utils\Result;
use app\common\utils\Uri;
use think\facade\Request;

/**
 * 商品记录
 */
class GoodLogService
{
    /**
     * 商品记录
     * @param $params
     * @return array
     */
    public function getGoodLogLists($params): array
    {

        $where          = [];

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }


        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }


        $GoodLogRepo  = new GoodsLogRepository();
        $data         = $GoodLogRepo->paginates($where);

        return Result::success($data);
    }

    /**
     * @param $params
     * @return array
     */
    public function export($params): array
    {
        $where          = [];

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['order_no']))
        {
            $where[] = ['order_no', '=', $params['order_no']];
        }

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }


        $GoodLogRepo  = new GoodsLogRepository();

        $list         = $GoodLogRepo->limits($where,'*', ['id' => 'desc'],0,10000);

        $fileName     = Excel::createCsvFile('购买记录' . Date('Y-m-d'), 'goods');


        foreach ($list as $row)
        {
            if ($row['status'] == 0) {
                $row['status'] = '待发货';
            } elseif ($row['status'] == 1)
            {
                $row['status'] = '已发货';
            }

            if ($row['is_test'] == 0) {
                $row['is_test'] = '正常';
            } elseif ($row['is_test'] == 1)
            {
                $row['is_test'] = '测试';
            }

            $row['deliver_time']    = date('Y-m-d H:i:s', $row['deliver_time']);

            Excel::formatDataAppend($row,'goods', $fileName);
        }

        $data = [
            'url' => Uri::file('/' . $fileName)
        ];

        return Result::success($data);

    }

    /**
     * 获取商品记录
     * @param $id
     * @return array
     */
    public function getGoodLogInfo($id): array
    {
        $GoodLogRepo  = new GoodsLogRepository();
        $data         = $GoodLogRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 删除商品记录
     * @param $id
     * @return array
     */
    public function deleteGoodLog($id): array
    {
        $GoodLogRepo  = new GoodsLogRepository();
        $res          = $GoodLogRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail('删除失败');
        }

        return Result::success();
    }

    /**
     * 添加记录
     * @param $param
     * @return array
     */
    public function addGoodLog($param): array
    {
        $UserRepo = new UserRepository();
        $user     = $UserRepo->findByCondition(['phone' => $param['phone']]);

        if (!$user)
        {
            return Result::fail('用户信息不能为空');
        }

        $UserAddressRepo = new UserAddressRepository();
        $address         = $UserAddressRepo->findByCondition(['uid' => $user['id']]);

        if (!$address)
        {
            return Result::fail('地址不能为空');
        }

        $GoodsRepo = new GoodsRepository();
        $goods     = $GoodsRepo->findById($param['goods_id']);

        if (!$goods)
        {
            return Result::fail('为空');
        }

        $UserStateRepo = new UserStateRepository();
        $userState     = $UserStateRepo->findByCondition(['uid' => $user['id']]);

        $update  = [
            'uid'               => $user['id'],
            'phone'             => $user['phone'],
            'username'          => $user['username'],
            'is_test'           => $userState['is_test'],
            'img'               => $goods['img'],
            'goods_id'          => $goods['id'],
            'goods_title'       => $goods['title'],
            'order_no'          => Order::uniqueNo(),
            'money'             => $goods['price'],
            'status'            => 2,
            'address_name'      => $address['address_name'],
            'address_phone'     => $address['address_phone'],
            'address_city'      => $address['address_city'],
            'address_place'     => $address['address_place'],
            'create_time'       => Request::time(),
            'update_time'       => Request::time(),
            'deliver_title'     => $param['deliver_title'],
            'deliver_order_no'  => $param['deliver_order_no'],
//            'deliver_time'      => strtotime($param['deliver_time']),
        ];

        $GoodsLogRepo = new GoodsLogRepository();
        $res          = $GoodsLogRepo->inserts($update);


        if (!$res)
        {
            return Result::fail('保存失败');
        }


        return Result::success();
    }



    public function updateGoodLog($params): array
    {
        $update  = [
            'status'            => $params['status'],
            'address_name'      => $params['address_name'],
            'address_phone'     => $params['address_phone'],
            'address_city'      => $params['address_city'],
            'address_place'     => $params['address_place'],
            'update_time'       => Request::time(),
            'deliver_title'     => $params['deliver_title'],
            'deliver_order_no'  => $params['deliver_order_no'],
            'deliver_time'      => strtotime($params['deliver_time']),
        ];

        $GoodsLogRepo = new GoodsLogRepository();
        $res          = $GoodsLogRepo->updateByCondition(['id' => $params['id']],$update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }


        return Result::success();
    }

}
