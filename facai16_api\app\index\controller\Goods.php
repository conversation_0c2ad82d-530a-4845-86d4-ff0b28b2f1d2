<?php
namespace app\index\controller;

use app\common\cache\RedisLock;
use app\common\repository\UserRepository;
use app\common\utils\Record;
use app\index\service\GoodsService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;


/**
 * 商品
 * Class Shop
 * @package app\api\controller
 */
class Goods
{

    /**
     * 商品列表
     * @return Json
     */
    public function lists(): Json
    {
        $params          = Request::only(['type' => 0 , 'limit' => 10]);

        $GoodsService    = new GoodsService();
        $result          = $GoodsService->lists($params['type'], $params['limit']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 拼团详情
     * @return Json
     */
    public function info(): Json
    {
        $params          = Request::only(['id' => 0]);
        $GoodsService    = new GoodsService();
        $result          = $GoodsService->info($params['id']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 参与拼团
     * @return Json
     */
    public function pay(): Json
    {
        $params        = Request::only(['id' => 0]);

        $UserRepo      = new UserRepository();
        $uid           = $UserRepo->userByHeader('id');
        $RedisLock     = new RedisLock();
        $status        = $RedisLock->lock('GoodsPay:' . $uid);


        try {

            if (empty($status))
            {
                return Ajax::fail('请不要重复提交');
            }

            $GoodsService    = new GoodsService();
            //余额
            $result          = $GoodsService->pay($params['id']);

            $RedisLock->unLock('GoodsPay:' . $uid);

            return Ajax::message($result['code'], $result['msg'], $result['data']);

        }catch (\Exception $e)
        {

            $RedisLock->unLock('GoodsPay:' . $uid);

            return Ajax::fail(Record::exception('http', $e));
        }

    }

    /**
     * 拼团记录
     * @return Json
     */
    public function record(): Json
    {
        $params          = Request::only(['limit' => 10]);

        $GoodsService    = new GoodsService();

        $result          = $GoodsService->record($params['limit']);

        return Ajax::message($result['code'], $result['msg'], $result['data']);
    }

}