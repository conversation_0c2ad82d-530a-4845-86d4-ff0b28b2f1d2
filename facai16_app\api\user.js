// 引用网络请求中间件
import {
	getUserId
} from "@/utils/request/auth.js"
import request, {
	get,
	post,
	uploadFile
} from "../utils/request/request"

export function getUserInfo() {
	return request({
		url: `/user/info`,
		method: 'GET',
	})
}

export function getUserMoneyDetail() {
	return request({
		url: `/user/detail`,
		method: 'GET',
	})
}

export function getBankList() {
	return request({
		url: `/bank/lists`,
		method: 'POST',
	})
}

export function realNameAuth(data) {
	return request({
		url: `/user/realName`,
		method: 'POST',
		data
	})
}

export function bankInCheck() {
	return request({
		url: `/bank/bankInCheck`,
		method: 'GET',
	})
}

export function addBank(data) {
	return request({
		url: `/bank/add`,
		method: 'POST',
		data
	})
}

export function deleteBank(data) {
	return request({
		url: `/bank/delete`,
		method: 'POST',
		data
	})
}

export function getAddressList() {
	return request({
		url: `/address/lists`,
		method: 'POST',
	})
}

export function getAddressInfo() {
	return request({
		url: `/address/info`,
		method: 'POST',
	})
}

export function addAdress(data) {
	return request({
		url: `/address/add`,
		method: 'POST',
		data
	})
}

export function editAdress(data) {
	return request({
		url: `/address/edit`,
		method: 'POST',
		data
	})
}

export function delAddressList() {
	return request({
		url: `/address/delete`,
		method: 'POST',
	})
}

export function getTeamInfo() {
	return request({
		url: `/team/info`,
		method: 'POST',
	})
}

export function getTeamList(data) {
	return request({
		url: `/Team/lists`,
		method: 'POST',
		data
	})
}

export function getPrizes() {
	return request({
		url: `/raffle/lists`,
		method: 'GET',

	})
}

export function getSetting(data) {
	return request({
		url: `/api/setting`,
		method: 'GET',
		data
	})
}

export function getArticleInfo(data) {
	return request({
		url: `/article/info`,
		method: 'GET',
		data
	})
}

export function getRaffleDraw() {
	return request({
		url: `/raffle/draw`,
		method: 'GET',
	})
}

export function drawRecord(data) {
	return request({
		url: `/raffle/record`,
		method: 'GET',
		data
	})
}

export function signin() {
	return request({
		url: `/signIn/sign`,
		method: 'GET',
	})
}

export function signRecord() {
	return request({
		url: `/signIn/record`,
		method: 'GET',
	})
}

export function question() {
	return request({
		url: `/api/question`,
		method: 'GET',
	})
}

export function getItemNotFinishedSum() {
	return request({
		url: `/Statistics/itemNotFinishedSum`,
		method: 'GET',
	})
}
export function getIncomeTotalSum() {
	return request({
		url: `/Statistics/incomeTotalSum`,
		method: 'GET',
	})
}
export function getTodayTeamTotalSum() {
	return request({
		url: `/Statistics/todayTeamTotalSum`,
		method: 'GET',
	})
}
export function getTodayIncomeSum() {
	return request({
		url: `/Statistics/todayIncomeSum`,
		method: 'GET',
	})
}
export function uploadImage(filePath, path) {
	return uploadFile({
		url: `/upload/add`,
		method: 'POST',
		data: filePath,
		path: path,
	})
}

export function postRecharge(data) {
	return post('/recharge/recharge', data);
}

export function walletTransfer(data) {
	return post('/rebate/wallet/toTransferIntoOrTransferOut', data);
}
export function getRebateWallet() {
	return get('/rebate/wallet/getRebateWallet');
}
export function getRebateWalletDetailPage(data) {
	return post('/rebate/wallet/getBalanceDetailPage', data)
}

//资金明细

export function getBalanceDetail(page, pageSize) {
	return get(`/user/getBalanceDetail/${page}/${pageSize}`)
}

// 签到信息
export function getUserSignInInfo(data) {
	return post('/sign/getUserSignInInfo', data);
}

//签到
export function signIn() {
	return post('/sign/signIn')
}
// 提现明细

export function getWithdrawalList(page, pageSize) {
	return get(`/withdrawal/getUserWithdrawalInfos/${page}/${pageSize}`)
}

/**
 * 更新用户信息
 * @param {Object} data
 */
export function updateUserInfo(data) {
	return request({
		url: `/user/updateUserInfo`,
		method: 'POST',
		data,
	})
}
// 抽奖
export function prizeDraw() {
	return get('/user/prizeDraw')
}
// 获取抽奖次数
export function getPrizeDrawCount() {
	return get('/user/getPrizeDrawCount')
}
// 获取奖品信息

export function getLotteryConfig() {
	return get('/user/getLotteryConfig')
}

// 最新的通知
export function newNotice() {
	return get('/notice/getNotice')
}

export function noticeList(data) {
	return post('/notice/noticePage', data)
}

// 
export function updateNotice(data) {
	return post('')
}
//资讯列表
export function getArticleList(page, pageSize, type) {
	return get(`/article/getPageByType/${page}/${pageSize}/${type}`);
}
//咨询详情
export function getArticle(id) {
	return get(`/article/getArticle/${id}`);
}
//好友信息
export function getUserFriendInfo() {
	return get('/user/getUserFriendInfo')
}
// 获取好友列表
export function getUserFriendList(data) {
	return post('/user/getUserFriendList', data);
}
//团队信息
export function getUserTeamInfo() {
	return get('/user/getUserTeamInfo')
}
// 系统配置参数
export function getConfigList() {
	return get('/config/getConfigList')
}
// 获取logo
export function getLogoInfo() {
	return get('/config/getPlatformInfo')
}

// 获取好友资金明细列表
export function getFriendAmountDetailList(data) {
	return post('/user/getFriendAmountDetailList', data);
}
export function checkUpgrade() {
	const sysInfo = uni.getSystemInfoSync();
	let data = {
		version: "1.9.0",
		platform: sysInfo.platform
	}
	return post('/upgrade/checkUpgrade', data);
}