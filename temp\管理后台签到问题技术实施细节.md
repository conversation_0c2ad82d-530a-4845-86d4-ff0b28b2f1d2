# 签到异常问题技术实施细节

## 📋 实施概览

**问题编号**: 50801  
**实施日期**: 2025-01-08  
**实施人员**: 技术团队  
**实施方式**: 数据库结构调整 + 代码逻辑修复  

## 🗄️ 数据库变更详情

### 表结构变更

#### user表字段添加
```sql
ALTER TABLE `user` 
ADD COLUMN `signin_time` INT(11) NOT NULL DEFAULT '0' COMMENT '最后签到时间' AFTER `login_time`;
```

**字段说明**:
- **字段名**: `signin_time`
- **数据类型**: `INT(11)`
- **默认值**: `0`
- **是否为空**: `NOT NULL`
- **注释**: `最后签到时间`
- **位置**: 在`login_time`字段之后

#### 字段设计考虑
1. **数据类型选择**: 使用INT存储UNIX时间戳，便于时间计算和比较
2. **默认值设计**: 0表示从未签到，便于区分历史状态
3. **位置安排**: 放在login_time之后，保持时间相关字段的聚集性

### 数据迁移脚本

#### 历史数据迁移
```sql
-- 为现有用户根据sign_in表的最新记录更新signin_time字段
-- 同时考虑个人签到（is_team = 0）和团队签到（is_team = 1），取最新的签到时间
UPDATE `user` u 
SET signin_time = (
    SELECT MAX(create_time) 
    FROM sign_in s 
    WHERE s.uid = u.id
) 
WHERE EXISTS (
    SELECT 1 FROM sign_in s 
    WHERE s.uid = u.id
);
```

**迁移逻辑说明**:
1. 从`sign_in`表中查找每个用户的最新签到时间
2. 包含个人签到和团队签到记录
3. 只更新有签到记录的用户
4. 无签到记录的用户保持默认值0

#### 数据验证查询
```sql
-- 验证迁移结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN signin_time > 0 THEN 1 END) as users_with_signin_time,
    COUNT(CASE WHEN signin_time = 0 THEN 1 END) as users_without_signin_time
FROM `user`;
```

## 💻 代码变更详情

### 签到业务逻辑修改

#### 文件: `facai7_api/app/index/service/SignInService.php`

**个人签到方法 (sign()) 修改**:

**修改位置**: 第168-188行
```php
// 原有代码
$res = $UserInfoRepo->statistic($user['id'],['signin_num' => 1, 'signin_money' => $signInMoney]);

if ($res['code'])
{
    Db::rollback();
    return Result::fail($res['msg']);
}

// 新增代码：更新用户表的签到时间
$updateResult = $UserRepo->updateByCondition(
    ['id' => $user['id']], 
    ['signin_time' => Request::time()]
);

if (!$updateResult) {
    Db::rollback();
    return Result::fail('更新签到时间失败');
}

// 提交事务
Db::commit();
```

**团队签到方法 (signTeam()) 修改**:

**修改位置**: 第277-297行
```php
// 原有代码
$res = $UserInfoRepo->statistic($user['id'],['signin_num' => 1, 'signin_money' => $signInMoney]);

if ($res['code'])
{
    Db::rollback();
    return Result::fail($res['msg']);
}

// 新增代码：更新用户表的签到时间
$updateResult = $UserRepo->updateByCondition(
    ['id' => $user['id']], 
    ['signin_time' => Request::time()]
);

if (!$updateResult) {
    Db::rollback();
    return Result::fail('更新签到时间失败');
}

// 提交事务
Db::commit();
```

### 查询逻辑修复

#### 文件: `facai7_api/app/admin/service/UserService.php`

**修改位置**: 第113-124行

**修复前的错误代码**:
```php
if (isset($params['is_signin']) && is_numeric($params['is_signin']))
{
    $where[] = ['ur.signin_time', '>=', strtotime(date('Y-m-d 00:00:00'))];
}
```

**修复后的正确代码**:
```php
if (isset($params['is_signin']) && is_numeric($params['is_signin']))
{
    $todayStart = strtotime(date('Y-m-d 00:00:00'));
    
    if ($params['is_signin'] == 1) {
        // 查询今日已签到用户
        $where[] = ['ur.signin_time', '>=', $todayStart];
    } else {
        // 查询今日未签到用户
        $where[] = ['ur.signin_time', '<', $todayStart];
    }
}
```

**修复说明**:
1. **条件分支**: 根据`is_signin`参数值执行不同的查询条件
2. **时间计算**: 提取今日开始时间戳，避免重复计算
3. **逻辑完整**: 覆盖"是"(1)和"否"(0)两种查询场景

## 🔧 前端参数映射

### booleanEnums定义
**文件**: `facai7_admin/src/config/enums.js`

```javascript
export const booleanEnums = [
  {
    label: "是",
    value: 1,
  },
  {
    label: "否",
    value: 0,
  },
];
```

### 参数传递流程
1. **前端选择**: 用户在下拉框中选择"是"或"否"
2. **参数绑定**: `v-model="searchForm.is_signin"`
3. **API调用**: 通过`/user/getUserLists`接口传递参数
4. **后端接收**: `Request::only(['is_signin', ...])`
5. **业务处理**: UserService中的查询逻辑处理

## 🧪 测试验证方案

### 自动化测试脚本

#### 数据库层面验证
```sql
-- 检查字段是否添加成功
DESCRIBE user;

-- 检查数据迁移是否正确
SELECT 
    u.username,
    u.signin_time,
    FROM_UNIXTIME(u.signin_time) as signin_datetime,
    s.max_signin_time,
    FROM_UNIXTIME(s.max_signin_time) as expected_datetime
FROM `user` u
LEFT JOIN (
    SELECT uid, MAX(create_time) as max_signin_time
    FROM sign_in 
    GROUP BY uid
) s ON u.id = s.uid
WHERE u.signin_time != IFNULL(s.max_signin_time, 0)
LIMIT 5;
```

#### 功能层面验证
```sql
-- 模拟is_signin=1查询（今日已签到）
SELECT COUNT(*) as signed_users
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE());

-- 模拟is_signin=0查询（今日未签到）
SELECT COUNT(*) as unsigned_users
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE());
```

### 手动测试用例

#### 测试数据准备
```sql
-- 创建测试用户和签到记录
INSERT INTO `user` (username, phone, ...) VALUES (...);
INSERT INTO `sign_in` (uid, username, create_time, ...) VALUES (...);
```

#### 测试场景
1. **签到功能测试**:
   - 个人签到后检查signin_time是否更新
   - 团队签到后检查signin_time是否更新

2. **查询功能测试**:
   - 选择"是否签到"="是"，验证结果
   - 选择"是否签到"="否"，验证结果
   - 不选择"是否签到"，验证显示所有用户

## 📊 性能影响分析

### 数据库性能
1. **字段添加**: 对现有查询性能无影响
2. **索引考虑**: signin_time字段暂不需要单独索引，查询频率不高
3. **存储开销**: 每个用户增加4字节存储空间

### 应用性能
1. **签到功能**: 增加一次UPDATE操作，性能影响微乎其微
2. **查询功能**: 简化了查询逻辑，性能略有提升
3. **内存使用**: 无明显变化

## 🔄 回滚方案

### 数据库回滚
```sql
-- 删除添加的字段
ALTER TABLE `user` DROP COLUMN `signin_time`;
```

### 代码回滚
```bash
# 恢复原始文件
git checkout HEAD~1 -- facai7_api/app/index/service/SignInService.php
git checkout HEAD~1 -- facai7_api/app/admin/service/UserService.php
```

### 回滚风险评估
- **数据丢失风险**: 删除signin_time字段会丢失签到时间数据
- **功能影响**: 回滚后"是否签到"查询功能将恢复异常
- **建议**: 除非出现严重问题，否则不建议回滚

## 📝 部署检查清单

### 部署前检查
- [ ] 数据库备份完成
- [ ] 测试环境验证通过
- [ ] 代码审查完成
- [ ] 回滚方案准备就绪

### 部署步骤
1. [ ] 执行数据库结构更新脚本
2. [ ] 验证字段添加成功
3. [ ] 执行数据迁移脚本
4. [ ] 验证数据迁移正确
5. [ ] 部署代码更新
6. [ ] 清理应用缓存
7. [ ] 执行功能验证测试

### 部署后验证
- [ ] 签到功能正常
- [ ] 查询功能正常
- [ ] 性能无异常
- [ ] 错误日志无异常

---

**文档维护**: 技术团队  
**最后更新**: 2025-01-08  
**版本**: v1.0
