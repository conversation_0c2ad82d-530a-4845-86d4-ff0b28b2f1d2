<?php
namespace app\admin\service;

use app\common\repository\UserRepository;
use app\common\repository\UserWordsLogsRepository;
use app\common\repository\UserWordsRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;


/**
 * 集字
 */
class WordsService
{
    /**
     * 集字列表
     * @param $params
     * @return array
     */
    public function getWordsLists($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['username', '=', $params['username']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        if (!empty($params['is_test']))
        {
            $where[] = ['is_test', '=', $params['is_test']];
        }

        $UserWordsRepo = new UserWordsRepository();
        $data          = $UserWordsRepo->paginates($where,'*',$params['limit'],['id'=> 'desc']);

        return Result::success($data);
    }

    /**
     * 获取集字信息
     * @param $id
     * @return array
     */
    public function getWordsInfo($id): array
    {
        $UserWordsRepo = new UserWordsRepository();
        $data          = $UserWordsRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加集字
     * @param $params
     * @return array
     */
    public function addWords($params): array
    {
        $where    = [];
        $where[]  = ['phone','=', $params['phone'] ?? ''];

        $UserRepo = new UserRepository();
        $user     = $UserRepo->findByCondition($where);

        if (!$user)
        {
            return Result::fail('用户信息不能为空');
        }

        $params['uid']      = $user['id'];
        $params['username'] = $user['username'];
        $params['phone']    = $user['phone'];
        $params['is_test']  = $user['is_test'];


        $UserWordsRepo = new UserWordsRepository();

        $where    = [];
        $where[]  = ['phone','=', $params['phone'] ?? ''];
        $has      = $UserWordsRepo->findByCondition($where);

        if ($has)
        {
            return Result::fail('用户数据已经存在');
        }

        $res           = $UserWordsRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 更新集字
     * @param $params
     * @return array
     */
    public function updateWords($params): array
    {

        $UserRepo = new UserRepository();
        $user     = $UserRepo->findByCondition(['phone' => $params['phone'] ?? '']);

        if (!$user)
        {
            return Result::fail('用户信息不能为空');
        }

        $params['uid']         = $user['id'];
        $params['username']    = $user['username'];
        $params['phone']       = $user['phone'];
        $params['is_test']     = $user['is_test'];
        $params['update_time'] = Request::time();

        $UserWordsRepo = new UserWordsRepository();
        $res           = $UserWordsRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除集字
     * @param $id
     * @return array
     */
    public function deleteWords($id): array
    {
        $UserWordsRepo = new UserWordsRepository();
        $res           = $UserWordsRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户集字记录
     * @return array
     */
    public function getWordsLogLists(): array
    {
        $UserWordsLogsRepo = new UserWordsLogsRepository();
        $data              = $UserWordsLogsRepo->paginates();

        return Result::success($data);
    }


    /**
     * 删除集字记录
     * @param $id
     * @return array
     */
    public function deleteWordsLogLists($id): array
    {
        $UserWordsLogsRepo  = new UserWordsLogsRepository();
        $res                = $UserWordsLogsRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

}