<template>
	<view>
		<view class="page-login">
			<view class="fcc-h pt-108">
				<image src="/static/login/logo.png" mode="aspectFit" class="w-140 h-100 block"></image>
				<view class="t-48 fm lh-62 color-#fff">
					Mistral AI
				</view>
			</view>
			<view class="h-250"></view>

			<view class="mx-34 r-20 border-1 border-solid border-#fff bg-#fff bg-op-10 px-36">
				<view class="fc-bet h-105">
					<view class="tit">
						登录账号
					</view>
					<!-- <view class="fc" @click="$refs.popSelect.open()">
						<image src="/static/login/118.png" mode="aspectFit" class="block mr-12 size-32"></image>
						<text class="t-30 lh-42 color-#fff">
							{{list[index]}}
						</text>
					</view> -->
				</view>


				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-tel.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1  mr-20">
						<input v-model="phone" type="number" placeholder="请输入手机号" maxlength="11"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff DIN">
					</view>
				</view>

				<view class="r-20 input-row h-116 bg-#fff bg-op-30 fc-bet mb-32">
					<view class="w-100 fcc">
						<image src="/static/login/ipt-pwd.png" mode="aspectFit" class="block size-40"></image>
					</view>
					<view class="input- flex1 mr-20">
						<input type="safe-password" v-model="password" password placeholder="请输入密码"
							placeholder-style="color:#fff;font-size:31rpx" class="t-36 w-full lh-116  color-#fff">
					</view>
				</view>

				<view class="h-20"></view>


				<view class="btn-full fcc !r-20" @click="toLogin()">
					登录
				</view>

				<view class="py-36 fcc color-#fff t-26 lh-37">
					<navigator url="/pages/login/forgot" class="p-30">
						忘记密码？
					</navigator>
					<view class="w1 bg-#fff h-24 mx-40"></view>
					<text>
						<navigator url="/pages/login/reg" class="p-30">
							注册账号
						</navigator>
					</text>
				</view>

			</view>


			<!-- <view class="fcc-h py-30 color-#fff">
				<image src="/static/login/kefu.png" mode="aspectFit" class="block w-104 h-92"></image>
				<view class="mt-15 t-24 lh-33 pb-15">
					在线客服
				</view>
			</view> -->
			<view class="mt-38">
				<view class="fcc">
					<view @click="agree=!agree">
						<image v-if="agree" src="/static/check1-on.png" mode="aspectFit" size-32 block></image>
						<image v-else src="/static/check1.png" mode="aspectFit" size-32 block></image>
					</view>
					<view class="t-22 color-#fff ml-8">
						<text @click="agree=!agree">我已阅读并同意</text>
						<navigator url="/pages/setting/article" class="inline-block">
							<text class="color">
								《服务协议》
							</text>
							<text class="color">《隐私协议》</text>
						</navigator>
					</view>
				</view>
				<view class="h-30"></view>
				<view class="btn-area"></view>
			</view>
		</view>


		<pop-select ref="popSelect" title="切换语言" :list="list" @select="select"></pop-select>
	</view>
</template>

<script>
	import {
		login
	} from '@/api/auth.js'
	import {
		noNeedAuth
	} from '@/utils/router-mixin.js'
	export default {
		mixins: [noNeedAuth],
		data() {
			return {
				agree: false,
				index: 0,
				list: ['中文', 'English'],
				phone: '',
				password: '',
			};
		},
		methods: {
			select(e) {
				this.index = e
			},
			async toLogin() {
				let err = ''
				if (!this.phone || !/^\d{11}$/.test(this.phone)) {
					err = '请输入有效手机号'
				} else if (!this.password) {
					err = '请输入密码'
				} else if (!this.agree) {
					err = '请阅读并同意用户协议'
				}
				if (err) {
					uni.showToast({
						title: err,
						duration: 2000,
						icon: 'error'
					});
					return
				} else {
					try {
						uni.showLoading({
							title: '加载中',
						});
						const resData = await login({
							phone: this.phone,
							password: this.password
						})
						uni.setStorageSync("Accept-Token", resData.data.token);

						this.$store.dispatch('afterLogin');

						uni.navigateTo({
							url: '/pages/index/index'
						})
					} catch (e) {} finally {
						uni.hideLoading()
					}
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page-login {
		background: url(/static/login/bg.jpg) no-repeat;
		background-size: 100% auto;
	}

	.tit {
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 40rpx;
		color: #FFFFFF;
		line-height: 56rpx;
		text-shadow: 2rpx 2rpx 2rpx rgba(0, 0, 0, 0.8);
		text-align: right;
		font-style: normal;
	}

	.input-row {

		backdrop-filter: blur(29rpx);
	}
</style>