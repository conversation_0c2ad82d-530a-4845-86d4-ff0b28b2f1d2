<?php
namespace app\common\cache;

use think\facade\Cache;


/**
 * redis 锁
 * Class RedisLock
 * @package app\common\cache
 */
class RedisLock
{
    protected  $redis;

    public function __construct()
    {
        $this->redis =  Cache::store('redis')->handler();
    }

    /**
     * key
     * @param string $key
     * @return string
     */
    public  function key(string $key = ''): string
    {
        return 'lock:' . $key;
    }


    /**
     * 加锁
     */
    public  function lock(string $key = '', int $expires = 5): bool
    {
        $result = $this->redis->set($this->key($key), time() + $expires, ['ex' => $expires, 'nx']);

        //不能获取锁
        if (empty($result))
        {
            //锁已过期，删除锁，重新获取
            if (time() > $this->redis->get($this->key($key)))
            {
                //删除锁
                $this->unLock($this->key($key));

                $result = $this->redis->set($this->key($key), time() + $expires, ['ex' => $expires, 'nx']);
            }

        }

        return $result;
    }


    /**
     * 释放锁
     */
    public  function unLock(string $key = ''): bool
    {
        return $this->redis->del($this->key($key));
    }


    /**
     * 等锁
     * @param string $key
     * @param int $lockTime
     * @param int $timeout
     * @param float $checkInterval
     * @return bool
     */
    public  function wait(string $key = '', int $lockTime = 60, int $timeout = 10, float $checkInterval = 0.1): bool
    {
        // 当前时间
        $startTime = microtime(true);

        // 尝试获取锁
        while (true)
        {
            if ($this->lock($key, $lockTime))
            {
                $status = true;
                break;
            }

            // 检查是否超过锁定尝试的超时时间
            $currentTime = microtime(true);

            if (($currentTime - $startTime) > $timeout)
            {
                $status = false;
                break;
            }

            // 等待一段时间后重试 转换为微秒
            usleep($checkInterval * 1000000);
        }

        return $status;
    }
}