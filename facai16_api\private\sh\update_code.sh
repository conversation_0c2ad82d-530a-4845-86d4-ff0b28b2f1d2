#!/bin/bash

# 设置工作目录
WORK_DIR="/path/to/your/repo"
cd $WORK_DIR || { echo "目录不存在"; exit 1; }

# 拉取最新的代码
echo "拉取最新的代码..."
git pull origin main

# 检查是否有未提交的更改
if [[ $(git status --porcelain) ]]; then
    echo "存在未提交的更改，请先处理这些更改。"
    exit 1
fi

# 运行任何必要的命令（如 Composer、npm 等）
# echo "安装依赖..."
# composer install
# npm install

echo "代码更新完成！"


# chmod +x update_code.sh
# 0 2 * * * /path/to/your/update_code.sh