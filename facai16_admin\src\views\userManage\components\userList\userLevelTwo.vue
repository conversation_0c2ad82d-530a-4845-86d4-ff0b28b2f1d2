<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    :hideResetButton="true"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="searchList"
  >
    <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="手机号码"
          prop="phone"
          disabled
        />
      </el-form-item>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.level"
          placeholder="用户等级"
          prop="level"
          disabled
        />
      </el-form-item> -->
      <el-form-item>
        <el-input
          v-model="searchForm.invite"
          placeholder="请输入邀请码"
          prop="invite"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="注册开始时间"
            style="width: 100%"
            prop="starttime"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            prop="endtime"
            placeholder="注册结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="100" />
      <el-table-column prop="nickname" label="昵称" width="100" />
      <el-table-column prop="phone" label="手机号码" width="100" />
      <el-table-column prop="invite" label="邀请码" width="70" />
      <el-table-column prop="level_name" label="用户等级" width="70">
        <template #default="scope">
          {{ scope.row.level_name }}
        </template>
      </el-table-column>
      <el-table-column label="持有项目人数" prop="not_finish" width="180" />
      <el-table-column label="未完结项目" prop="item_not_finish" width="180" />
      <el-table-column prop="addtime" label="注册时间" width="280">
        <template #default="scope">
          注册时间：{{ scope.row.create_at }}
          <br />
          注册IP：{{ scope.row.register_ip }}
          <br />
          登录时间：{{ scope.row.login_time }}
          <br />
          登录地址：{{ scope.row.ip_address }}
        </template>
      </el-table-column>
      <el-table-column label="会员统计" width="180">
        <template #default="scope"> 余额：{{ scope.row.money }} </template>
      </el-table-column>
      <el-table-column label="充值金额" prop="infos.recharge_money" width="180" />
      <el-table-column label="提款金额" prop="infos.withdraw_money" width="180" />
      <el-table-column label="投资金额" prop="infos.invest_money" width="180" />
      <el-table-column label="收入" prop="infos.income_money" width="180" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { sfzStatusEnums } from "@/config/enums";
import { ElMessage, ElMessageBox } from "element-plus";

const searchForm = ref({
  phone: "",
  invite: "",
  starttime: "",
  endtime: "",
  level: 2,
});
const tableData = ref([]);

const props = defineProps(["item", 'level']);
const { proxy } = getCurrentInstance();

onMounted(() => {
  searchForm.value.phone = props.item.phone;
  searchForm.value.level = props.level;
  getList();
});

const searchList = () => {
  getList();
};

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    method: "get",
    url: "/user/getUserLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped>
.table-handle-td {
  display: flex;
  cursor: pointer;
  flex-wrap: wrap;
  button {
    margin-bottom: 10px;
    margin-left: 0;
    margin-right: 10px;
  }
}
</style>
