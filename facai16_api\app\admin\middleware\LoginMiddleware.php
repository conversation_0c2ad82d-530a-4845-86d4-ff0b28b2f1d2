<?php

namespace app\admin\middleware;

use app\common\repository\SystemUserRepository;
use Closure;
use think\exception\HttpResponseException;
use think\helper\Str;
use think\Request;
use think\Response;
use think\response\Html;


/**
 * 登入中间件
 * Class LoginMiddleware
 * @package app\admin\middleware
 */
class LoginMiddleware
{

    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next): Response
    {

        $reqUri    = \think\facade\Request::baseUrl();

        //白名单接口
        if(Str::contains($reqUri,  '/login/login'))
        {
            return $next($request);
        }

        //非名单接口
        if (empty((new SystemUserRepository())->userByHeader('id')))
        {
            $info     = json_encode(['code' => 444, '登录失效,请重新登录','data'=>[]],320);
            $type     = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);

            throw new HttpResponseException($response);
        }

        return $next($request);
    }

}