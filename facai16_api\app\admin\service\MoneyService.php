<?php
namespace app\admin\service;

use app\common\repository\MoneyClassRepository;
use app\common\repository\MoneyRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;

/**
 * 钱
 */
class MoneyService
{
    public function getMoneyLists(): array
    {
        $MoneyRepo = new MoneyRepository();
        $data      = $MoneyRepo->paginates();

        return Result::success($data);
    }



    public function addMoney($params): array
    {
        $MoneyRepo = new MoneyRepository();
        $res       = $MoneyRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    public function getMoneyClassLists(): array
    {
        $MoneyRepo = new MoneyClassRepository();
        $data      = $MoneyRepo->selectByCondition();
        return Result::success($data);
    }

    public function getMoneyClassInfo($id): array
    {
        $MoneyRepo = new MoneyClassRepository();
        $data      = $MoneyRepo->findById($id);
        return Result::success($data);
    }


    public function addMoneyClass($params): array
    {
        $MoneyRepo = new MoneyClassRepository();
        $res       = $MoneyRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    public function updateMoneyClass($params): array
    {
        $MoneyRepo = new MoneyClassRepository();

        $res       = $MoneyRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


}
