<?php

namespace app\admin\service;

use app\common\repository\GiveProductLogRepository;
use app\common\utils\Result;
use think\facade\Request;

/**
 * 商品记录
 */
class GiveProductLogService
{
    public function records($params): array
    {
        $where = [];
        $params['phone'] && $where[] = ['phone', '=', $params['phone']];
        $params['username'] && $where[] = ['username', '=', $params['username']];
        $params['order_no'] && $where[] = ['order_no', '=', $params['order_no']];
        $params['item_name'] && $where[] = ['item_name', '=', $params['item_name']];
        $params['item_id'] && $where[] = ['item_id', '=', $params['item_id']];
        $params['status'] && $where[] = ['status', '=', $params['status']];

        $params['is_test'] && is_numeric($params['is_test']) && $where[] = ['is_test', '=', $params['is_test']];
        $GiveProductLogRepo = new GiveProductLogRepository();


        $data = $GiveProductLogRepo->paginates($where);

        return Result::success($data);

    }

    public function update($params): array
    {

//        $update = array_filter([
//
//            'phone' => $params['phone'],
//            'status' => $params['status'],
//            'username' => $params['username'],
//            'address_name' => $params['address_name'],
//            'address_phone' => $params['address_phone'],
//            'address_city' => $params['address_city'],
//            'address_place' => $params['address_place'],
//            'deliver_title' => $params['deliver_title'],
//            'deliver_order_no' => $params['deliver_order_no'],
//            'deliver_time' => strtotime($params['deliver_time']),
//        ]);
        $update = array_filter($params);
        if(!empty($params['status'])){
            $update['status'] = $params['status'];
        }

        $GoodsLogRepo = new GiveProductLogRepository();
        $res = $GoodsLogRepo->updateById($params['id'], $update);

        if (!$res) {
            return Result::fail('保存失败');
        }


        return Result::success();
    }

    public function del($id): array
    {
        $GoodLogRepo = new GiveProductLogRepository();
        $res = $GoodLogRepo->deleteById($id);

        if (!$res) {
            return Result::fail('删除失败');
        }

        return Result::success();
    }

}