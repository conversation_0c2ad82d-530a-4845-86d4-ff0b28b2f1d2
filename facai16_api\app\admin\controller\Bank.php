<?php
namespace app\admin\controller;

use app\admin\service\BankService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 银行
 */
class Bank
{
    /**
     * 获取银行列表
     * @return Json
     */
    public function getBankList(): Json
    {
        $BankService = new BankService();
        $data        = $BankService->getBankList();
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 获取银行信息
     * @return Json
     */
    public function getBankInfo(): Json
    {
        $params = Request::only([
            'id',
        ]);

        $BankService = new BankService();
        $data        = $BankService->getBankInfo($params['id']);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 添加银行
     * @return Json
     */
    public function addBank(): Json
    {
        $params = Request::only([
           'title',
           'img'
        ]);

        $BankService = new BankService();
        $data        = $BankService->addBank($params);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 更新银行
     * @return Json
     */
    public function updateBank(): Json
    {
        $params = Request::only([
            'title',
            'img',
            'id'
        ]);

        $BankService = new BankService();
        $data        = $BankService->updateBank($params);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 删除银行
     * @return Json
     */
    public function deleteBank(): Json
    {
        $params = Request::only([
            'id'
        ]);

        $BankService = new BankService();
        $data        = $BankService->deleteBank($params);

        return Ajax::message($data['code'], $data['msg'], $data['data']);
    }
}