<?php
declare (strict_types = 1);

namespace app\common\command;


use app\common\model\MoneyClass;
use app\common\repository\MoneyLogRepository;
use app\common\repository\TeamGiftRepository;
use app\common\repository\TeamLogRepository;
use app\common\repository\TeamRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\utils\BCalculator;
use app\common\utils\Record;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\facade\Request;

/**
 * 用户升级等级
 */
class TeamLeveLUp extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('UserLeveLUp')->setDescription('the UserLeveLUp command');

    }


    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function execute(Input $input, Output $output)
    {

        $UserRepo  = new UserRepository();

        $UserRepo->where('id','>',0)->chunk(100, function ($member)
        {
            $MoneyLogRepo   = new MoneyLogRepository();
            $UserRepo       = new UserRepository();
            $TeamRepo       = new TeamRepository();

            foreach ($member as $value)
            {
                $where            = [];
                $where[]          = ['top_id', '=', $value['id']];
                $where[]          = ['level', '<=', 99];

                $UserRelationRepo = new UserRelationRepository();
                $members          = $UserRelationRepo->selectByCondition($where,'uid');

                if (empty($members))
                {
                    continue;
                }

                $members          = array_column($members, 'uid');
                $history          = strtotime(date('Y-m-d 00:00:00', 1));
                $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day', Request::time())));
                $UserInfoRepo     = new UserInfoRepository();

                //1总充值
                $recharge         = empty($members) ? 0 : $UserInfoRepo->totalRecharge($history, $tomorrow,'', $members);

                //2总提现
                $withdraw         = empty($members) ? 0 : $UserInfoRepo->totalWithdraw($history, $tomorrow,'', $members);

                $yeji       = $recharge - $withdraw;
                $where      = [];
                $where[]    = ['v1_id', '=', $value['id']];
                $team       = $UserRepo->countByCondition($where);

                $where      = [];
                $where[]    = ['id', '>', $value['level_team']];
                $levels     = $TeamRepo->findByCondition($where,'*',['id' => 'ASC']);

                if (empty($levels))
                {
                    continue;
                }

                if ($yeji >= $levels['invest'] && $team >= $levels['user'])
                {

                    Db::startTrans();

                    try {

                        $update = [
                            'level_team'         => $levels['id'],
                            'level_team_name'    => $levels['title'],
                            'update_time'        => time()
                        ];

                        $res          = $UserRepo->updateById($value['id'], $update);

                        if (!$res)
                        {
                            Db::rollback();
                            continue;
                        }


                        $TeamLogRepo = new TeamLogRepository();

                        $teamName   = empty($value['level_team']) ? '普通会员' : $value['level_team_name'];

                        $insert  = [
                            'uid'           => $value['id'],
                            'username'      => $value['username'],
                            'phone'         => $value['phone'],
                            'is_test'       => $value['is_test'],
                            'lv_id'         => $levels['id'],
                            'lv_name'       => $levels['title'],
                            'desc'          => '从' .$teamName . '升级到' . $levels['title'],
                            'create_time'   => time(),
                            'update_time'   => time()
                        ];


                        $res = $TeamLogRepo->inserts($insert);

                        if (!$res)
                        {
                            Db::rollback();
                            continue;
                        }

                        $TeamGiftRepo = new TeamGiftRepository();


                        $where        = [];
                        $where[]      = ['uid', '=', $value['id']];
                        $where[]      = ['team_id', '=', $levels['id']];
                        $has          = $TeamGiftRepo->findByCondition($where);

                        if (empty($has))
                        {
                            $bonus   = BCalculator::calc($yeji)->mul($levels['commission'])->div(100)->result();
                            $res     = $MoneyLogRepo->fund($value['id'], (float) $bonus,MoneyClass::LEADER_UPGRADE_REWARDS,'1','团队长业绩提成奖励: + ' . $bonus .'元');

                            if ($res['code'])
                            {
                                Db::rollback();
                                continue;
                            }

                            $insert = [
                                'uid'           => $value['id'],
                                'username'      => $value['username'],
                                'phone'         => $value['phone'],
                                'is_test'       => $value['is_test'],
                                'team_id'       => $levels['id'],
                                'team_name'     => $levels['title'],
                                'gift_time'     => time(),
                                'bonus'         => $bonus,
                                'status'        => 1,
                                'create_time'   => time(),
                                'update_time'   => time()
                            ];

                            $res = $TeamGiftRepo->inserts($insert);

                            if (!$res)
                            {
                                Db::rollback();
                                continue;
                            }

                        }

                        // 提交事务
                        Db::commit();

                    } catch (\Exception $exception)
                    {
                        // 回滚事务
                        Db::rollback();

                        Record::exception('job', $exception);
                    }

                }


            }

        });


    }


}
