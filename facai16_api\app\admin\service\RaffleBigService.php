<?php
namespace app\admin\service;

use app\common\repository\RaffleBigLogRepository;
use app\common\repository\RaffleBigRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use think\facade\Request;


/**
 * 抽大奖
 */
class RaffleBigService
{

    /**
     * 奖品列表
     * @return array
     */
    public function getRaffleLists(): array
    {
        $RaffleRepo = new RaffleBigRepository();
        $data       = $RaffleRepo->paginates([],'*',10,['id'=> 'desc']);

        return Result::success($data);
    }


    /**
     * 获取奖品信息
     * @param $id
     * @return array
     */
    public function getRaffleInfo($id): array
    {
        $RaffleRepo = new RaffleBigRepository();
        $data       = $RaffleRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 添加奖品
     * @param $params
     * @return array
     */
    public function addRaffle($params): array
    {
        $RaffleRepo = new RaffleBigRepository();
        $res        = $RaffleRepo->inserts($params);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }

    /**
     * 更新奖品
     * @param $params
     * @return array
     */
    public function updateRaffle($params): array
    {

        $params['update_time'] = Request::time();

        $RaffleRepo = new RaffleBigRepository();
        $res        = $RaffleRepo->updateById($params['id'], Arrays::withOut($params,['id']));

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除奖品
     * @param $id
     * @return array
     */
    public function deleteRaffle($id): array
    {
        $RaffleRepo = new RaffleBigRepository();

        $res        = $RaffleRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户抽奖记录
     * @return array
     */
    public function getRaffleLogLists(): array
    {
        $RaffleRepo = new RaffleBigLogRepository();
        $data       = $RaffleRepo->paginates();
        return Result::success($data);
    }

    /**
     * 删除用户抽奖记录
     * @param $id
     * @return array
     */
    public function deleteRaffleLogLists($id): array
    {
        $RaffleRepo = new RaffleBigLogRepository();

        $res        = $RaffleRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


}