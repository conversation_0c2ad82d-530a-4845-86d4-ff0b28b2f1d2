<template>
  <adminTable
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
  <template v-slot:form-inline-items>
      <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.order_id"
          placeholder="请输入订单号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="uid" label="用户ID" width="130" />
      <el-table-column prop="username" label="用户名" width="160" />
      <el-table-column prop="phone" label="电话" width="130" />
      <el-table-column prop="order_id" label="订单ID" width="130" />
      <el-table-column prop="cycle_start" label="已开始周期" width="130" />
      <el-table-column prop="cycle_end" label="全部周期" width="130" />
      <el-table-column
        prop="profit_total"
        label="总收益全额（元）"
        width="130"
      />
      <el-table-column prop="profit_more" label="收益倍率加成" width="130" />
      <el-table-column prop="profit_now" label="收益全额（元）" width="130" />
      <el-table-column prop="profit_rate" label="每日收益（%）" width="130" />
      <el-table-column prop="cycle_start" label="已开始周期(天)" width="200" />
      <el-table-column prop="cycle_end" label="全部周期(天)" width="200" />
      <el-table-column prop="create_at" label="创建时间" width="130" />
    </template>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";

const searchForm = ref({
  username: "",
  phone: "",
  starttime: "",
  endtime: "",
  order_id: "",
});

onMounted(() => {
  getList();
});

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);

const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/item/getItemLogLists",
    params: {
      ...searchForm.value,
      page: page.value,
      limit: limit.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
