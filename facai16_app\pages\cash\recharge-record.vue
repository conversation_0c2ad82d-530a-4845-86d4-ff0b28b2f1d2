<template>
	<view>
		<view class="flex-page scroll-view page-bg">
			<view>
				<uv-navbar @leftClick="back" fixed placeholder bgColor="none">
					<template #left>
						<view>
							<image src="/static/back2.png" mode="aspectFit" size-48 block></image>
						</view>
					</template>
					<template #center>
						<view class="color-#fff">
							交易记录
						</view>
					</template>

				</uv-navbar>

				<uni-datetime-picker v-model="range" type="daterange" :start="startDate" :end="endDate"
					rangeSeparator="至">

					<view class="fc px-32 py-24 color-#fff t-28 lh-40">
						<text class="mr-10">
							{{range[0]}} 至 {{range[1]}}
						</text>
						<uv-icon name="arrow-down-fill" color="#fff" size="24rpx"></uv-icon>
					</view>


				</uni-datetime-picker>
				<view
					class="mx-32 r-100 border-1 border-solid border-#407CEE bg-#000 bg-op-100 p-8 t-28 lh-40 color flex">
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==0}"
						@click="tabIndex = 0">
						充值记录
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==1}"
						@click="tabIndex = 1">
						提现记录
					</view>
					<view class="flex1 fcc h-68" :class="{'bg-#407CEE color-#fff r-99' : tabIndex ==2}"
						@click="tabIndex = 2">
						转账记录
					</view>
				</view>
				<view class="h-16"></view>
			</view>
			<view class="flex1 f-pr color-#fff t-22 lh-30">
				<scroll-view scroll-y="true" class="scroll-view">
					<view class="p-32 !pt-16">

						<box-box :blue="true">
							<view class="mx-32 py-32 border-b border-b-solid border-white border-op-20"
								v-for="(item,index) in 10">
								<view class="fc-bet">
									<view>
										<view class="t-32 lh-45 fw-600 mb-4">
											提现
										</view>
										<view>
											2024/04/26 07:56
										</view>
									</view>
									<view>
										<view class="t-32 lh-45 fw-600 mb-4 DIN text-right">
											-8USDT
										</view>
										<view>
											奖池余额 1209
										</view>
									</view>
								</view>
							</view>
						</box-box>

					</view>
					<view class="h-20"></view>
					<view class="btn-area"></view>
				</scroll-view>
			</view>

		</view>

		<fixed-kefu></fixed-kefu>
	</view>
</template>

<script>
	import moment from 'moment'
	export default {
		data() {
			return {
				tabIndex: 0,

				range: [],
				startDate: "",
				endDate: "",

			};
		},


		mounted() {

			this.endDate = this.moment().format('YYYY-MM-DD')
			this.startDate = this.moment().subtract(1, 'years').format('YYYY-MM-DD');

			this.range.splice(0, 1, this.moment().subtract(30, 'days').format('YYYY-MM-DD'))
			this.range.splice(1, 1, this.endDate)

		},
		computed: {

		},

		methods: {
			moment,
		},
	}
</script>

<style lang="scss" scoped>
	.page-bg {
		background: url(/static/article/bg.jpg) no-repeat #022044;
		background-size: 100% auto;
	}
</style>