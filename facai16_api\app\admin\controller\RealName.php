<?php
namespace app\admin\controller;

use app\admin\service\RealNameService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 实名列表
 */
class RealName
{

    /**
     * 实名列表
     * @return Json
     */
    public function getRealNameLists(): Json
    {
        $params         = Request::only([
            'starttime',
            'endtime',
            'phone',
            'status'
        ]);

        $RealNameService  = new RealNameService();
        $data             = $RealNameService->getRealNameLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 实名信息
     * @return Json
     */
    public function getRealNameInfo(): Json
    {
        $id              = Request::param('id',0);
        $RealNameService = new RealNameService();
        $data            = $RealNameService->getRealNameInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 更新实名
     * @return Json
     */
    public function updateRealName(): Json
    {
        $params           = Request::only([
            'id'        => 0,
            'status'    => 0,
        ]);

        $RealNameService = new RealNameService();
        $data            = $RealNameService->updateRealName($params['id'],$params['status']);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


}