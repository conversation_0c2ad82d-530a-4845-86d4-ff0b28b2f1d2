<?php
namespace app\admin\controller;

use app\admin\service\TransferService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 转账
 */
class Transfer
{
    /**
     * 转账列表
     * @return Json
     */
    public function getTransferLists(): Json
    {
        $params        = Request::only([
            'starttime'        => '',
            'endtime'          => '',
            'username'         => '',
            'phone'            => ''
        ]);



        $TeamService    = new TransferService();
        $data           = $TeamService->getTransferLog($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }


    /**
     * 转账列表记录
     * @return Json
     */
    public function userTransfer(): Json
    {
        $ids             = Request::param('ids',[]);
        $status          = Request::param('status',0);
        $remark          = Request::param('remark','');

        $TeamService     = new TransferService();
        $data            = $TeamService->userTransfer($ids, $status, $remark);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}