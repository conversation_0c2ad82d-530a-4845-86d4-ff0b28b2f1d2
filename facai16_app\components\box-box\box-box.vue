<template>
	<view>
		<view class="box-box" :class="{'box-blue':blue}">
			<view :style="'min-height: '+ height +'rpx'" class="bd">
				 <slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"box-box",
		props: {
			height:{
				type:[String,Number],
				default:40
			},
			blue:{
				type:Boolean,
				default:false
			}
			
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	
	.box-box{
		
		border-radius: 20rpx;
		border: 1px solid rgba(255,255,255,0.13);
		
	}
	.bd{
		background: rgba(255,255,255,0.1);
		border-radius: 20rpx;
		backdrop-filter: blur(12rpx);
	}
	
	.box-blue{
		
		
		border-radius: 20rpx;
		border: 1px solid rgba(255,255,255,0.4);
		.bd{
			background: rgba(64,124,238,0.4);
			border-radius: 20rpx;
			backdrop-filter: blur(29px);
		}
		
	}
</style>