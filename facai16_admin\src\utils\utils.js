export const fileUpload = async (http, file, fileLoading) => {
  fileLoading = true;
  const res = await http({
    method: "post",
    url: "index/upload",
    data: {
      file: file,
    },
  });
  fileLoading = false;
  
};

export const fileChange = async (file, files) => {
 
};

export const htmlDecodeByRegExp = (str) => {
  var temp = "";
  if (str.length == 0) return "";
  temp = str.replace(/&amp;/g, "&");
  temp = temp.replace(/&lt;/g, "<");
  temp = temp.replace(/&gt;/g, ">");
  temp = temp.replace(/&nbsp;/g, " ");
  temp = temp.replace(/&#39;/g, "'");
  temp = temp.replace(/&quot;/g, '"');
  temp = temp.replace(/↵/g, "");
  return temp;
};
