<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="标题" required>
      <el-input v-model="form.title" clearable />
    </el-form-item>
    <el-form-item label="描述" required>
      <el-input v-model="form.desc" clearable />
    </el-form-item>
    <el-form-item label="文章唯一编码" required>
      <el-input v-model="form.code" clearable />
    </el-form-item>

    <el-form-item
      label="文章类型"
      prop="class_id"
      :rules="[
        {
          required: true,
          message: '请选择文章类型',
          trigger: ['change'],
        },
      ]"
    >
      <el-select v-model="form.class_id" placeholder="文章类型" clearable>
        <el-option
          v-for="item in typesEnum"
          :label="item.title"
          :key="item.title"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      label="图片"
      prop="img"
      :rules="[{ required: true, message: '请上传图片' }]"
    >
      <el-upload
        class="upload-demo"
        style="width: 114px"
        :show-file-list="false"
        drag
        :headers="headers"
        :action="`${proxy.BASE_API_URL}index/upload`"
        :on-success="successUpload"
        :on-error="handleErr"
        :multiple="false"
      >
        <img
          v-if="form.img"
          :src="proxy.IMG_BASE_URL + form.img"
          width="100%"
          class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon
      ></el-upload>
    </el-form-item>
    <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="height: 500px; overflow-y: hidden"
        v-model="form.content"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
      />
    </div>
  </el-form>
</template>

<script setup>
import { rolesEnums } from "@/config/enums";
import {
  onBeforeUnmount,
  nextTick,
  ref,
  shallowRef,
  onMounted,
  getCurrentInstance,
} from "vue";
import fileUploadHoook from "@/hooks/fileUpload";
import { htmlDecodeByRegExp } from "@/utils/utils";
import { getTokenAUTH } from "@/utils/auth";

const form = ref({
  title: "",
  content: "",
  class_id: "",
  code: "",
  desc: "",
  img: "",
});
const props = defineProps(["item"]);

const headers = ref({})
onMounted(() => {
  headers.value['Accept-Token'] = getTokenAUTH()
  nextTick(() => {
    props.item.content = htmlDecodeByRegExp(props.item.content);
    form.value = Object.assign(form.value, props.item);

  });
  getTYpesEnum();
});

const { proxy } = getCurrentInstance();
const typesEnum = ref([]);

const getTYpesEnum = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/Article/getArticleClassLists",
  });
  if (res.code == 0) {
    typesEnum.value = res.data.data;
  }
};

const successUpload = (res) => {
  form.value.img = res.data.url;
};

const handleErr = (err) => {
  if (err.status == 320) {
    form.value.img = JSON.parse(err.message).data.url;
  }
}

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref("<p>hello</p>");
const mode = ref("default");

const toolbarConfig = {};
const editorConfig = {
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024, // 10M
      server: proxy.BASE_API_URL + "index/uploadX",
      headers: {
        "Accept-Token": getTokenAUTH(),
      },
      customInsert(res, insertFn) {
        console.log(res)
        const url = proxy.IMG_BASE_URL + res.data.url;
        const alt = res.data.alt
        const href = res.data.href
        insertFn(url, alt, href);
      },
      onError(file, err, res) {
        console.log(`${file.name} 上传出错`, err, res)
      }
    },
  },
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}

.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/deep/ .el-radio-group {
  width: 220px;
}

.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}

/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
