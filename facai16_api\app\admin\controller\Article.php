<?php
namespace app\admin\controller;

use app\admin\service\ArticleService;
use app\common\utils\Ajax;
use think\facade\Request;
use think\response\Json;

/**
 * 产品
 */
class Article
{

    /**
     * 文章列表
     * @return mixed
     */
    public function getArticleLists(): Json
    {
        $params = Request::only([
            'limit' => 100,
            'title'  => ''
        ]);

        $ArticleService = new ArticleService();
        $data           = $ArticleService->getArticleLists($params);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 文章信息
     * @return Json
     */
    public function getArticleInfo(): Json
    {
        $id             = Request::param('id',0);
        $ArticleService = new ArticleService();
        $data           = $ArticleService->getArticleInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加文章
     * @return Json
     */
    public function addArticle(): Json
    {
        $param          = Request::param();
        $ArticleService = new ArticleService();
        $data           = $ArticleService->addArticle($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新文章
     * @return Json
     */
    public function updateArticle(): Json
    {
        $param          = Request::param();
        $ArticleService = new ArticleService();
        $data           = $ArticleService->updateArticle($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除文章
     * @return Json
     */
    public function deleteArticle(): Json
    {
        $id             = Request::param('id',0);
        $ArticleService = new ArticleService();
        $data           = $ArticleService->deleteArticle($id);
        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }



    /**
     * 文章类型列表
     * @return mixed
     */
    public function getArticleClassLists(): Json
    {
        $ArticleService = new ArticleService();
        $data           = $ArticleService->getArticleClassLists();

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 文章类型信息
     * @return Json
     */
    public function getArticleClassInfo(): Json
    {
        $id             = Request::param('id',0);
        $ArticleService = new ArticleService();
        $data           = $ArticleService->getArticleClassInfo($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 添加文章类型
     * @return Json
     */
    public function addArticleClass(): Json
    {
        $param          = Request::param();
        $ArticleService = new ArticleService();
        $data           = $ArticleService->addArticleClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 更新文章类型
     * @return Json
     */
    public function updateArticleClass(): Json
    {
        $param          = Request::param();
        $ArticleService = new ArticleService();
        $data           = $ArticleService->updateArticleClass($param);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

    /**
     * 删除文章类型
     * @return Json
     */
    public function deleteArticleClass(): Json
    {
        $id             = Request::param('id',0);
        $ArticleService = new ArticleService();
        $data           = $ArticleService->deleteArticleClass($id);

        return Ajax::message($data['code'],$data['msg'],$data['data']);
    }

}