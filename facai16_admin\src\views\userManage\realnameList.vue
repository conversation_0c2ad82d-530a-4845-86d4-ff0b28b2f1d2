<template>
  <adminTable
    ref="adminTableRef"
    :tableLoading="tableLoading"
    :totalList="totalList"
    v-model:page="page"
    v-model:limit="limit"
    v-model:searchForm="searchForm"
    :tableData="tableData"
    @search="getList"
  >
    <template v-slot:form-inline-items>
      <!-- <el-form-item>
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchForm.status" placeholder="审核状态" clearable>
          <el-option
            v-for="item in sfzStatusEnums"
            :label="item.label"
            :value="item.value"
            :key="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.starttime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="开始时间"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" class="text-center">
          <span class="text-gray-500">-</span>
        </el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="searchForm.endtime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="结束时间"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
    </template>
    <template v-slot:table>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="phone" label="手机号码" width="160" />
      <el-table-column prop="sfz_number" label="身份证ID" width="200" />
      <el-table-column prop="sfz_name" label="身份证名字" width="130" />
      <el-table-column label="实名状态" width="100">
        <template #default="scope">
          <span
            :class="{
              green: scope.row.status == 0,
              red: scope.row.status == 2,
            }"
            >{{ getLabelByVal(scope.row.status, sfzStatusEnums) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" width="130" />
      <el-table-column
        label="操作"
        width="200"
        fixed="right"
        v-if="currentUser.role == 0"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            @click="editItem(scope.row)"
            v-permission
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </template>
    <el-dialog v-model="dialogFlag" title="编辑" width="1100">
      <editPop :key="editRow" ref="editFormRef" :item="editRow" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFlag = false">关闭</el-button>
          <el-button type="primary" @click="submitForm"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </adminTable>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { sfzStatusEnums, getLabelByVal } from "@/config/enums";
import EditPop from "./components/realnameList/editPop.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const currentUser = JSON.parse(sessionStorage.getItem("currentUser"));
const searchForm = ref({
  username: "",
  starttime: "",
  endtime: "",
  status: "",
});
const dialogFlag = ref(false);
const editRow = ref({});
const editFormRef = ref(null);
const adminTableRef = ref(null);

onMounted(() => {
  getList();
});

const editItem = (row = {}) => {
  editRow.value = row;
  dialogFlag.value = true;
};

const submitForm = async () => {
  const url = "RealName/updateRealName";
  const data = editFormRef.value.form;

  const res = await proxy.$http({
    method: "post",
    url: url,
    data: {
      sfz_name: data.sfz_name,
      sfz_number: data.sfz_number,
      status: data.status,
      username: data.username,
      id: data.id,
    },
  });

  if (res.code == 0) {
    dialogFlag.value = false;
    getList();
    ElMessage({
      type: "success",
      message: res.msg,
    });
  } else {
    dialogFlag.value = false;
    ElMessage({
      type: "error",
      message: res.msg,
    });
  }
};

const { proxy } = getCurrentInstance();

const page = ref(1);
const limit = ref(10);
const totalList = ref(0);
const tableLoading = ref(false);
const tableData = ref([]);
const getList = async () => {
  tableLoading.value = true;
  const res = await proxy.$http({
    type: "get",
    url: "/RealName/getRealNameLists",
    params: {
      page: page.value,
      limit: limit.value,
      ...searchForm.value,
    },
  });
  tableLoading.value = false;
  if (res.code == 0) {
    tableData.value = res.data.data;
    totalList.value = res.data.total;
  }
};
</script>

<style lang="less" scoped></style>
