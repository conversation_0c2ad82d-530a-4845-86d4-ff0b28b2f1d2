<template>
  <el-form
    label-width="100px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <h4 class="form-title">买家信息</h4>
    <el-form-item label="用户名" v-if="formType !== 'add'" required>
      <el-input v-model="form.username" clearable disabled />
    </el-form-item>
    <el-form-item label="手机" required>
      <el-input v-model="form.phone" :disabled="formType != 'add'" clearable />
    </el-form-item>
    <!-- <el-form-item label="订单号" required>
      <el-input v-model="form.deliver_order_no" clearable />
    </el-form-item> -->
    <!-- <el-form-item label="商品名称"  required>
      <el-input v-model="form.goods_title" :disabled="formType != 'add'" clearable />
    </el-form-item> -->
    <!-- <el-form-item label="姓名"  required>
      <el-input v-model="form.address_name" clearable :disabled="formType != 'add'" />
    </el-form-item> -->
    <!-- <el-form-item label="收货手机号" required>
      <el-input v-model="form.address_phone" :disabled="formType != 'add'" clearable />
    </el-form-item> -->
    <el-form-item label="商品" prop="goods_id">
      <el-select v-model="form.goods_id" placeholder="请选择商品" clearable>
        <el-option
          v-for="item in goodsList"
          :label="item.title"
          :key="item.title"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item label="市区" required>
      <el-input v-model="form.address_city" :disabled="formType != 'add'" clearable />
    </el-form-item> -->
    <!-- <el-form-item label="详细地址" required>
      <el-input v-model="form.address_place" :disabled="formType != 'add'" clearable />
    </el-form-item> -->

    <h4 class="form-title">发货信息</h4>
    <el-form-item label="订单状态" required>
      <el-select v-model="form.status" placeholder="" clearable>
        <el-option
          v-for="item in sendStatusEnums"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="发货公司" required>
      <el-input v-model="form.deliver_title" clearable />
    </el-form-item>
    <el-form-item label="发货单号" required>
      <el-input v-model="form.deliver_order_no" clearable />
    </el-form-item>
    <!-- <el-form-item label="发货时间" required>
      <el-date-picker
        v-model="form.deliver_time"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="发货时间"
        style="width: 100%"
      />
    </el-form-item> -->
  </el-form>
</template>

<script setup>
import { nextTick, onMounted, ref, getCurrentInstance } from "vue";
import { rolesEnums, getLabelByVal, sendStatusEnums } from "@/config/enums";
import moment from "moment";

const form = ref({
  username: "",
  phone: "",
  deliver_order_no: "",
  deliver_title: "",
  address_name: "",
  address_phone: "",
  address_city: "",
  address_place: "",
  status: "",
  goods_id: "",
  deliver_title: "",
  deliver_order_no: "",
  deliver_time: "",
});
const props = defineProps(["item", "formType"]);

onMounted(() => {
  nextTick(() => {
    form.value = Object.assign(form, props.item);
    if (form.value.deliver_time) {
      form.value.deliver_time = moment(new Date(props.item.deliver_time * 1000)).format('YYYY-MM-DD HH:mm:ss')
    }
  });
  getGoodsList();
});

const { proxy } = getCurrentInstance();

const goodsList = ref([]);

const getGoodsList = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "/Goods/getGoodsLists",
    params: {
      limit: 1000000
    }
  });
  if (res.code == 0) {
    goodsList.value = res.data.data;
  }
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
  width: 100%;
}
/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
