<?php
declare (strict_types = 1);

namespace app\common\command;

use app\common\cache\QueueInx;
use app\common\cache\QueueSet;
use app\common\jobs\ItemSettleJob;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;


/**
 * 项目结算
 * Class ItemSettle
 * @package app\command
 */
class ItemSettle extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('ItemSettle')->setDescription('the ItemSettle command');

        $this->timer     = date('YmdH');
    }

    /**
     * 查询数量
     * @var int
     */
    public $select  = 1;

    /**
     * 超时时间
     * @var int
     */
    public $timeout = 1;

    /**
     * 时间
     * @var
     */
    public $timer;

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function execute(Input $input, Output $output)
    {
        // 当前时间
        $startTime = (int) microtime(true);
        $QueueInx  = new QueueInx();
        $QueueSet  = new QueueSet();


        //判断集合是否存在
        $QueueSet->exists('ItemSettle:' . $this->timer);

        //删除其他集合
        $QueueSet->delOtherKey('ItemSettle', $this->timer);

        //队列执行节点id
        $RecIndex  = $QueueInx->get('ItemSettle');

        //索引
        $index     = $RecIndex ?: 0;
        $index     = (int) $index;

        // 尝试获取锁
        while (true)
        {
            // 检查是否超过锁定尝试的超时时间
            if (((int) microtime(true) - $startTime) > $this->timeout)
            {
                $QueueInx->set('ItemSettle', $index);
                break;
            }


            //开始查询id为0 从数据倒序第一个数据开始查询
            if ($index == 0)
            {
                $first = $this->findone();

                if (empty($first))
                {
                    //等待0.1秒
                    usleep( 1 * 100000);
                    continue;
                }

                $data = $this->getlist($first['id'] ?? 0);
            }
            else
            {
                $data = $this->getlist($index);
            }


            //
            if (count($data) < $this->select)
            {
                $this->pushToJob($data);
                $index = 0;
            }
            else
            {
                $last  = $this->pushToJob($data);
                $index = $last;
            }

            // var_dump("最后的id:" . $index);
            //等待0.01秒
            usleep( 1 * 10000);
        }


    }


    /**
     * 查询最新一条
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function findOne(): array
    {
        $time    = time();
        $where   = [];
        $where[] = ['item_status','=',0];
        $where[] = ['next_time','<',$time];

        //每日返息，到期返本
        $info = Db::name('item_order')->where($where)->field('id')->order('id','desc')->find();

        if (empty($info))
        {
            return  [];
        }
        else
        {
            return  $info;
        }
    }

    /**
     * @param int $lastId
     * @return array|Db[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getList(int $lastId): array
    {
        $time    = time();
        $where   = [];
        $where[] = ['item_status','=',0];
        $where[] = ['next_time','<',$time];
        $where[] = ['id','<=',$lastId];

        //每日返息，到期返本
        $data = Db::name('item_order')->where($where)->field('id')->order('id','desc')->limit($this->select)->select();

        if (empty($data))
        {
            return [];
        }
        else
        {
            return $data->toArray();
        }
    }

    /**
     * 推送队列
     * @param array $records
     * @return int|mixed
     */
    public function pushToJob(array $records)
    {

        if (empty($records))
        {
            return 0;
        }

        $QueueSet   = new QueueSet();
        $ids        = [];
        $last       = 0;

        $records    = array_column($records,'id');


        foreach ($records as $id)
        {
            $res = $QueueSet->sIsMember('ItemSettle:' . $this->timer, $id);

            if (empty($res))
            {
                queue(ItemSettleJob::class, $id);

                $QueueSet->sAdd('ItemSettle:' . $this->timer, $id);
            }

            $ids[] = $id;
            $last  = $id;
        }


        return $last;
    }



}
