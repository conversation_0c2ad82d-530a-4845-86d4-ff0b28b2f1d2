<?php
namespace app\admin\service;

use app\common\repository\SmsCodeRepository;
use app\common\utils\Result;

/**
 * 短信
 */
class SmsCodeService
{



    /**
     * 短信列表
     * @return array
     */
    public function getSmsCodeLists(): array
    {
        $SmsCodeRepo = new SmsCodeRepository();
        $data        = $SmsCodeRepo->paginates();

        return Result::success($data);
    }


    /**
     * 获取短信信息
     * @param $id
     * @return array
     */
    public function getSmsCodeInfo($id): array
    {
        $SmsCodeRepo = new SmsCodeRepository();
        $data        = $SmsCodeRepo->findById($id);

        return Result::success($data);
    }

    /**
     * 删除短信
     * @param $id
     * @return array
     */
    public function deleteSmsCode($id): array
    {
        $SmsCodeRepo = new SmsCodeRepository();

        $res        = $SmsCodeRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }



}
