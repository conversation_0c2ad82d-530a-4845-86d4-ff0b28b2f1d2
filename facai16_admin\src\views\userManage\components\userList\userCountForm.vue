<template>
  <el-form
    label-width="140px"
    :inline="true"
    :model="form"
    class="demo-form-inline"
  >
    <el-form-item label="用户名ID" required>
      <el-input v-model="form.id" disabled clearable />
    </el-form-item>
    <el-form-item label="用户名" required>
      <el-input v-model="form.username" disabled clearable />
    </el-form-item>
    <el-form-item label="股权金额" required>
      <el-input v-model="form.shares_money" clearable />
    </el-form-item>
    <el-form-item label="余额宝金额" required>
      <el-input v-model="form.yuebao_money" clearable />
    </el-form-item>
    <el-form-item label="余额宝收益" required>
      <el-input v-model="form.yuebao_earn" clearable />
    </el-form-item>
    <el-form-item label="余额宝结算时间" required>
      <el-input v-model="form.yuebao_time" clearable />
    </el-form-item>
    <el-form-item label="余额宝结算次数" required>
      <el-input v-model="form.yuebao_num" clearable />
    </el-form-item>
    <el-form-item label="累计充值次数" required>
      <el-input v-model="form.recharge_num" clearable />
    </el-form-item>
    <el-form-item label="累计提现额度" required>
      <el-input v-model="form.withdraw_money" clearable />
    </el-form-item>
    <el-form-item label="累计提现次数" required>
      <el-input v-model="form.withdraw_num" clearable />
    </el-form-item>
    <el-form-item label="用户签到额度" required>
      <el-input v-model="form.signin_money" clearable />
    </el-form-item>
    <el-form-item label="用户签到次数" required>
      <el-input v-model="form.signin_num" clearable />
    </el-form-item>
    <el-form-item label="用户抽奖收益" required>
      <el-input v-model="form.raffle_money" clearable />
    </el-form-item>
    <el-form-item label="用户抽奖次数" required>
      <el-input v-model="form.raffle_num" clearable />
    </el-form-item>
    <el-form-item label="团队投资金额" required>
      <el-input v-model="form.team_invite" clearable />
    </el-form-item>
    <el-form-item label="团队投资项目" required>
      <el-input v-model="form.team_item" clearable />
    </el-form-item>
    <el-form-item label="团队投资v1金额" required>
      <el-input v-model="form.team_item_v1" clearable />
    </el-form-item>
    <el-form-item label="团队投资v2金额" required>
      <el-input v-model="form.team_item_v2" clearable />
    </el-form-item>
    <el-form-item label="团队投资v3金额" required>
      <el-input v-model="form.team_item_v3" clearable />
    </el-form-item>
    <el-form-item label="团队返利v1金额" required>
      <el-input v-model="form.team_invite_v1" clearable />
    </el-form-item>
    <el-form-item label="团队返利v2金额" required>
      <el-input v-model="form.team_invite_v2" clearable />
    </el-form-item>
    <el-form-item label="团队返利v3金额" required>
      <el-input v-model="form.team_invite_v3" clearable />
    </el-form-item>
    <el-form-item label="团队返利次数" required>
      <el-input v-model="form.team_invite_num" clearable />
    </el-form-item>
    <el-form-item label="团队人数" required>
      <el-input v-model="form.team_invite_user" clearable />
    </el-form-item>
    <el-form-item label="团队充值金额" required>
      <el-input v-model="form.team_recharge" clearable />
    </el-form-item>
    <el-form-item label="团队充值v1金额" required>
      <el-input v-model="form.team_recharge_v1" clearable />
    </el-form-item>
    <el-form-item label="团队充值v2金额" required>
      <el-input v-model="form.team_recharge_v2" clearable />
    </el-form-item>
    <el-form-item label="团队充值v3金额" required>
      <el-input v-model="form.team_recharge_v3" clearable />
    </el-form-item>
    <el-form-item label="用户总收益" required>
      <el-input v-model="form.income_money" clearable />
    </el-form-item>
    <el-form-item label="用户投资额度" required>
      <el-input v-model="form.invest_money" clearable />
    </el-form-item>
    <el-form-item label="用户在订购中金额" required>
      <el-input v-model="form.invest_not_finish" clearable />
    </el-form-item>
    <el-form-item label="用户总订阅数量" required>
      <el-input v-model="form.invest_num" clearable />
    </el-form-item>
    <el-form-item label="用户积分" required>
      <el-input v-model="form.user_points" clearable />
    </el-form-item>

    <el-form-item label="用户优惠券" required>
      <el-input v-model="form.user_coupon" clearable />
    </el-form-item>

    <el-form-item label="用户奖金次数" required>
      <el-input v-model="form.bonus_num" clearable />
    </el-form-item>

    <el-form-item label="用户奖金收益" required>
      <el-input v-model="form.bonus_money" clearable />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { getCurrentInstance, nextTick, onMounted, ref } from "vue";

const form = ref({
  id: "",
  username: "",
  shares_money: "",
  yuebao_money: "",
  yuebao_earn: "",
  yuebao_time: "",
  yuebao_num: "",
  recharge_num: "",
  withdraw_money: "",
  withdraw_num: "",
  signin_money: "",
  signin_num: "",
  raffle_money: "",
  raffle_num: "",
  team_invite: "",
  team_item: "",
  team_item_v1: "",
  team_item_v2: "",
  team_item_v3: "",
  team_invite_v1: "",
  team_invite_v2: "",
  team_invite_v3: "",
  team_invite_num: "",
  team_invite_user: "",
  team_recharge: "",
  team_recharge_v1: "",
  team_recharge_v2: "",
  team_recharge_v3: "",
  income_money: "",
  invest_money: "",
  invest_not_finish: "",
  invest_num: "",
  user_points: "",
  user_coupon: "",
  bonus_num: "",
  bonus_money: "",
});
const props = defineProps(["item"]);

onMounted(() => {
  nextTick(() => {
    form.value = Object.assign(form.value, props.item);
    getUserState();
  });
});

const { proxy } = getCurrentInstance();
const getUserState = async () => {
  const res = await proxy.$http({
    method: "get",
    url: "user/getUserInfos?id=" + form.value.id,
  });

  if (res.code == 0) {
    form.value = Object.assign(form.value, res.data);
  }
};

defineExpose({ form });
</script>

<style lang="less" scoped>
.demo-form-inline {
  justify-content: flex-start;
  display: flex;
  flex-wrap: wrap;
}
.demo-form-inline .el-input {
  --el-input-width: 260px;
}

.demo-form-inline .el-select {
  --el-select-width: 260px;
}
/deep/ .el-radio-group {
  width: 220px;
}
.form-title {
  text-align: left;
  padding-left: 30px;
  margin: 20px auto 10px;
  height: 44px;
  background-color: #f2f2f2;
  border-radius: 5px;
  line-height: 44px;
}
/deep/ .el-form-item {
  align-items: flex-start;
}
</style>
